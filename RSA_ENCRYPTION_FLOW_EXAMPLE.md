# RSA Encryption Flow Example

## Complete Flow for Transfer Controller

### 1. Client Side (Caller)

```csharp
// Step 1: Create XML payload
var nameEnquiryXml = @"
<NESingleRequest>
    <SessionID>12345</SessionID>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>BELEMA_VA</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>";

// Step 2: Encrypt the entire XML payload
var encryptedPayload = rsaService.Encrypt(nameEnquiryXml);

// Step 3: Send as base64 string
var response = await httpClient.PostAsync("/api/transfers/name-enquiry", 
    new StringContent(encryptedPayload, Encoding.UTF8, "text/plain"));

// Step 4: Decrypt the response
var encryptedResponse = await response.Content.ReadAsStringAsync();
var decryptedResponseXml = rsaService.Decrypt(encryptedResponse);

// Step 5: Deserialize response XML
var responseObject = DeserializeXml<NameEnquiryXmlResponse>(decryptedResponseXml);
```

### 2. Server Side (Clean Controller)

```csharp
[HttpPost("name-enquiry")]
[Consumes("text/plain")]
[Produces("text/plain")]
public async Task<IActionResult> NameEnquiry([FromBody] string encryptedXmlPayload, CancellationToken cancellationToken)
{
    try
    {
        // Decrypt and deserialize the XML request
        var xmlRequest = await encryptedXmlService.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedXmlPayload);
        if (xmlRequest == null)
        {
            logger.LogWarning("Failed to decrypt and deserialize name enquiry request");
            return BadRequest("Invalid encrypted XML payload");
        }

        // Validate XML request
        var validationResult = await xmlValidationService.ValidateAsync(xmlRequest);
        if (!validationResult.IsValid)
        {
            logger.LogWarning("XML validation failed for name enquiry: {Errors}", string.Join(", ", validationResult.Errors));
            var errorResponse = xmlResponseFactory.CreateNameEnquiryValidationErrorResponse(xmlRequest);
            var encryptedErrorResponse = await encryptedXmlService.CreateEncryptedErrorResponseAsync(errorResponse);
            return BadRequest(encryptedErrorResponse);
        }

        // Process name enquiry (focus on business logic)
        var command = new PaymentNameEnquiryCommand
        {
            AccountNumber = xmlRequest.AccountNumber,
            BankCode = xmlRequest.DestinationInstitutionCode
        };

        var result = await paymentGateway.ProcessNameEnquiryAsync(command, cancellationToken);
        var response = xmlResponseFactory.CreateNameEnquirySuccessResponse(xmlRequest, result);

        // Serialize and encrypt the response
        var encryptedResponse = await encryptedXmlService.SerializeAndEncryptAsync(response);

        logger.LogInformation("Successfully processed name enquiry for account {AccountNumber}", xmlRequest.AccountNumber);
        return result.IsSuccessful ? Ok(encryptedResponse) : BadRequest(encryptedResponse);
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error processing encrypted name enquiry");
        return BadRequest("Name enquiry processing failed");
    }
}
```

### 3. Clean Architecture Benefits

- ✅ **Controller is focused**: Only business logic, no encryption details
- ✅ **Single Responsibility**: `IEncryptedXmlService` handles all encryption/decryption
- ✅ **Reusable**: Same service can be used across multiple controllers
- ✅ **Testable**: Easy to mock `IEncryptedXmlService` for unit tests
- ✅ **Maintainable**: Encryption logic is centralized in one place

## Key Points

1. **Input**: Encrypted base64 string containing XML payload
2. **Process**: Decrypt → Deserialize → Validate → Process → Serialize → Encrypt
3. **Output**: Encrypted base64 string containing XML response

## Benefits

- ✅ **End-to-End Encryption**: Entire XML payload is encrypted
- ✅ **Transparent Processing**: Business logic remains unchanged
- ✅ **Secure Communication**: No plain text XML over the wire
- ✅ **Flexible**: Works with any XML structure
- ✅ **Error Handling**: Encrypted error responses maintain security

## Testing

```bash
# 1. Create XML
echo '<NESingleRequest><SessionID>12345</SessionID><DestinationInstitutionCode>999998</DestinationInstitutionCode><ChannelCode>BELEMA_VA</ChannelCode><AccountNumber>**********</AccountNumber></NESingleRequest>' > request.xml

# 2. Encrypt XML (using your RSA service)
# encryptedPayload = rsaService.Encrypt(File.ReadAllText("request.xml"))

# 3. Send encrypted payload
curl -X POST "https://localhost:7001/api/transfers/name-enquiry" \
     -H "Content-Type: text/plain" \
     -d "your-encrypted-base64-payload-here"

# 4. Decrypt response
# decryptedResponse = rsaService.Decrypt(response)
```
