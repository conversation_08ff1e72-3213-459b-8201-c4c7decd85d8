# RSA Encryption Flow Example

## Complete Flow for Transfer Controller

### 1. Client Side (Caller)

```csharp
// Step 1: Create XML payload
var nameEnquiryXml = @"
<NESingleRequest>
    <SessionID>12345</SessionID>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>BELEMA_VA</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>";

// Step 2: Encrypt the entire XML payload
var encryptedPayload = rsaService.Encrypt(nameEnquiryXml);

// Step 3: Send as base64 string
var response = await httpClient.PostAsync("/api/transfers/name-enquiry", 
    new StringContent(encryptedPayload, Encoding.UTF8, "text/plain"));

// Step 4: Decrypt the response
var encryptedResponse = await response.Content.ReadAsStringAsync();
var decryptedResponseXml = rsaService.Decrypt(encryptedResponse);

// Step 5: Deserialize response XML
var responseObject = DeserializeXml<NameEnquiryXmlResponse>(decryptedResponseXml);
```

### 2. Server Side (Your Controller)

```csharp
[HttpPost("name-enquiry")]
[Consumes("text/plain")]
[Produces("text/plain")]
public async Task<IActionResult> NameEnquiry([FromBody] string encryptedXmlPayload, CancellationToken cancellationToken)
{
    try
    {
        // Step 1: Decrypt the base64 encrypted XML payload
        var decryptedXml = rsaService.Decrypt(encryptedXmlPayload);
        
        // Step 2: Deserialize XML to object
        var xmlRequest = DeserializeXml<NameEnquiryXmlRequest>(decryptedXml);
        
        // Step 3: Validate and process
        var validationResult = await xmlValidationService.ValidateAsync(xmlRequest);
        if (!validationResult.IsValid)
        {
            var errorResponse = xmlResponseFactory.CreateNameEnquiryValidationErrorResponse(xmlRequest);
            var errorXml = SerializeToXml(errorResponse);
            var encryptedErrorResponse = rsaService.Encrypt(errorXml);
            return BadRequest(encryptedErrorResponse);
        }

        // Step 4: Process business logic
        var command = new PaymentNameEnquiryCommand
        {
            AccountNumber = xmlRequest.AccountNumber,
            BankCode = xmlRequest.DestinationInstitutionCode
        };

        var result = await paymentGateway.ProcessNameEnquiryAsync(command, cancellationToken);
        var response = xmlResponseFactory.CreateNameEnquirySuccessResponse(xmlRequest, result);

        // Step 5: Serialize response to XML
        var responseXml = SerializeToXml(response);
        
        // Step 6: Encrypt the XML response
        var encryptedResponse = rsaService.Encrypt(responseXml);
        
        return Ok(encryptedResponse);
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error processing encrypted name enquiry");
        return BadRequest("Processing failed");
    }
}
```

## Key Points

1. **Input**: Encrypted base64 string containing XML payload
2. **Process**: Decrypt → Deserialize → Validate → Process → Serialize → Encrypt
3. **Output**: Encrypted base64 string containing XML response

## Benefits

- ✅ **End-to-End Encryption**: Entire XML payload is encrypted
- ✅ **Transparent Processing**: Business logic remains unchanged
- ✅ **Secure Communication**: No plain text XML over the wire
- ✅ **Flexible**: Works with any XML structure
- ✅ **Error Handling**: Encrypted error responses maintain security

## Testing

```bash
# 1. Create XML
echo '<NESingleRequest><SessionID>12345</SessionID><DestinationInstitutionCode>999998</DestinationInstitutionCode><ChannelCode>BELEMA_VA</ChannelCode><AccountNumber>**********</AccountNumber></NESingleRequest>' > request.xml

# 2. Encrypt XML (using your RSA service)
# encryptedPayload = rsaService.Encrypt(File.ReadAllText("request.xml"))

# 3. Send encrypted payload
curl -X POST "https://localhost:7001/api/transfers/name-enquiry" \
     -H "Content-Type: text/plain" \
     -d "your-encrypted-base64-payload-here"

# 4. Decrypt response
# decryptedResponse = rsaService.Decrypt(response)
```
