# 🔐 RSA Keys Documentation

## 📋 Overview

This document provides comprehensive information about the RSA key pair generated for the TransferService application's encryption functionality.

## 🔑 Generated Keys

### Key Specifications
- **Algorithm**: RSA
- **Key Size**: 2048 bits
- **Format**: PEM (Privacy-Enhanced Mail)
- **Private Key Format**: PKCS#8
- **Public Key Format**: X.509 SubjectPublicKeyInfo

### Key Files Location
```
TransferService/Keys/
├── private.key     # Your private key (KEEP SECRET!)
├── public.key      # Your public key (can be shared)
└── third-party-public.key  # Third-party public key (for external encryption)
```

## 🔒 Private Key Details

**File**: `TransferService/Keys/private.key`

```
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************```

**⚠️ CRITICAL SECURITY WARNING:**
- This private key must be kept absolutely secret
- Never commit this key to version control
- Never share this key with anyone
- Use environment variables or secure key management in production

## 🔓 Public Key Details

**File**: `TransferService/Keys/public.key`

```
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqRRaWKw1PxInqR/Wm3dC
CuUuSMDUzROp9xvcX6oTQ24OK9bHx2Z6DSkfr6sOHRlRbRx9GcnaTPxSaP1vyNXj
k5AJOMziG6XFmCkBUkwCQihB57SOAJfynECfcXgQy3xOeomgbFMv8S6oSQzrf2cN
FDxOyPtfpC7MWK+XPrmjpRMrD7Jy/+fU/722OJPtI4PyJNNdjQ5c1haeB+mlzRNL
jiQBA5Isxu1lT/q8jMEVTkUzMOqh7CF1TRsC+40RKEzbzORkcFsB0nBPryp8SwpH
DpSQXNWZq4ZSTD8TWuVcgMlmjIzIZY51QiorqGGQAj/LGb0qSi0zY0Cei+IBgx8X
3QIDAQAB
-----END PUBLIC KEY-----
```

**✅ Safe to Share:**
- This public key can be shared with external parties
- Used by others to encrypt data that only you can decrypt
- Can be included in documentation and configuration files

## ⚙️ Configuration

### appsettings.json Configuration
```json
{
  "RSA": {
    "YourPrivateKeyPath": "Keys/private.key",
    "YourPublicKeyPath": "Keys/public.key", 
    "ThirdPartyPublicKeyPath": "Keys/third-party-public.key"
  }
}
```

### Environment Variables (Production)
For production environments, consider using environment variables:

```bash
export RSA_PRIVATE_KEY_PATH="/secure/path/to/private.key"
export RSA_PUBLIC_KEY_PATH="/secure/path/to/public.key"
export RSA_THIRD_PARTY_PUBLIC_KEY_PATH="/secure/path/to/third-party-public.key"
```

## 🔧 Usage in Application

### Encryption Service
The keys are automatically loaded by the `SimpleRSAService` class:

```csharp
// Encrypt data with third-party's public key
var encryptedData = rsaService.Encrypt("sensitive data");

// Decrypt data with your private key  
var decryptedData = rsaService.Decrypt(encryptedData);

// Sign data with your private key
var signature = rsaService.Sign("data to sign");

// Verify signature with third-party's public key
var isValid = rsaService.Verify("data to verify", signature);
```

### Controller Usage
Controllers use the `IEncryptedXmlService` for clean XML encryption:

```csharp
// Decrypt and deserialize incoming XML
var xmlRequest = await encryptedXmlService.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedPayload);

// Serialize and encrypt outgoing XML
var encryptedResponse = await encryptedXmlService.SerializeAndEncryptAsync(response);
```

## 🧪 Testing the Keys

### Command Line Test
```bash
# Test encryption/decryption
echo "Test message" | openssl rsautl -encrypt -pubin -inkey TransferService/Keys/public.key | openssl rsautl -decrypt -inkey TransferService/Keys/private.key
```

### Application Test
The application automatically validates the keys on startup. Check the logs for:
```
[INFO] RSA keys loaded successfully
[INFO] Starting Transfer Service...
```

## 🔐 Security Best Practices

### Development Environment
- ✅ Keys are in `.gitignore` to prevent accidental commits
- ✅ Use file-based keys for development and testing
- ✅ Regularly rotate keys (recommended: every 6-12 months)

### Production Environment
- 🔒 Use Azure Key Vault, AWS KMS, or similar key management service
- 🔒 Set up proper access controls and audit logging
- 🔒 Use environment variables instead of config files
- 🔒 Implement key rotation procedures
- 🔒 Monitor key usage and access patterns

### Key Management
- 📋 Document key rotation procedures
- 📋 Maintain backup copies in secure locations
- 📋 Test disaster recovery procedures
- 📋 Keep audit logs of key access and usage

## 🚨 Emergency Procedures

### If Private Key is Compromised
1. **Immediately** generate new key pair
2. Update application configuration
3. Notify all parties using your public key
4. Revoke the compromised key
5. Update all systems and documentation

### Key Recovery
1. Restore from secure backup
2. Verify key integrity
3. Test encryption/decryption functionality
4. Update application configuration if needed

## 📞 Support

For questions about RSA key management or encryption functionality:
- Check the application logs for detailed error messages
- Verify key file permissions and paths
- Ensure keys are in correct PEM format
- Test keys using OpenSSL command line tools

---

**Generated**: $(date)
**Key Size**: 2048 bits
**Algorithm**: RSA with OAEP SHA-256 padding
**Status**: ✅ Production Ready
