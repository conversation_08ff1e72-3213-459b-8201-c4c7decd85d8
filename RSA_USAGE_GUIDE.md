# Simple RSA Service Usage Guide

## Overview
This is a simple, clean RSA service that can load your private/public keys and third-party public keys for encryption/decryption operations.

## Configuration

Add the following to your `appsettings.json`:

```json
{
  "RSA": {
    "YourPrivateKeyPath": "keys/your-private.key",
    "YourPublicKeyPath": "keys/your-public.key", 
    "ThirdPartyPublicKeyPath": "keys/third-party-public.key"
  }
}
```

## Key Files

Place your key files in the `keys/` directory:

- `your-private.key` - Your private key in PEM format
- `your-public.key` - Your public key in PEM format  
- `third-party-public.key` - Third party's public key in PEM format

## Usage

### Inject the service in your controller or service:

```csharp
public class MyController : ControllerBase
{
    private readonly IRSAService _rsaService;

    public MyController(IRSAService rsaService)
    {
        _rsaService = rsaService;
    }
}
```

### Encrypt data (using third-party public key):

```csharp
string plainText = "Hello, World!";
string encryptedData = _rsaService.Encrypt(plainText);
```

### Decrypt data (using your private key):

```csharp
string encryptedData = "base64-encrypted-data";
string decryptedText = _rsaService.Decrypt(encryptedData);
```

### Sign data (using your private key):

```csharp
string data = "Data to sign";
string signature = _rsaService.Sign(data);
```

### Verify signature (using third-party public key):

```csharp
string data = "Original data";
string signature = "base64-signature";
bool isValid = _rsaService.VerifySignature(data, signature);
```

## Key Generation

To generate RSA key pairs, you can use OpenSSL:

```bash
# Generate private key
openssl genpkey -algorithm RSA -out your-private.key -pkcs8 -aes256

# Extract public key from private key
openssl rsa -pubout -in your-private.key -out your-public.key
```

## Features

- ✅ Simple and clean interface
- ✅ Loads keys from file system
- ✅ Supports encryption/decryption
- ✅ Supports signing/verification
- ✅ Uses OAEP SHA-256 padding for encryption
- ✅ Uses PKCS1 padding for signatures
- ✅ Proper error handling and logging
- ✅ Implements IDisposable for resource cleanup

## Integration in Controllers

The RSA service has been integrated into your main controllers:

### 1. BelemaEasyTransferController
- **Name Enquiry**: Automatically decrypts account numbers if they appear encrypted and encrypts account names in responses
- **Transfer**: Ready for RSA integration in transfer operations

### 2. XmlTransfersController
- **Test Endpoints**: Added dedicated RSA testing endpoints:
  - `POST /api/xml/transfers/test-encrypt` - Test encryption
  - `POST /api/xml/transfers/test-decrypt` - Test decryption
  - `POST /api/xml/transfers/test-sign` - Test signing
  - `POST /api/xml/transfers/test-verify` - Test signature verification

### 3. EasyPayController
- **All Methods**: RSA service is injected and ready for use in name enquiry, transfers, wallet operations, and TSQ

## Testing the RSA Service

You can test the RSA functionality using the dedicated endpoints:

```bash
# Test encryption
curl -X POST "https://localhost:7001/api/xml/transfers/test-encrypt" \
     -H "Content-Type: text/plain" \
     -d "Hello World"

# Test decryption
curl -X POST "https://localhost:7001/api/xml/transfers/test-decrypt" \
     -H "Content-Type: text/plain" \
     -d "base64-encrypted-data-here"

# Test signing
curl -X POST "https://localhost:7001/api/xml/transfers/test-sign" \
     -H "Content-Type: text/plain" \
     -d "Data to sign"

# Test verification
curl -X POST "https://localhost:7001/api/xml/transfers/test-verify" \
     -H "Content-Type: application/json" \
     -d '{"data": "Original data", "signature": "base64-signature-here"}'
```

## Error Handling

The service will throw exceptions if:
- Key files are not found
- Key files are invalid or corrupted
- Encryption/decryption operations fail
- Signing/verification operations fail

All errors are logged for debugging purposes.

## Next Steps

1. **Add your actual RSA keys** to the `keys/` directory
2. **Test the endpoints** to ensure everything works
3. **Customize the integration** in your controllers as needed
4. **Add RSA operations** to specific business logic where required
