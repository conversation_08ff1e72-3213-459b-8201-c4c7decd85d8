# Simple RSA Service Usage Guide

## Overview
This is a simple, clean RSA service that can load your private/public keys and third-party public keys for encryption/decryption operations.

## Configuration

Add the following to your `appsettings.json`:

```json
{
  "RSA": {
    "YourPrivateKeyPath": "keys/your-private.key",
    "YourPublicKeyPath": "keys/your-public.key", 
    "ThirdPartyPublicKeyPath": "keys/third-party-public.key"
  }
}
```

## Key Files

Place your key files in the `keys/` directory:

- `your-private.key` - Your private key in PEM format
- `your-public.key` - Your public key in PEM format  
- `third-party-public.key` - Third party's public key in PEM format

## Usage

### Inject the service in your controller or service:

```csharp
public class MyController : ControllerBase
{
    private readonly IRSAService _rsaService;

    public MyController(IRSAService rsaService)
    {
        _rsaService = rsaService;
    }
}
```

### Encrypt data (using third-party public key):

```csharp
string plainText = "Hello, World!";
string encryptedData = _rsaService.Encrypt(plainText);
```

### Decrypt data (using your private key):

```csharp
string encryptedData = "base64-encrypted-data";
string decryptedText = _rsaService.Decrypt(encryptedData);
```

### Sign data (using your private key):

```csharp
string data = "Data to sign";
string signature = _rsaService.Sign(data);
```

### Verify signature (using third-party public key):

```csharp
string data = "Original data";
string signature = "base64-signature";
bool isValid = _rsaService.VerifySignature(data, signature);
```

## Key Generation

To generate RSA key pairs, you can use OpenSSL:

```bash
# Generate private key
openssl genpkey -algorithm RSA -out your-private.key -pkcs8 -aes256

# Extract public key from private key
openssl rsa -pubout -in your-private.key -out your-public.key
```

## Features

- ✅ Simple and clean interface
- ✅ Loads keys from file system
- ✅ Supports encryption/decryption
- ✅ Supports signing/verification
- ✅ Uses OAEP SHA-256 padding for encryption
- ✅ Uses PKCS1 padding for signatures
- ✅ Proper error handling and logging
- ✅ Implements IDisposable for resource cleanup

## Error Handling

The service will throw exceptions if:
- Key files are not found
- Key files are invalid or corrupted
- Encryption/decryption operations fail
- Signing/verification operations fail

All errors are logged for debugging purposes.
