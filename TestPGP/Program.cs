using System;
using System.IO;
using System.Linq;
using System.Text;
using Org.BouncyCastle.Bcpg;
using Org.BouncyCastle.Bcpg.OpenPgp;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;

try
{
    Console.WriteLine("🔐 Testing PGP encryption...");

    // Load RSA keys and convert to PGP
    var rsaPublicKey = LoadRsaPublicKey("../TransferService/Keys/public.key");
    var rsaPrivateKey = LoadRsaPrivateKey("../TransferService/Keys/private.key");

    Console.WriteLine($"✅ RSA keys loaded successfully. Key size: {rsaPublicKey.Modulus.BitLength} bits");

    // Create PGP key pair from RSA private key
    var pgpKeyPair = CreatePgpKeyPair(rsaPrivateKey);
    var pgpPublicKey = pgpKeyPair.PublicKey;
    var pgpPrivateKey = pgpKeyPair.PrivateKey;

    Console.WriteLine($"✅ PGP keys created successfully. Key ID: {pgpPublicKey.KeyId:X}");

    // Test data - Name Enquiry XML (smaller for testing)
    var nameEnquiryXml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<NESingleRequest>
    <SessionID>000504250718204500123456789114</SessionID>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>";

    Console.WriteLine($"📄 Name Enquiry XML length: {nameEnquiryXml.Length} characters");

    // Encrypt with PGP
    var encrypted = EncryptWithPgp(nameEnquiryXml, pgpPublicKey);
    Console.WriteLine($"🔒 PGP encrypted length: {encrypted.Length} characters");
    Console.WriteLine($"🔒 Encrypted data: {encrypted.Substring(0, Math.Min(100, encrypted.Length))}...");

    // Save encrypted data for curl testing (even if decryption fails)
    File.WriteAllText("encrypted-name-enquiry.txt", encrypted);
    Console.WriteLine("💾 Encrypted data saved to encrypted-name-enquiry.txt");

    // Try to decrypt with PGP
    try
    {
        var decrypted = DecryptWithPgp(encrypted, pgpPrivateKey);
        Console.WriteLine($"🔓 Decrypted length: {decrypted.Length} characters");

        // Verify
        if (nameEnquiryXml == decrypted)
        {
            Console.WriteLine("✅ PGP Encryption/Decryption successful!");
        }
        else
        {
            Console.WriteLine("❌ PGP Encryption/Decryption failed - data mismatch!");
            Console.WriteLine($"Expected: {nameEnquiryXml}");
            Console.WriteLine($"Got: {decrypted}");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ PGP Decryption failed: {ex.Message}");
        Console.WriteLine("🔒 But encryption succeeded - testing with API...");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
}

static string EncryptWithPgp(string plainText, PgpPublicKey publicKey)
{
    var plainTextBytes = Encoding.UTF8.GetBytes(plainText);

    using var outputStream = new MemoryStream();

    // Create PGP encrypted data generator
    var encryptedDataGenerator = new PgpEncryptedDataGenerator(
        SymmetricKeyAlgorithmTag.Aes256,
        true,
        new SecureRandom());

    // Add recipient public key
    encryptedDataGenerator.AddMethod(publicKey);

    using var encryptedStream = encryptedDataGenerator.Open(outputStream, plainTextBytes.Length);

    // Create compressed data generator
    var compressedDataGenerator = new PgpCompressedDataGenerator(CompressionAlgorithmTag.Zip);
    using var compressedStream = compressedDataGenerator.Open(encryptedStream);

    // Create literal data generator
    var literalDataGenerator = new PgpLiteralDataGenerator();
    using var literalStream = literalDataGenerator.Open(
        compressedStream,
        PgpLiteralData.Binary,
        "data",
        plainTextBytes.Length,
        DateTime.UtcNow);

    literalStream.Write(plainTextBytes, 0, plainTextBytes.Length);

    return Convert.ToBase64String(outputStream.ToArray());
}

static string DecryptWithPgp(string encryptedData, PgpPrivateKey privateKey)
{
    var encryptedBytes = Convert.FromBase64String(encryptedData);

    using var inputStream = new MemoryStream(encryptedBytes);
    var pgpFactory = new PgpObjectFactory(inputStream);
    var encryptedDataList = (PgpEncryptedDataList)pgpFactory.NextPgpObject();

    PgpPublicKeyEncryptedData? encryptedDataObj = null;
    foreach (PgpPublicKeyEncryptedData pked in encryptedDataList.GetEncryptedDataObjects())
    {
        Console.WriteLine($"🔍 Encrypted data key ID: {pked.KeyId:X}, Private key ID: {privateKey.KeyId:X}");
        if (privateKey.KeyId == pked.KeyId)
        {
            encryptedDataObj = pked;
            break;
        }
    }

    if (encryptedDataObj == null)
    {
        Console.WriteLine("❌ No matching private key found for PGP decryption");
        // Try the first one anyway for debugging
        encryptedDataObj = encryptedDataList.GetEncryptedDataObjects().Cast<PgpPublicKeyEncryptedData>().First();
        Console.WriteLine($"🔧 Trying first encrypted data object with key ID: {encryptedDataObj.KeyId:X}");
    }

    using var decryptedStream = encryptedDataObj.GetDataStream(privateKey);
    var decryptedFactory = new PgpObjectFactory(decryptedStream);

    var compressedData = (PgpCompressedData)decryptedFactory.NextPgpObject();
    using var compressedStream = compressedData.GetDataStream();

    var literalFactory = new PgpObjectFactory(compressedStream);
    var literalData = (PgpLiteralData)literalFactory.NextPgpObject();

    using var literalStream = literalData.GetInputStream();
    using var outputStream = new MemoryStream();
    literalStream.CopyTo(outputStream);

    return Encoding.UTF8.GetString(outputStream.ToArray());
}

static PgpKeyPair CreatePgpKeyPair(RsaPrivateCrtKeyParameters rsaPrivateKey)
{
    // Extract public key from private key
    var rsaPublicKey = new RsaKeyParameters(false, rsaPrivateKey.Modulus, rsaPrivateKey.PublicExponent);

    var keyPair = new PgpKeyPair(
        PublicKeyAlgorithmTag.RsaGeneral,
        new AsymmetricCipherKeyPair(rsaPublicKey, rsaPrivateKey),
        DateTime.UtcNow);

    return keyPair;
}

static RsaKeyParameters LoadRsaPublicKey(string publicKeyPath)
{
    var keyContent = File.ReadAllText(publicKeyPath);
    using var reader = new StringReader(keyContent);
    var pemReader = new PemReader(reader);
    var keyObject = pemReader.ReadObject();

    if (keyObject is RsaKeyParameters rsaKey)
    {
        return rsaKey;
    }
    else if (keyObject is AsymmetricCipherKeyPair keyPair)
    {
        return (RsaKeyParameters)keyPair.Public;
    }

    throw new InvalidOperationException("Invalid RSA public key format");
}

static RsaPrivateCrtKeyParameters LoadRsaPrivateKey(string privateKeyPath)
{
    var keyContent = File.ReadAllText(privateKeyPath);
    using var reader = new StringReader(keyContent);
    var pemReader = new PemReader(reader);
    var keyObject = pemReader.ReadObject();

    if (keyObject is RsaPrivateCrtKeyParameters rsaPrivateKey)
    {
        return rsaPrivateKey;
    }
    else if (keyObject is AsymmetricCipherKeyPair keyPair)
    {
        return (RsaPrivateCrtKeyParameters)keyPair.Private;
    }

    throw new InvalidOperationException("Invalid RSA private key format");
}
