﻿using PgpCore;

Console.WriteLine("🔐 Testing PgpCore library...");

try
{
    // Generate PGP keys using PgpCore
    Console.WriteLine("🔑 Generating PGP keys...");

    using var pgp = new PGP();

    // Generate key pair
    await pgp.GenerateKeyAsync(
        new FileInfo("../keys/public.asc"),
        new FileInfo("../keys/private.asc"),
        "<EMAIL>",
        "");

    Console.WriteLine("✅ PGP keys generated successfully!");
    Console.WriteLine($"📁 Public key: ../keys/public.asc");
    Console.WriteLine($"📁 Private key: ../keys/private.asc");

    // Load keys for encryption/decryption
    var publicKeyInfo = new FileInfo("../keys/public.asc");
    var privateKeyInfo = new FileInfo("../keys/private.asc");
    var encryptionKeys = new EncryptionKeys(publicKeyInfo, privateKeyInfo, "");

    using var pgpWithKeys = new PGP(encryptionKeys);

    // Test 1: Name Enquiry
    Console.WriteLine("\n🔍 Testing Name Enquiry...");
    var nameEnquiryXml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<NESingleRequest>
    <SessionID>000504250718204500123456789114</SessionID>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>";

    Console.WriteLine($"📄 Name Enquiry XML length: {nameEnquiryXml.Length} characters");

    // Encrypt Name Enquiry
    var encryptedNameEnquiry = await pgpWithKeys.EncryptAsync(nameEnquiryXml);
    Console.WriteLine($"🔒 Encrypted Name Enquiry length: {encryptedNameEnquiry.Length} characters");

    // Save for API testing
    File.WriteAllText("encrypted-name-enquiry.txt", encryptedNameEnquiry);
    Console.WriteLine("💾 Encrypted Name Enquiry saved to encrypted-name-enquiry.txt");

    // Test 2: Fund Transfer
    Console.WriteLine("\n💸 Testing Fund Transfer...");
    var transferXml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<FTSingleCreditRequest>
    <SessionID>000504250718105353033496566072</SessionID>
    <NameEnquiryRef>000504250718105353033496566072</NameEnquiryRef>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <BeneficiaryAccountName>Ake Mobolaji Temabo</BeneficiaryAccountName>
    <BeneficiaryAccountNumber>**********</BeneficiaryAccountNumber>
    <BeneficiaryBankVerificationNumber>***********</BeneficiaryBankVerificationNumber>
    <BeneficiaryKYCLevel>1</BeneficiaryKYCLevel>
    <OriginatorAccountName>vee Test</OriginatorAccountName>
    <OriginatorAccountNumber>**********</OriginatorAccountNumber>
    <OriginatorBankVerificationNumber>***********</OriginatorBankVerificationNumber>
    <OriginatorKYCLevel>1</OriginatorKYCLevel>
    <TransactionLocation>1.38716,3.05117</TransactionLocation>
    <Narration>Payment from ********** to **********</Narration>
    <PaymentReference>NIPMINI/**********</PaymentReference>
    <Amount>100.00</Amount>
</FTSingleCreditRequest>";

    Console.WriteLine($"📄 Transfer XML length: {transferXml.Length} characters");

    // Encrypt Transfer
    var encryptedTransfer = await pgpWithKeys.EncryptAsync(transferXml);
    Console.WriteLine($"🔒 Encrypted Transfer length: {encryptedTransfer.Length} characters");

    // Save for API testing
    File.WriteAllText("encrypted-transfer.txt", encryptedTransfer);
    Console.WriteLine("💾 Encrypted Transfer saved to encrypted-transfer.txt");

    // Test decryption to verify
    Console.WriteLine("\n🔓 Verifying encryption/decryption...");
    var decryptedNameEnquiry = await pgpWithKeys.DecryptAsync(encryptedNameEnquiry);
    var decryptedTransfer = await pgpWithKeys.DecryptAsync(encryptedTransfer);

    if (nameEnquiryXml == decryptedNameEnquiry && transferXml == decryptedTransfer)
    {
        Console.WriteLine("✅ All PGP Encryption/Decryption tests successful!");
    }
    else
    {
        Console.WriteLine("❌ PGP Encryption/Decryption verification failed!");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
}
