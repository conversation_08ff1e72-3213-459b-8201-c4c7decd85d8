﻿using PgpCore;

Console.WriteLine("🔐 Testing PgpCore library...");

try
{
    // Generate PGP keys using PgpCore
    Console.WriteLine("🔑 Generating PGP keys...");

    using var pgp = new PGP();

    // Generate key pair
    await pgp.GenerateKeyAsync(
        new FileInfo("../keys/public.asc"),
        new FileInfo("../keys/private.asc"),
        "<EMAIL>",
        "");

    Console.WriteLine("✅ PGP keys generated successfully!");
    Console.WriteLine($"📁 Public key: ../keys/public.asc");
    Console.WriteLine($"📁 Private key: ../keys/private.asc");

    // Test encryption/decryption
    var testData = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<NESingleRequest>
    <SessionID>000504250718204500123456789114</SessionID>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>";

    Console.WriteLine($"📄 Test data length: {testData.Length} characters");

    // Load keys for encryption/decryption
    var publicKeyInfo = new FileInfo("../keys/public.asc");
    var privateKeyInfo = new FileInfo("../keys/private.asc");
    var encryptionKeys = new EncryptionKeys(publicKeyInfo, privateKeyInfo, "");

    using var pgpWithKeys = new PGP(encryptionKeys);

    // Encrypt
    var encrypted = await pgpWithKeys.EncryptAsync(testData);
    Console.WriteLine($"🔒 Encrypted length: {encrypted.Length} characters");
    Console.WriteLine($"🔒 Encrypted data: {encrypted.Substring(0, Math.Min(100, encrypted.Length))}...");

    // Decrypt
    var decrypted = await pgpWithKeys.DecryptAsync(encrypted);
    Console.WriteLine($"🔓 Decrypted length: {decrypted.Length} characters");

    // Verify
    if (testData == decrypted)
    {
        Console.WriteLine("✅ PgpCore Encryption/Decryption successful!");

        // Save encrypted data for API testing
        File.WriteAllText("encrypted-test.txt", encrypted);
        Console.WriteLine("💾 Encrypted data saved to encrypted-test.txt");
    }
    else
    {
        Console.WriteLine("❌ PgpCore Encryption/Decryption failed - data mismatch!");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
}
