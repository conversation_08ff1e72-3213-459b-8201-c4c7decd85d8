using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using TransferService.Application.Configuration;

namespace TransferService.Application.Attributes;

/// <summary>
/// Attribute to enable RSA encryption for controllers or actions
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public class RSAEncryptedAttribute : Attribute, IActionFilter
{
    /// <summary>
    /// Key identifier to use for this endpoint
    /// </summary>
    public string? KeyIdentifier { get; set; }

    /// <summary>
    /// Encryption mode for this endpoint
    /// </summary>
    public RSAEncryptionMode EncryptionMode { get; set; } = RSAEncryptionMode.RequestResponse;

    /// <summary>
    /// Serialization format for object encryption
    /// </summary>
    public SerializationFormat SerializationFormat { get; set; } = SerializationFormat.Json;

    /// <summary>
    /// Content types that should be encrypted
    /// </summary>
    public string[] EncryptedContentTypes { get; set; } = { "application/json", "application/xml" };

    /// <summary>
    /// HTTP methods that should be encrypted
    /// </summary>
    public string[] EncryptedMethods { get; set; } = { "POST", "PUT", "PATCH" };

    /// <summary>
    /// Whether to bypass encryption for health checks
    /// </summary>
    public bool BypassHealthChecks { get; set; } = true;

    /// <summary>
    /// Custom error message for encryption failures
    /// </summary>
    public string? EncryptionErrorMessage { get; set; }

    /// <summary>
    /// Custom error message for decryption failures
    /// </summary>
    public string? DecryptionErrorMessage { get; set; }

    public void OnActionExecuting(ActionExecutingContext context)
    {
        // This will be handled by the middleware
        // The attribute serves as a marker and configuration
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // This will be handled by the middleware
        // The attribute serves as a marker and configuration
    }
}

/// <summary>
/// Attribute to enable RSA decryption for incoming requests
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public class RSADecryptedAttribute : Attribute, IActionFilter
{
    /// <summary>
    /// Key identifier to use for decryption
    /// </summary>
    public string? KeyIdentifier { get; set; }

    /// <summary>
    /// Serialization format for object decryption
    /// </summary>
    public SerializationFormat SerializationFormat { get; set; } = SerializationFormat.Json;

    /// <summary>
    /// Whether to bypass decryption for health checks
    /// </summary>
    public bool BypassHealthChecks { get; set; } = true;

    /// <summary>
    /// Custom error message for decryption failures
    /// </summary>
    public string? DecryptionErrorMessage { get; set; }

    public void OnActionExecuting(ActionExecutingContext context)
    {
        // This will be handled by the middleware
        // The attribute serves as a marker and configuration
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // This will be handled by the middleware
        // The attribute serves as a marker and configuration
    }
}

/// <summary>
/// Attribute to enable RSA encryption for outgoing responses
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public class RSAEncryptResponseAttribute : Attribute, IActionFilter
{
    /// <summary>
    /// Key identifier to use for encryption
    /// </summary>
    public string? KeyIdentifier { get; set; }

    /// <summary>
    /// Serialization format for object encryption
    /// </summary>
    public SerializationFormat SerializationFormat { get; set; } = SerializationFormat.Json;

    /// <summary>
    /// Content types that should be encrypted
    /// </summary>
    public string[] EncryptedContentTypes { get; set; } = { "application/json", "application/xml" };

    /// <summary>
    /// Whether to bypass encryption for health checks
    /// </summary>
    public bool BypassHealthChecks { get; set; } = true;

    /// <summary>
    /// Custom error message for encryption failures
    /// </summary>
    public string? EncryptionErrorMessage { get; set; }

    public void OnActionExecuting(ActionExecutingContext context)
    {
        // This will be handled by the middleware
        // The attribute serves as a marker and configuration
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // This will be handled by the middleware
        // The attribute serves as a marker and configuration
    }
}

/// <summary>
/// Attribute to bypass RSA encryption for specific endpoints
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public class RSABypassAttribute : Attribute, IActionFilter
{
    /// <summary>
    /// Reason for bypassing encryption
    /// </summary>
    public string? Reason { get; set; }

    public void OnActionExecuting(ActionExecutingContext context)
    {
        // This will be handled by the middleware
        // The attribute serves as a marker and configuration
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // This will be handled by the middleware
        // The attribute serves as a marker and configuration
    }
} 