using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using TransferService.Application.Services.Interfaces;

namespace TransferService.Application.Common.Extensions;

/// <summary>
/// Extension methods for automatic service registration
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Automatically registers all payment result mappers from the assembly
    /// </summary>
    public static IServiceCollection AddPaymentResultMappers(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();
        
        // Find all types that implement IPaymentResultMapper<T>
        var mapperTypes = assembly.GetTypes()
            .Where(type => type.IsClass && !type.IsAbstract)
            .Where(type => type.GetInterfaces()
                .Any(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IPaymentResultMapper<>)))
            .ToList();

        foreach (var mapperType in mapperTypes)
        {
            // Register each mapper as its concrete type (for direct injection)
            services.AddScoped(mapperType);
            
            // Also register as all its IPaymentResultMapper<T> interfaces
            var mapperInterfaces = mapperType.GetInterfaces()
                .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IPaymentResultMapper<>));
            
            foreach (var mapperInterface in mapperInterfaces)
            {
                services.AddScoped(mapperInterface, provider => provider.GetRequiredService(mapperType));
            }
        }

        return services;
    }

    /// <summary>
    /// Automatically registers all payment provider handlers from the assembly
    /// </summary>
    public static IServiceCollection AddPaymentProviderHandlers(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();
        
        // Find all types that implement IPaymentProviderHandler
        var handlerTypes = assembly.GetTypes()
            .Where(type => type.IsClass && !type.IsAbstract)
            .Where(type => typeof(IPaymentProviderHandler).IsAssignableFrom(type))
            .ToList();

        foreach (var handlerType in handlerTypes)
        {
            services.AddScoped<IPaymentProviderHandler>(provider => 
                (IPaymentProviderHandler)provider.GetRequiredService(handlerType));
            services.AddScoped(handlerType);
        }

        return services;
    }

    /// <summary>
    /// Automatically registers all validators from the assembly
    /// </summary>
    public static IServiceCollection AddFluentValidators(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();
        
        // Find all validator types
        var validatorTypes = assembly.GetTypes()
            .Where(type => type.IsClass && !type.IsAbstract)
            .Where(type => type.BaseType?.IsGenericType == true && 
                          type.BaseType.GetGenericTypeDefinition() == typeof(FluentValidation.AbstractValidator<>))
            .ToList();

        foreach (var validatorType in validatorTypes)
        {
            // Get the type being validated
            var validatedType = validatorType.BaseType!.GetGenericArguments()[0];
            var validatorInterface = typeof(FluentValidation.IValidator<>).MakeGenericType(validatedType);
            
            services.AddScoped(validatorInterface, validatorType);
            services.AddScoped(validatorType);
        }

        return services;
    }
}
