using Microsoft.AspNetCore.Mvc.Formatters;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using Microsoft.Net.Http.Headers;

namespace TransferService.Application.Common.Formatters;

/// <summary>
/// Custom XML input formatter that safely handles XML with special characters
/// </summary>
public class XmlSafeInputFormatter : TextInputFormatter
{
    public XmlSafeInputFormatter()
    {
        SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("application/xml"));
        SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("text/xml"));
        
        SupportedEncodings.Add(Encoding.UTF8);
        SupportedEncodings.Add(Encoding.Unicode);
    }

    public override async Task<InputFormatterResult> ReadRequestBodyAsync(
        InputFormatterContext context, 
        Encoding encoding)
    {
        if (context == null)
        {
            throw new ArgumentNullException(nameof(context));
        }

        var request = context.HttpContext.Request;
        
        try
        {
            using var reader = new StreamReader(request.Body, encoding);
            var xmlContent = await reader.ReadToEndAsync();

            if (string.IsNullOrWhiteSpace(xmlContent))
            {
                return await InputFormatterResult.FailureAsync();
            }

            // Pre-process the XML to handle special characters safely
            var processedXml = PreprocessXml(xmlContent);

            // Deserialize the processed XML
            var serializer = new XmlSerializer(context.ModelType);
            using var stringReader = new StringReader(processedXml);
            using var xmlReader = XmlReader.Create(stringReader, new XmlReaderSettings
            {
                IgnoreWhitespace = true,
                IgnoreComments = true,
                DtdProcessing = DtdProcessing.Prohibit,
                XmlResolver = null
            });

            var result = serializer.Deserialize(xmlReader);
            
            if (result != null)
            {
                return await InputFormatterResult.SuccessAsync(result);
            }
            else
            {
                return await InputFormatterResult.FailureAsync();
            }
        }
        catch (Exception ex)
        {
            context.ModelState.TryAddModelError(string.Empty, 
                $"Failed to deserialize XML: {ex.Message}");
            return await InputFormatterResult.FailureAsync();
        }
    }

    protected override bool CanReadType(Type type)
    {
        // Only handle types in our models namespace
        return type.Namespace?.Contains("TransferService.Application.Features.Transfers.Models") == true;
    }

    /// <summary>
    /// Pre-processes XML content to handle special characters safely
    /// </summary>
    private static string PreprocessXml(string xmlContent)
    {
        if (string.IsNullOrEmpty(xmlContent))
            return xmlContent;

        try
        {
            // Try to parse the XML as-is first
            var doc = new XmlDocument();
            doc.LoadXml(xmlContent);
            
            // If successful, return the content as-is since it's already well-formed
            return xmlContent;
        }
        catch (XmlException)
        {
            // If XML parsing fails, try to fix common issues
            return FixCommonXmlIssues(xmlContent);
        }
    }

    /// <summary>
    /// Attempts to fix common XML issues like unescaped ampersands
    /// </summary>
    private static string FixCommonXmlIssues(string xmlContent)
    {
        var result = xmlContent;
        
        // Replace unescaped & that are not part of valid XML entities
        // This regex matches & that are not followed by valid entity names
        var pattern = @"&(?!(?:amp|lt|gt|quot|apos|#\d+|#x[0-9a-fA-F]+);)";
        result = System.Text.RegularExpressions.Regex.Replace(result, pattern, "&amp;");
        
        // Handle other common issues if needed
        // For example, unescaped < and > in text content
        
        return result;
    }
}
