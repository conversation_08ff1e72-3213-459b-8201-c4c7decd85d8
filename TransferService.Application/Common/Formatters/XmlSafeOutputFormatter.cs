using Microsoft.AspNetCore.Mvc.Formatters;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using Microsoft.Net.Http.Headers;

namespace TransferService.Application.Common.Formatters;

/// <summary>
/// Custom XML output formatter that prevents double-escaping of XML entities
/// </summary>
public class XmlSafeOutputFormatter : TextOutputFormatter
{
    public XmlSafeOutputFormatter()
    {
        SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("application/xml"));
        SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("text/xml"));
        
        SupportedEncodings.Add(Encoding.UTF8);
        SupportedEncodings.Add(Encoding.Unicode);
    }

    public override async Task WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
    {
        if (context == null)
        {
            throw new ArgumentNullException(nameof(context));
        }

        var response = context.HttpContext.Response;
        
        try
        {
            // Serialize the object to XML
            var serializer = new XmlSerializer(context.Object.GetType());
            
            using var stringWriter = new StringWriter();
            using var xmlWriter = XmlWriter.Create(stringWriter, new XmlWriterSettings
            {
                Indent = false,
                OmitXmlDeclaration = false,
                Encoding = selectedEncoding,
                CheckCharacters = false // This prevents double-escaping
            });

            serializer.Serialize(xmlWriter, context.Object);
            xmlWriter.Flush();
            
            var xmlContent = stringWriter.ToString();
            
            // Post-process to fix any double-escaping issues
            var processedXml = FixDoubleEscaping(xmlContent);
            
            var bytes = selectedEncoding.GetBytes(processedXml);
            await response.Body.WriteAsync(bytes, 0, bytes.Length);
        }
        catch (Exception ex)
        {
            // Log the error and fall back to default behavior
            var errorMessage = $"Error in XML serialization: {ex.Message}";
            var errorBytes = selectedEncoding.GetBytes(errorMessage);
            await response.Body.WriteAsync(errorBytes, 0, errorBytes.Length);
        }
    }

    protected override bool CanWriteType(Type type)
    {
        // Handle types in our models namespace
        return type?.Namespace?.Contains("TransferService.Application.Features.Transfers.Models") == true;
    }

    /// <summary>
    /// Fixes double-escaping issues and returns unescaped ampersands
    /// </summary>
    private static string FixDoubleEscaping(string xmlContent)
    {
        if (string.IsNullOrEmpty(xmlContent))
            return xmlContent;

        // Fix common double-escaping patterns and return unescaped ampersands
        var result = xmlContent;

        // Fix &amp;amp; back to &amp; and then to &
        result = result.Replace("&amp;amp;", "&amp;");

        // Convert &amp; to & (unescaped ampersand)
        result = result.Replace("&amp;", "&");

        // Fix other double-escaping patterns
        result = result.Replace("&amp;lt;", "&lt;");
        result = result.Replace("&amp;gt;", "&gt;");
        result = result.Replace("&amp;quot;", "&quot;");
        result = result.Replace("&amp;apos;", "&apos;");

        return result;
    }
}
