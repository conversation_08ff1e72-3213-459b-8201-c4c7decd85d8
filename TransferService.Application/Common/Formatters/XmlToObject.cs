using System.Xml;
using System.Xml.Serialization;

namespace TransferService.Application.Common.Formatters;

public class XmlToObject<T> where T : class
{
    /// <summary>
    /// Convert xml from xml http content to object
    /// </summary>
    /// <param name="content"></param>
    /// <returns></returns>
    public static T? Convert(HttpContent content)
    {
        var xmlSerializer = new XmlSerializer(typeof(T));
        using XmlReader reader = new XmlTextReader(content.ReadAsStream());
        return xmlSerializer.Deserialize(reader) as T;
    }
    /// <summary>
    /// convert string xml to object
    /// </summary>
    /// <param name="xml"></param>
    /// <returns></returns>
    public static T? Convert(string xml)
    {
        var xmlSerializer = new XmlSerializer(typeof(T));
        using StringReader stringReader = new StringReader(xml);
        using XmlReader reader = new XmlTextReader(stringReader);
        return xmlSerializer.Deserialize(reader) as T;
    }
}