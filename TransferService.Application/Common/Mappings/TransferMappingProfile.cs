using AutoMapper;
using TransferService.Domain.Entities;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Features.Transfers.Commands;

namespace TransferService.Application.Common.Mappings;

public class TransferMappingProfile : Profile
{
    public TransferMappingProfile()
    {
        // Domain to DTO mappings
        CreateMap<TransferRequest, TransferDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.FromAccount, opt => opt.MapFrom(src => src.FromAccount))
            .ForMember(dest => dest.ToAccount, opt => opt.MapFrom(src => src.ToAccount))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Amount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.ExternalTransactionId, opt => opt.MapFrom(src => src.ExternalTransactionId))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.ProcessedAt, opt => opt.MapFrom(src => src.ProcessedAt));

        // Command to Domain mappings
        CreateMap<CreateTransferCommand, TransferRequest>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.Status, opt => opt.Ignore())
            .ForMember(dest => dest.ExternalTransactionId, opt => opt.Ignore())
            .ForMember(dest => dest.ErrorMessage, opt => opt.Ignore())
            .ForMember(dest => dest.ProcessedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore());

        // Request DTO to Command mappings
        CreateMap<CreateTransferRequest, CreateTransferCommand>();
    }
}
