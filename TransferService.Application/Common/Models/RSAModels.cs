namespace TransferService.Application.Common.Models;

/// <summary>
/// Result wrapper for RSA operations with success/failure indication
/// </summary>
/// <typeparam name="T">Type of the result data</typeparam>
public class RSAOperationResult<T>
{
    public bool IsSuccess { get; init; }
    public T? Data { get; init; }
    public string? ErrorMessage { get; init; }
    public string? ErrorCode { get; init; }
    public Exception? Exception { get; init; }

    public static RSAOperationResult<T> Success(T data)
    {
        return new RSAOperationResult<T>
        {
            IsSuccess = true,
            Data = data
        };
    }

    public static RSAOperationResult<T> Failure(string errorMessage, string? errorCode = null, Exception? exception = null)
    {
        return new RSAOperationResult<T>
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode,
            Exception = exception
        };
    }
}

/// <summary>
/// Information about an RSA key pair
/// </summary>
public class RSAKeyPairInfo
{
    public string KeyIdentifier { get; init; } = string.Empty;
    public string PublicKeyPem { get; init; } = string.Empty;
    public string PrivateKeyPem { get; init; } = string.Empty;
    public int KeySize { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? ExpiresAt { get; init; }
    public bool IsActive { get; init; }
}

/// <summary>
/// Health status information for RSA service
/// </summary>
public class RSAHealthStatus
{
    public bool IsHealthy { get; init; }
    public string Status { get; init; } = string.Empty;
    public Dictionary<string, object> Details { get; init; } = new();
    public DateTime CheckedAt { get; init; }
    public TimeSpan ResponseTime { get; init; }
}

/// <summary>
/// RSA encryption context for operation tracking
/// </summary>
public class RSAEncryptionContext
{
    public string OperationId { get; init; } = Guid.NewGuid().ToString();
    public string? KeyIdentifier { get; init; }
    public SerializationFormat SerializationFormat { get; init; }
    public DateTime StartTime { get; init; } = DateTime.UtcNow;
    public string? ContentType { get; init; }
    public int DataSize { get; init; }
}

/// <summary>
/// Configuration for RSA encryption operations
/// </summary>
public class RSAEncryptionOptions
{
    public string? DefaultKeyIdentifier { get; init; }
    public int DefaultKeySize { get; init; } = 2048;
    public bool EnableKeyRotation { get; init; } = false;
    public TimeSpan KeyRotationInterval { get; init; } = TimeSpan.FromDays(90);
    public bool EnablePerformanceLogging { get; init; } = true;
    public bool EnableDetailedErrorLogging { get; init; } = true;
    public int MaxDataSizeBytes { get; init; } = 245; // For 2048-bit key with OAEP padding
    public bool EnableHealthChecks { get; init; } = true;
    public bool AutoLoadKeys { get; init; } = false;
}
