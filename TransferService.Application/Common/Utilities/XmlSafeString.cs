using System.Xml;
using System.Xml.Schema;
using System.Xml.Serialization;
using System.Web;

namespace TransferService.Application.Common.Utilities;

/// <summary>
/// A wrapper for strings that automatically handles XML escaping/unescaping during serialization
/// </summary>
public class XmlSafeString : IXmlSerializable
{
    private string _value = string.Empty;

    public XmlSafeString() { }

    public XmlSafeString(string value)
    {
        _value = value ?? string.Empty;
    }

    public string Value
    {
        get => _value;
        set => _value = value ?? string.Empty;
    }

    public static implicit operator string(XmlSafeString xmlSafeString)
    {
        return xmlSafeString?._value ?? string.Empty;
    }

    public static implicit operator XmlSafeString(string value)
    {
        return new XmlSafeString(value);
    }

    public XmlSchema? GetSchema()
    {
        return null;
    }

    public void ReadXml(XmlReader reader)
    {
        if (reader.IsEmptyElement)
        {
            _value = string.Empty;
            reader.ReadStartElement();
        }
        else
        {
            reader.ReadStartElement();
            _value = HttpUtility.HtmlDecode(reader.ReadContentAsString());
            reader.ReadEndElement();
        }
    }

    public void WriteXml(XmlWriter writer)
    {
        var escapedValue = HttpUtility.HtmlEncode(_value);
        writer.WriteString(escapedValue);
    }

    public override string ToString()
    {
        return _value;
    }

    public override bool Equals(object? obj)
    {
        if (obj is XmlSafeString other)
            return _value == other._value;
        if (obj is string str)
            return _value == str;
        return false;
    }

    public override int GetHashCode()
    {
        return _value.GetHashCode();
    }
}

/// <summary>
/// Extension methods for XML safe string handling
/// </summary>
public static class XmlStringExtensions
{
    /// <summary>
    /// Escapes XML special characters in a string
    /// </summary>
    public static string EscapeXml(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return HttpUtility.HtmlEncode(input);
    }

    /// <summary>
    /// Unescapes XML special characters in a string
    /// </summary>
    public static string UnescapeXml(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return HttpUtility.HtmlDecode(input);
    }

    /// <summary>
    /// Safely sets a string value with automatic XML escaping
    /// </summary>
    public static string SetXmlSafe(this string input)
    {
        return input.EscapeXml();
    }

    /// <summary>
    /// Safely gets a string value with automatic XML unescaping
    /// </summary>
    public static string GetXmlSafe(this string input)
    {
        return input.UnescapeXml();
    }
}
