namespace TransferService.Application.Configuration;

/// <summary>
/// YARP-style payment gateway configuration
/// </summary>
public class PaymentGatewayOptions
{
    public string LoadBalancingPolicy { get; set; } = "Priority";
    public HealthCheckOptions HealthCheck { get; set; } = new();
    public Dictionary<string, PaymentDestination> Destinations { get; set; } = new();
}

/// <summary>
/// Health check configuration
/// </summary>
public class HealthCheckOptions
{
    public bool Enabled { get; set; } = true;
    public TimeSpan Interval { get; set; } = TimeSpan.FromSeconds(30);
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(5);
    public int FailureThreshold { get; set; } = 3;
}

/// <summary>
/// Payment destination configuration
/// </summary>
public class PaymentDestination
{
    public bool Enabled { get; set; } = true;
    public int Priority { get; set; } = 1;
    public int Weight { get; set; } = 100;
    public string HealthEndpoint { get; set; } = "/health";
    public Dictionary<string, string> Metadata { get; set; } = new();
    public string Provider => Metadata.GetValueOrDefault("provider", "unknown");
}
