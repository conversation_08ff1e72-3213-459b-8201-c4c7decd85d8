using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Options;

namespace TransferService.Application.Configuration;

/// <summary>
/// Validator for RSA configuration options
/// </summary>
public class RSAConfigurationValidator : IValidateOptions<RSAOptions>
{
    public ValidateOptionsResult Validate(string? name, RSAOptions options)
    {
        var errors = new List<string>();

        // Validate encryption settings
        if (options.Encryption != null)
        {
            if (options.Encryption.DefaultKeySize != 2048 && 
                options.Encryption.DefaultKeySize != 3072 && 
                options.Encryption.DefaultKeySize != 4096)
            {
                errors.Add("DefaultKeySize must be 2048, 3072, or 4096 bits");
            }

            if (options.Encryption.MaxDataSizeBytes <= 0)
            {
                errors.Add("MaxDataSizeBytes must be greater than 0");
            }

            if (string.IsNullOrEmpty(options.Encryption.DefaultKeyIdentifier))
            {
                errors.Add("DefaultKeyIdentifier cannot be null or empty");
            }
        }

        // Validate key management settings
        if (options.KeyManagement != null)
        {
            if (options.KeyManagement.MaxKeysInRotation < 1 || options.KeyManagement.MaxKeysInRotation > 10)
            {
                errors.Add("MaxKeysInRotation must be between 1 and 10");
            }

            if (options.KeyManagement.RotationInterval <= TimeSpan.Zero)
            {
                errors.Add("RotationInterval must be greater than zero");
            }

            // Validate storage configuration
            if (options.KeyManagement.Storage != null)
            {
                ValidateStorageConfiguration(options.KeyManagement.Storage, errors);
            }

            // Validate backup configuration
            if (options.KeyManagement.Backup != null)
            {
                ValidateBackupConfiguration(options.KeyManagement.Backup, errors);
            }
        }

        // Validate endpoint configurations
        if (options.Endpoints != null)
        {
            foreach (var endpoint in options.Endpoints)
            {
                ValidateEndpointConfiguration(endpoint.Key, endpoint.Value, errors);
            }
        }

        // Validate middleware configuration
        if (options.Middleware != null)
        {
            if (options.Middleware.Order < 0)
            {
                errors.Add("Middleware Order must be non-negative");
            }
        }

        // Validate monitoring configuration
        if (options.Monitoring != null)
        {
            if (options.Monitoring.PerformanceWarningThreshold <= 0)
            {
                errors.Add("PerformanceWarningThreshold must be greater than 0");
            }

            if (options.Monitoring.PerformanceErrorThreshold <= 0)
            {
                errors.Add("PerformanceErrorThreshold must be greater than 0");
            }

            if (options.Monitoring.PerformanceErrorThreshold <= options.Monitoring.PerformanceWarningThreshold)
            {
                errors.Add("PerformanceErrorThreshold must be greater than PerformanceWarningThreshold");
            }

            if (options.Monitoring.HealthCheckInterval <= TimeSpan.Zero)
            {
                errors.Add("HealthCheckInterval must be greater than zero");
            }
        }

        return errors.Count > 0 
            ? ValidateOptionsResult.Fail(errors) 
            : ValidateOptionsResult.Success;
    }

    private void ValidateStorageConfiguration(RSAKeyStorageOptions storage, List<string> errors)
    {
        switch (storage.StorageType)
        {
            case RSAKeyStorageType.File:
                if (string.IsNullOrEmpty(storage.FilePath))
                {
                    errors.Add("FilePath is required when StorageType is File");
                }
                break;

            case RSAKeyStorageType.Database:
                if (string.IsNullOrEmpty(storage.DatabaseConnectionString))
                {
                    errors.Add("DatabaseConnectionString is required when StorageType is Database");
                }
                break;

            case RSAKeyStorageType.AzureKeyVault:
                if (storage.KeyVault == null)
                {
                    errors.Add("KeyVault configuration is required when StorageType is AzureKeyVault");
                }
                else
                {
                    ValidateKeyVaultConfiguration(storage.KeyVault, errors);
                }
                break;
        }
    }

    private void ValidateKeyVaultConfiguration(RSAKeyVaultOptions keyVault, List<string> errors)
    {
        if (string.IsNullOrEmpty(keyVault.VaultUrl))
        {
            errors.Add("KeyVault VaultUrl is required");
        }

        if (string.IsNullOrEmpty(keyVault.TenantId))
        {
            errors.Add("KeyVault TenantId is required");
        }

        if (string.IsNullOrEmpty(keyVault.ClientId))
        {
            errors.Add("KeyVault ClientId is required");
        }

        if (string.IsNullOrEmpty(keyVault.ClientSecret))
        {
            errors.Add("KeyVault ClientSecret is required");
        }

        if (string.IsNullOrEmpty(keyVault.KeyNamePrefix))
        {
            errors.Add("KeyVault KeyNamePrefix is required");
        }
    }

    private void ValidateBackupConfiguration(RSAKeyBackupOptions backup, List<string> errors)
    {
        if (backup.Enabled)
        {
            if (string.IsNullOrEmpty(backup.BackupLocation))
            {
                errors.Add("BackupLocation is required when backup is enabled");
            }

            if (backup.RetentionCount < 1 || backup.RetentionCount > 100)
            {
                errors.Add("Backup RetentionCount must be between 1 and 100");
            }

            if (backup.BackupFrequency <= TimeSpan.Zero)
            {
                errors.Add("Backup BackupFrequency must be greater than zero");
            }
        }
    }

    private void ValidateEndpointConfiguration(string endpointPath, RSAEndpointOptions endpoint, List<string> errors)
    {
        if (string.IsNullOrEmpty(endpointPath))
        {
            errors.Add("Endpoint path cannot be null or empty");
            return;
        }

        if (endpoint.Enabled)
        {
            if (string.IsNullOrEmpty(endpoint.KeyIdentifier))
            {
                errors.Add($"KeyIdentifier is required for enabled endpoint: {endpointPath}");
            }

            if (endpoint.EncryptedContentTypes == null || endpoint.EncryptedContentTypes.Length == 0)
            {
                errors.Add($"EncryptedContentTypes cannot be empty for endpoint: {endpointPath}");
            }

            if (endpoint.EncryptedMethods == null || endpoint.EncryptedMethods.Length == 0)
            {
                errors.Add($"EncryptedMethods cannot be empty for endpoint: {endpointPath}");
            }

            // Validate bypass configuration
            if (endpoint.Bypass != null)
            {
                ValidateBypassConfiguration(endpointPath, endpoint.Bypass, errors);
            }
        }
    }

    private void ValidateBypassConfiguration(string endpointPath, RSABypassOptions bypass, List<string> errors)
    {
        if (bypass.BypassUserAgents != null)
        {
            foreach (var userAgent in bypass.BypassUserAgents)
            {
                if (string.IsNullOrEmpty(userAgent))
                {
                    errors.Add($"BypassUserAgents cannot contain null or empty values for endpoint: {endpointPath}");
                }
            }
        }

        if (bypass.BypassIpAddresses != null)
        {
            foreach (var ipAddress in bypass.BypassIpAddresses)
            {
                if (string.IsNullOrEmpty(ipAddress))
                {
                    errors.Add($"BypassIpAddresses cannot contain null or empty values for endpoint: {endpointPath}");
                }
            }
        }

        if (bypass.BypassHeaders != null)
        {
            foreach (var header in bypass.BypassHeaders)
            {
                if (string.IsNullOrEmpty(header.Key))
                {
                    errors.Add($"BypassHeaders cannot contain null or empty keys for endpoint: {endpointPath}");
                }
            }
        }
    }
} 