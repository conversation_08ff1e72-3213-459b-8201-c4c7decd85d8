using System.ComponentModel.DataAnnotations;
using TransferService.Application.Common.Models;

namespace TransferService.Application.Configuration;

/// <summary>
/// Comprehensive RSA configuration options for the application
/// </summary>
public class RSAOptions
{
    /// <summary>
    /// Global RSA encryption settings
    /// </summary>
    public RSAEncryptionOptions Encryption { get; set; } = new();

    /// <summary>
    /// RSA key configurations mapped by key identifier
    /// </summary>
    public Dictionary<string, RSAKeyConfiguration> Keys { get; set; } = new();

    /// <summary>
    /// Key management configuration
    /// </summary>
    public RSAKeyManagementOptions KeyManagement { get; set; } = new();

    /// <summary>
    /// Per-endpoint RSA configuration
    /// </summary>
    public Dictionary<string, RSAEndpointOptions> Endpoints { get; set; } = new();

    /// <summary>
    /// Middleware configuration
    /// </summary>
    public RSAMiddlewareOptions Middleware { get; set; } = new();

    /// <summary>
    /// Performance and monitoring settings
    /// </summary>
    public RSAMonitoringOptions Monitoring { get; set; } = new();
}

/// <summary>
/// RSA key management configuration
/// </summary>
public class RSAKeyManagementOptions
{
    /// <summary>
    /// Enable automatic key rotation
    /// </summary>
    public bool EnableKeyRotation { get; set; } = false;

    /// <summary>
    /// Key rotation interval
    /// </summary>
    public TimeSpan RotationInterval { get; set; } = TimeSpan.FromDays(90);

    /// <summary>
    /// Number of keys to keep in rotation
    /// </summary>
    [Range(1, 10)]
    public int MaxKeysInRotation { get; set; } = 3;

    /// <summary>
    /// Key storage configuration
    /// </summary>
    public RSAKeyStorageOptions Storage { get; set; } = new();

    /// <summary>
    /// Backup configuration for keys
    /// </summary>
    public RSAKeyBackupOptions Backup { get; set; } = new();
}

/// <summary>
/// RSA key storage configuration
/// </summary>
public class RSAKeyStorageOptions
{
    /// <summary>
    /// Storage type for RSA keys
    /// </summary>
    public RSAKeyStorageType StorageType { get; set; } = RSAKeyStorageType.InMemory;

    /// <summary>
    /// File path for file-based storage
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// Database connection string for database storage
    /// </summary>
    public string? DatabaseConnectionString { get; set; }

    /// <summary>
    /// Azure Key Vault configuration
    /// </summary>
    public RSAKeyVaultOptions? KeyVault { get; set; }

    /// <summary>
    /// Enable key encryption at rest
    /// </summary>
    public bool EncryptAtRest { get; set; } = true;
}

/// <summary>
/// Azure Key Vault configuration
/// </summary>
public class RSAKeyVaultOptions
{
    /// <summary>
    /// Key Vault URL
    /// </summary>
    [Required]
    public string VaultUrl { get; set; } = string.Empty;

    /// <summary>
    /// Tenant ID
    /// </summary>
    [Required]
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Client ID
    /// </summary>
    [Required]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// Client secret
    /// </summary>
    public string? ClientSecret { get; set; }

    /// <summary>
    /// Key name prefix
    /// </summary>
    public string KeyNamePrefix { get; set; } = "rsa-";
}

/// <summary>
/// RSA key backup configuration
/// </summary>
public class RSAKeyBackupOptions
{
    /// <summary>
    /// Enable key backup
    /// </summary>
    public bool Enabled { get; set; } = false;

    /// <summary>
    /// Backup location
    /// </summary>
    public string? BackupLocation { get; set; }

    /// <summary>
    /// Backup frequency
    /// </summary>
    public TimeSpan BackupFrequency { get; set; } = TimeSpan.FromDays(7);

    /// <summary>
    /// Number of backups to retain
    /// </summary>
    [Range(1, 100)]
    public int RetentionCount { get; set; } = 10;
}

/// <summary>
/// Per-endpoint RSA configuration
/// </summary>
public class RSAEndpointOptions
{
    /// <summary>
    /// Enable RSA encryption for this endpoint
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Key identifier to use for this endpoint
    /// </summary>
    public string? KeyIdentifier { get; set; }

    /// <summary>
    /// Encryption mode for this endpoint
    /// </summary>
    public RSAEncryptionMode EncryptionMode { get; set; } = RSAEncryptionMode.RequestResponse;

    /// <summary>
    /// Serialization format for object encryption
    /// </summary>
    public SerializationFormat SerializationFormat { get; set; } = SerializationFormat.Json;

    /// <summary>
    /// Content types that should be encrypted
    /// </summary>
    public string[] EncryptedContentTypes { get; set; } = { "application/json", "application/xml" };

    /// <summary>
    /// HTTP methods that should be encrypted
    /// </summary>
    public string[] EncryptedMethods { get; set; } = { "POST", "PUT", "PATCH" };

    /// <summary>
    /// Bypass encryption for specific conditions
    /// </summary>
    public RSABypassOptions Bypass { get; set; } = new();
}

/// <summary>
/// RSA bypass configuration
/// </summary>
public class RSABypassOptions
{
    /// <summary>
    /// Bypass encryption for health checks
    /// </summary>
    public bool HealthChecks { get; set; } = true;

    /// <summary>
    /// Bypass encryption for specific user agents
    /// </summary>
    public string[] BypassUserAgents { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Bypass encryption for specific IP addresses
    /// </summary>
    public string[] BypassIpAddresses { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Bypass encryption for specific request headers
    /// </summary>
    public Dictionary<string, string> BypassHeaders { get; set; } = new();
}

/// <summary>
/// RSA middleware configuration
/// </summary>
public class RSAMiddlewareOptions
{
    /// <summary>
    /// Enable RSA middleware
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Middleware order in the pipeline
    /// </summary>
    public int Order { get; set; } = 100;

    /// <summary>
    /// Enable request decryption
    /// </summary>
    public bool EnableRequestDecryption { get; set; } = true;

    /// <summary>
    /// Enable response encryption
    /// </summary>
    public bool EnableResponseEncryption { get; set; } = true;

    /// <summary>
    /// Error handling configuration
    /// </summary>
    public RSAErrorHandlingOptions ErrorHandling { get; set; } = new();
}

/// <summary>
/// RSA error handling configuration
/// </summary>
public class RSAErrorHandlingOptions
{
    /// <summary>
    /// Action to take on encryption/decryption errors
    /// </summary>
    public RSAErrorAction OnError { get; set; } = RSAErrorAction.ReturnError;

    /// <summary>
    /// Return detailed error messages in development
    /// </summary>
    public bool IncludeDetailsInErrors { get; set; } = false;

    /// <summary>
    /// Log encryption/decryption errors
    /// </summary>
    public bool LogErrors { get; set; } = true;

    /// <summary>
    /// Custom error message for encryption failures
    /// </summary>
    public string EncryptionErrorMessage { get; set; } = "Request encryption failed";

    /// <summary>
    /// Custom error message for decryption failures
    /// </summary>
    public string DecryptionErrorMessage { get; set; } = "Request decryption failed";
}

/// <summary>
/// RSA monitoring configuration
/// </summary>
public class RSAMonitoringOptions
{
    /// <summary>
    /// Enable performance monitoring
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// Enable metrics collection
    /// </summary>
    public bool EnableMetrics { get; set; } = true;

    /// <summary>
    /// Performance threshold for warnings (milliseconds)
    /// </summary>
    public int PerformanceWarningThreshold { get; set; } = 1000;

    /// <summary>
    /// Performance threshold for errors (milliseconds)
    /// </summary>
    public int PerformanceErrorThreshold { get; set; } = 5000;

    /// <summary>
    /// Enable health checks
    /// </summary>
    public bool EnableHealthChecks { get; set; } = true;

    /// <summary>
    /// Health check interval
    /// </summary>
    public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(5);
}

/// <summary>
/// RSA key storage types
/// </summary>
public enum RSAKeyStorageType
{
    InMemory,
    File,
    Database,
    AzureKeyVault,
    AWSKMS
}

/// <summary>
/// RSA encryption modes
/// </summary>
public enum RSAEncryptionMode
{
    None,
    RequestOnly,
    ResponseOnly,
    RequestResponse
}

/// <summary>
/// RSA error actions
/// </summary>
public enum RSAErrorAction
{
    ReturnError,
    Bypass,
    ThrowException
}

/// <summary>
/// Configuration for a specific RSA key pair
/// </summary>
public class RSAKeyConfiguration
{
    /// <summary>
    /// Path to the public key file (PEM format)
    /// </summary>
    [Required]
    public string PublicKeyPath { get; set; } = string.Empty;

    /// <summary>
    /// Path to the private key file (PEM format)
    /// </summary>
    [Required]
    public string PrivateKeyPath { get; set; } = string.Empty;

    /// <summary>
    /// Unique identifier for this key pair
    /// </summary>
    [Required]
    public string KeyIdentifier { get; set; } = string.Empty;

    /// <summary>
    /// Description of this key pair
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Whether this key pair is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Key size in bits (for validation)
    /// </summary>
    [Range(1024, 4096)]
    public int KeySize { get; set; } = 2048;
} 