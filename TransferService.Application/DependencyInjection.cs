using Microsoft.Extensions.DependencyInjection;
using FluentValidation;
using MediatR;
using AutoMapper;
using System.Reflection;
using TransferService.Application.Common.Behaviors;
using TransferService.Application.Services;
using TransferService.Application.Services.Interfaces;
using TransferService.Application.Common.Extensions;
using Microsoft.Extensions.Configuration;

namespace TransferService.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // Add MediatR
        services.AddMediatR(cfg => {
            cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(PerformanceBehavior<,>));
        });

        // Add FluentValidation - automatic registration
        services.AddFluentValidators();

        // Add AutoMapper
        services.AddAutoMapper(Assembly.GetExecutingAssembly());

        // Add XML Services
        services.AddScoped<IXmlValidationService, XmlValidationService>();
        services.AddScoped<IXmlResponseFactory>(provider => {
            var config = provider.GetRequiredService<IConfiguration>();
            var institutionCode = config.GetValue<string>("ExternalApis:EasyPay:InstitutionCode") ?? "";
            return new XmlResponseFactory(institutionCode);
        });

        // Add Clean Payment Router Services - automatic registration
        services.AddScoped<IPaymentHealthManager, PaymentHealthManager>();
        services.AddPaymentResultMappers();
        services.AddPaymentProviderHandlers();
        services.AddScoped<IPaymentProviderRouter, PaymentProviderRouter>();

        return services;
    }
}
