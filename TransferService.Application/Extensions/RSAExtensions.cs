using System.Text;
using System.Text.Json;
using System.Xml.Serialization;
using TransferService.Application.Common.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Extensions;

/// <summary>
/// Extension methods for RSA operations
/// </summary>
public static class RSAExtensions
{
    /// <summary>
    /// Encrypts a string using the RSA service
    /// </summary>
    /// <param name="plainText">Text to encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Encrypted text</returns>
    public static async Task<string> EncryptAsync(this string plainText, IRSACryptographyService rsaService, string? keyIdentifier = null)
    {
        var result = await rsaService.EncryptAsync(plainText, keyIdentifier);
        if (!result.IsSuccess)
        {
            throw new InvalidOperationException($"Encryption failed: {result.ErrorMessage}");
        }
        return result.Data!;
    }

    /// <summary>
    /// Decrypts a string using the RSA service
    /// </summary>
    /// <param name="encryptedText">Text to decrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Decrypted text</returns>
    public static async Task<string> DecryptAsync(this string encryptedText, IRSACryptographyService rsaService, string? keyIdentifier = null)
    {
        var result = await rsaService.DecryptAsync(encryptedText, keyIdentifier);
        if (!result.IsSuccess)
        {
            throw new InvalidOperationException($"Decryption failed: {result.ErrorMessage}");
        }
        return result.Data!;
    }

    /// <summary>
    /// Encrypts an object by serializing to JSON and then encrypting
    /// </summary>
    /// <typeparam name="T">Type of object to encrypt</typeparam>
    /// <param name="obj">Object to encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Encrypted object data</returns>
    public static async Task<string> EncryptObjectAsync<T>(this T obj, IRSACryptographyService rsaService, string? keyIdentifier = null) where T : class
    {
        var result = await rsaService.EncryptObjectAsync(obj, SerializationFormat.Json, keyIdentifier);
        if (!result.IsSuccess)
        {
            throw new InvalidOperationException($"Object encryption failed: {result.ErrorMessage}");
        }
        return result.Data!;
    }

    /// <summary>
    /// Encrypts an object by serializing to XML and then encrypting
    /// </summary>
    /// <typeparam name="T">Type of object to encrypt</typeparam>
    /// <param name="obj">Object to encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Encrypted object data</returns>
    public static async Task<string> EncryptObjectXmlAsync<T>(this T obj, IRSACryptographyService rsaService, string? keyIdentifier = null) where T : class
    {
        var result = await rsaService.EncryptObjectAsync(obj, SerializationFormat.Xml, keyIdentifier);
        if (!result.IsSuccess)
        {
            throw new InvalidOperationException($"Object encryption failed: {result.ErrorMessage}");
        }
        return result.Data!;
    }

    /// <summary>
    /// Decrypts and deserializes an object from JSON
    /// </summary>
    /// <typeparam name="T">Type of object to decrypt to</typeparam>
    /// <param name="encryptedData">Encrypted data</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Decrypted and deserialized object</returns>
    public static async Task<T> DecryptObjectAsync<T>(this string encryptedData, IRSACryptographyService rsaService, string? keyIdentifier = null) where T : class
    {
        var result = await rsaService.DecryptObjectAsync<T>(encryptedData, SerializationFormat.Json, keyIdentifier);
        if (!result.IsSuccess)
        {
            throw new InvalidOperationException($"Object decryption failed: {result.ErrorMessage}");
        }
        return result.Data!;
    }

    /// <summary>
    /// Decrypts and deserializes an object from XML
    /// </summary>
    /// <typeparam name="T">Type of object to decrypt to</typeparam>
    /// <param name="encryptedData">Encrypted data</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Decrypted and deserialized object</returns>
    public static async Task<T> DecryptObjectXmlAsync<T>(this string encryptedData, IRSACryptographyService rsaService, string? keyIdentifier = null) where T : class
    {
        var result = await rsaService.DecryptObjectAsync<T>(encryptedData, SerializationFormat.Xml, keyIdentifier);
        if (!result.IsSuccess)
        {
            throw new InvalidOperationException($"Object decryption failed: {result.ErrorMessage}");
        }
        return result.Data!;
    }

    /// <summary>
    /// Safely encrypts a string, returning null if encryption fails
    /// </summary>
    /// <param name="plainText">Text to encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Encrypted text or null if failed</returns>
    public static async Task<string?> TryEncryptAsync(this string plainText, IRSACryptographyService rsaService, string? keyIdentifier = null)
    {
        var result = await rsaService.EncryptAsync(plainText, keyIdentifier);
        return result.IsSuccess ? result.Data : null;
    }

    /// <summary>
    /// Safely decrypts a string, returning null if decryption fails
    /// </summary>
    /// <param name="encryptedText">Text to decrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Decrypted text or null if failed</returns>
    public static async Task<string?> TryDecryptAsync(this string encryptedText, IRSACryptographyService rsaService, string? keyIdentifier = null)
    {
        var result = await rsaService.DecryptAsync(encryptedText, keyIdentifier);
        return result.IsSuccess ? result.Data : null;
    }

    /// <summary>
    /// Safely encrypts an object, returning null if encryption fails
    /// </summary>
    /// <typeparam name="T">Type of object to encrypt</typeparam>
    /// <param name="obj">Object to encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Encrypted object data or null if failed</returns>
    public static async Task<string?> TryEncryptObjectAsync<T>(this T obj, IRSACryptographyService rsaService, string? keyIdentifier = null) where T : class
    {
        var result = await rsaService.EncryptObjectAsync(obj, SerializationFormat.Json, keyIdentifier);
        return result.IsSuccess ? result.Data : null;
    }

    /// <summary>
    /// Safely decrypts an object, returning null if decryption fails
    /// </summary>
    /// <typeparam name="T">Type of object to decrypt to</typeparam>
    /// <param name="encryptedData">Encrypted data</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Decrypted object or null if failed</returns>
    public static async Task<T?> TryDecryptObjectAsync<T>(this string encryptedData, IRSACryptographyService rsaService, string? keyIdentifier = null) where T : class
    {
        var result = await rsaService.DecryptObjectAsync<T>(encryptedData, SerializationFormat.Json, keyIdentifier);
        return result.IsSuccess ? result.Data : null;
    }
} 