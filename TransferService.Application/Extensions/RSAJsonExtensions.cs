using System.Text.Json;
using TransferService.Application.Common.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Extensions;

/// <summary>
/// JSON serialization helpers with RSA encryption
/// </summary>
public static class RSAJsonExtensions
{
    private static readonly JsonSerializerOptions DefaultJsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        PropertyNameCaseInsensitive = true,
        WriteIndented = false
    };

    /// <summary>
    /// Serializes an object to JSON and encrypts it
    /// </summary>
    /// <typeparam name="T">Type of object to serialize and encrypt</typeparam>
    /// <param name="obj">Object to serialize and encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="jsonOptions">Optional JSON serialization options</param>
    /// <returns>Encrypted JSON data</returns>
    public static async Task<string> ToEncryptedJsonAsync<T>(this T obj, IRSACryptographyService rsaService, string? keyIdentifier = null, JsonSerializerOptions? jsonOptions = null) where T : class
    {
        var options = jsonOptions ?? DefaultJsonOptions;
        var json = JsonSerializer.Serialize(obj, options);
        return await rsaService.EncryptAsync(json, keyIdentifier);
    }

    /// <summary>
    /// Decrypts and deserializes JSON data to an object
    /// </summary>
    /// <typeparam name="T">Type of object to deserialize to</typeparam>
    /// <param name="encryptedJson">Encrypted JSON data</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="jsonOptions">Optional JSON deserialization options</param>
    /// <returns>Deserialized object</returns>
    public static async Task<T> FromEncryptedJsonAsync<T>(this string encryptedJson, IRSACryptographyService rsaService, string? keyIdentifier = null, JsonSerializerOptions? jsonOptions = null) where T : class
    {
        var options = jsonOptions ?? DefaultJsonOptions;
        var decryptResult = await rsaService.DecryptAsync(encryptedJson, keyIdentifier);
        
        if (!decryptResult.IsSuccess)
        {
            throw new InvalidOperationException($"JSON decryption failed: {decryptResult.ErrorMessage}");
        }

        var deserialized = JsonSerializer.Deserialize<T>(decryptResult.Data!, options);
        if (deserialized == null)
        {
            throw new InvalidOperationException("Failed to deserialize JSON data");
        }

        return deserialized;
    }

    /// <summary>
    /// Safely serializes an object to JSON and encrypts it, returning null if failed
    /// </summary>
    /// <typeparam name="T">Type of object to serialize and encrypt</typeparam>
    /// <param name="obj">Object to serialize and encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="jsonOptions">Optional JSON serialization options</param>
    /// <returns>Encrypted JSON data or null if failed</returns>
    public static async Task<string?> TryToEncryptedJsonAsync<T>(this T obj, IRSACryptographyService rsaService, string? keyIdentifier = null, JsonSerializerOptions? jsonOptions = null) where T : class
    {
        try
        {
            return await obj.ToEncryptedJsonAsync(rsaService, keyIdentifier, jsonOptions);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Safely decrypts and deserializes JSON data, returning null if failed
    /// </summary>
    /// <typeparam name="T">Type of object to deserialize to</typeparam>
    /// <param name="encryptedJson">Encrypted JSON data</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="jsonOptions">Optional JSON deserialization options</param>
    /// <returns>Deserialized object or null if failed</returns>
    public static async Task<T?> TryFromEncryptedJsonAsync<T>(this string encryptedJson, IRSACryptographyService rsaService, string? keyIdentifier = null, JsonSerializerOptions? jsonOptions = null) where T : class
    {
        try
        {
            return await encryptedJson.FromEncryptedJsonAsync<T>(rsaService, keyIdentifier, jsonOptions);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Creates an encrypted JSON response object
    /// </summary>
    /// <typeparam name="T">Type of data to encrypt</typeparam>
    /// <param name="data">Data to encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="jsonOptions">Optional JSON serialization options</param>
    /// <returns>Encrypted response object</returns>
    public static async Task<EncryptedJsonResponse<T>> ToEncryptedResponseAsync<T>(this T data, IRSACryptographyService rsaService, string? keyIdentifier = null, JsonSerializerOptions? jsonOptions = null) where T : class
    {
        var encryptedData = await data.ToEncryptedJsonAsync(rsaService, keyIdentifier, jsonOptions);
        return new EncryptedJsonResponse<T>
        {
            Encrypted = true,
            Data = encryptedData,
            Timestamp = DateTime.UtcNow,
            KeyIdentifier = keyIdentifier
        };
    }

    /// <summary>
    /// Creates an encrypted JSON response with metadata
    /// </summary>
    /// <typeparam name="T">Type of data to encrypt</typeparam>
    /// <param name="data">Data to encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="metadata">Additional metadata</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="jsonOptions">Optional JSON serialization options</param>
    /// <returns>Encrypted response object with metadata</returns>
    public static async Task<EncryptedJsonResponse<T>> ToEncryptedResponseWithMetadataAsync<T>(this T data, IRSACryptographyService rsaService, Dictionary<string, object> metadata, string? keyIdentifier = null, JsonSerializerOptions? jsonOptions = null) where T : class
    {
        var response = await data.ToEncryptedResponseAsync(rsaService, keyIdentifier, jsonOptions);
        response.Metadata = metadata;
        return response;
    }
}

/// <summary>
/// Encrypted JSON response wrapper
/// </summary>
/// <typeparam name="T">Type of the original data</typeparam>
public class EncryptedJsonResponse<T> where T : class
{
    /// <summary>
    /// Indicates if the data is encrypted
    /// </summary>
    public bool Encrypted { get; set; }

    /// <summary>
    /// The encrypted data
    /// </summary>
    public string Data { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp when the data was encrypted
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Key identifier used for encryption
    /// </summary>
    public string? KeyIdentifier { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// Decrypts and returns the original data
    /// </summary>
    /// <param name="rsaService">RSA service</param>
    /// <param name="jsonOptions">Optional JSON deserialization options</param>
    /// <returns>Original data</returns>
    public async Task<T> DecryptAsync(IRSACryptographyService rsaService, JsonSerializerOptions? jsonOptions = null)
    {
        return await Data.FromEncryptedJsonAsync<T>(rsaService, KeyIdentifier, jsonOptions);
    }
} 