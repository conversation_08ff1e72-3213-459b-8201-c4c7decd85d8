using System.Diagnostics;
using Microsoft.Extensions.Logging;
using TransferService.Application.Common.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Extensions;

/// <summary>
/// Performance monitoring extensions for RSA operations
/// </summary>
public static class RSAPerformanceExtensions
{
    /// <summary>
    /// Executes an RSA operation with performance monitoring
    /// </summary>
    /// <typeparam name="T">Type of the result</typeparam>
    /// <param name="operation">RSA operation to execute</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="operationName">Name of the operation for logging</param>
    /// <param name="keyIdentifier">Key identifier for logging</param>
    /// <param name="dataSize">Size of data being processed</param>
    /// <param name="warningThresholdMs">Warning threshold in milliseconds</param>
    /// <param name="errorThresholdMs">Error threshold in milliseconds</param>
    /// <returns>Operation result with performance metrics</returns>
    public static async Task<RSAOperationResult<T>> WithPerformanceMonitoring<T>(
        this Func<Task<RSAOperationResult<T>>> operation,
        ILogger logger,
        string operationName,
        string? keyIdentifier = null,
        int? dataSize = null,
        int warningThresholdMs = 1000,
        int errorThresholdMs = 5000)
    {
        var stopwatch = Stopwatch.StartNew();
        var startTime = DateTime.UtcNow;

        try
        {
            var result = await operation();
            stopwatch.Stop();

            var elapsedMs = stopwatch.ElapsedMilliseconds;
            var logLevel = GetLogLevel(elapsedMs, warningThresholdMs, errorThresholdMs);

            logger.Log(logLevel, 
                "RSA {Operation} completed in {ElapsedMs}ms for key {KeyId}, data size: {DataSize} bytes, success: {Success}",
                operationName, elapsedMs, keyIdentifier ?? "default", dataSize ?? 0, result.IsSuccess);

            // Add performance metrics to the result if it's successful
            if (result.IsSuccess)
            {
                // Note: In a real implementation, you might want to add performance metrics
                // to the result object or use a metrics collection service
            }

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            logger.LogError(ex, 
                "RSA {Operation} failed after {ElapsedMs}ms for key {KeyId}, data size: {DataSize} bytes",
                operationName, stopwatch.ElapsedMilliseconds, keyIdentifier ?? "default", dataSize ?? 0);
            
            throw;
        }
    }

    /// <summary>
    /// Executes an RSA encryption operation with performance monitoring
    /// </summary>
    /// <param name="rsaService">RSA service</param>
    /// <param name="plainText">Text to encrypt</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="warningThresholdMs">Warning threshold in milliseconds</param>
    /// <param name="errorThresholdMs">Error threshold in milliseconds</param>
    /// <returns>Encryption result with performance monitoring</returns>
    public static async Task<RSAOperationResult<string>> EncryptWithPerformanceMonitoring(
        this IRSACryptographyService rsaService,
        string plainText,
        ILogger logger,
        string? keyIdentifier = null,
        int warningThresholdMs = 1000,
        int errorThresholdMs = 5000)
    {
        return await WithPerformanceMonitoring(
            () => rsaService.EncryptAsync(plainText, keyIdentifier),
            logger,
            "Encryption",
            keyIdentifier,
            plainText?.Length,
            warningThresholdMs,
            errorThresholdMs);
    }

    /// <summary>
    /// Executes an RSA decryption operation with performance monitoring
    /// </summary>
    /// <param name="rsaService">RSA service</param>
    /// <param name="encryptedText">Text to decrypt</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="warningThresholdMs">Warning threshold in milliseconds</param>
    /// <param name="errorThresholdMs">Error threshold in milliseconds</param>
    /// <returns>Decryption result with performance monitoring</returns>
    public static async Task<RSAOperationResult<string>> DecryptWithPerformanceMonitoring(
        this IRSACryptographyService rsaService,
        string encryptedText,
        ILogger logger,
        string? keyIdentifier = null,
        int warningThresholdMs = 1000,
        int errorThresholdMs = 5000)
    {
        return await WithPerformanceMonitoring(
            () => rsaService.DecryptAsync(encryptedText, keyIdentifier),
            logger,
            "Decryption",
            keyIdentifier,
            encryptedText?.Length,
            warningThresholdMs,
            errorThresholdMs);
    }

    /// <summary>
    /// Executes an RSA object encryption operation with performance monitoring
    /// </summary>
    /// <typeparam name="T">Type of object to encrypt</typeparam>
    /// <param name="rsaService">RSA service</param>
    /// <param name="obj">Object to encrypt</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="serializationFormat">Serialization format</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="warningThresholdMs">Warning threshold in milliseconds</param>
    /// <param name="errorThresholdMs">Error threshold in milliseconds</param>
    /// <returns>Object encryption result with performance monitoring</returns>
    public static async Task<RSAOperationResult<string>> EncryptObjectWithPerformanceMonitoring<T>(
        this IRSACryptographyService rsaService,
        T obj,
        ILogger logger,
        SerializationFormat serializationFormat = SerializationFormat.Json,
        string? keyIdentifier = null,
        int warningThresholdMs = 1000,
        int errorThresholdMs = 5000) where T : class
    {
        return await WithPerformanceMonitoring(
            () => rsaService.EncryptObjectAsync(obj, serializationFormat, keyIdentifier),
            logger,
            $"ObjectEncryption({serializationFormat})",
            keyIdentifier,
            null, // Data size calculation would require serialization
            warningThresholdMs,
            errorThresholdMs);
    }

    /// <summary>
    /// Executes an RSA object decryption operation with performance monitoring
    /// </summary>
    /// <typeparam name="T">Type of object to decrypt to</typeparam>
    /// <param name="rsaService">RSA service</param>
    /// <param name="encryptedData">Encrypted data</param>
    /// <param name="logger">Logger instance</param>
    /// <param name="serializationFormat">Serialization format</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="warningThresholdMs">Warning threshold in milliseconds</param>
    /// <param name="errorThresholdMs">Error threshold in milliseconds</param>
    /// <returns>Object decryption result with performance monitoring</returns>
    public static async Task<RSAOperationResult<T>> DecryptObjectWithPerformanceMonitoring<T>(
        this IRSACryptographyService rsaService,
        string encryptedData,
        ILogger logger,
        SerializationFormat serializationFormat = SerializationFormat.Json,
        string? keyIdentifier = null,
        int warningThresholdMs = 1000,
        int errorThresholdMs = 5000) where T : class
    {
        return await WithPerformanceMonitoring(
            () => rsaService.DecryptObjectAsync<T>(encryptedData, serializationFormat, keyIdentifier),
            logger,
            $"ObjectDecryption({serializationFormat})",
            keyIdentifier,
            encryptedData?.Length,
            warningThresholdMs,
            errorThresholdMs);
    }

    /// <summary>
    /// Gets the appropriate log level based on elapsed time and thresholds
    /// </summary>
    /// <param name="elapsedMs">Elapsed time in milliseconds</param>
    /// <param name="warningThresholdMs">Warning threshold in milliseconds</param>
    /// <param name="errorThresholdMs">Error threshold in milliseconds</param>
    /// <returns>Log level</returns>
    private static LogLevel GetLogLevel(long elapsedMs, int warningThresholdMs, int errorThresholdMs)
    {
        if (elapsedMs >= errorThresholdMs)
            return LogLevel.Error;
        
        if (elapsedMs >= warningThresholdMs)
            return LogLevel.Warning;
        
        return LogLevel.Information;
    }

    /// <summary>
    /// Creates a performance metrics object for RSA operations
    /// </summary>
    /// <param name="operationName">Name of the operation</param>
    /// <param name="elapsedMs">Elapsed time in milliseconds</param>
    /// <param name="keyIdentifier">Key identifier</param>
    /// <param name="dataSize">Data size in bytes</param>
    /// <param name="success">Whether the operation was successful</param>
    /// <returns>Performance metrics object</returns>
    public static RSAPerformanceMetrics CreatePerformanceMetrics(
        string operationName,
        long elapsedMs,
        string? keyIdentifier = null,
        int? dataSize = null,
        bool success = true)
    {
        return new RSAPerformanceMetrics
        {
            OperationName = operationName,
            ElapsedMilliseconds = elapsedMs,
            KeyIdentifier = keyIdentifier ?? "default",
            DataSizeBytes = dataSize,
            Success = success,
            Timestamp = DateTime.UtcNow
        };
    }
}

/// <summary>
/// Performance metrics for RSA operations
/// </summary>
public class RSAPerformanceMetrics
{
    /// <summary>
    /// Name of the RSA operation
    /// </summary>
    public string OperationName { get; set; } = string.Empty;

    /// <summary>
    /// Elapsed time in milliseconds
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// Key identifier used for the operation
    /// </summary>
    public string KeyIdentifier { get; set; } = string.Empty;

    /// <summary>
    /// Size of data processed in bytes
    /// </summary>
    public int? DataSizeBytes { get; set; }

    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Timestamp when the operation completed
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Performance category based on elapsed time
    /// </summary>
    public string PerformanceCategory => ElapsedMilliseconds switch
    {
        < 100 => "Excellent",
        < 500 => "Good",
        < 1000 => "Fair",
        < 5000 => "Poor",
        _ => "Very Poor"
    };
} 