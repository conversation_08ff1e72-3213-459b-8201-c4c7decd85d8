using System.Text;
using System.Xml;
using System.Xml.Serialization;
using TransferService.Application.Common.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Extensions;

/// <summary>
/// XML serialization helpers with RSA encryption
/// </summary>
public static class RSAXmlExtensions
{
    private static readonly XmlWriterSettings DefaultXmlSettings = new()
    {
        Encoding = Encoding.UTF8,
        Indent = false,
        OmitXmlDeclaration = false
    };

    /// <summary>
    /// Serializes an object to XML and encrypts it
    /// </summary>
    /// <typeparam name="T">Type of object to serialize and encrypt</typeparam>
    /// <param name="obj">Object to serialize and encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="xmlSettings">Optional XML writer settings</param>
    /// <returns>Encrypted XML data</returns>
    public static async Task<string> ToEncryptedXmlAsync<T>(this T obj, IRSACryptographyService rsaService, string? keyIdentifier = null, XmlWriterSettings? xmlSettings = null) where T : class
    {
        var settings = xmlSettings ?? DefaultXmlSettings;
        var xml = SerializeToXml(obj, settings);
        return await rsaService.EncryptAsync(xml, keyIdentifier);
    }

    /// <summary>
    /// Decrypts and deserializes XML data to an object
    /// </summary>
    /// <typeparam name="T">Type of object to deserialize to</typeparam>
    /// <param name="encryptedXml">Encrypted XML data</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Deserialized object</returns>
    public static async Task<T> FromEncryptedXmlAsync<T>(this string encryptedXml, IRSACryptographyService rsaService, string? keyIdentifier = null) where T : class
    {
        var decryptResult = await rsaService.DecryptAsync(encryptedXml, keyIdentifier);
        
        if (!decryptResult.IsSuccess)
        {
            throw new InvalidOperationException($"XML decryption failed: {decryptResult.ErrorMessage}");
        }

        var deserialized = DeserializeFromXml<T>(decryptResult.Data!);
        if (deserialized == null)
        {
            throw new InvalidOperationException("Failed to deserialize XML data");
        }

        return deserialized;
    }

    /// <summary>
    /// Safely serializes an object to XML and encrypts it, returning null if failed
    /// </summary>
    /// <typeparam name="T">Type of object to serialize and encrypt</typeparam>
    /// <param name="obj">Object to serialize and encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="xmlSettings">Optional XML writer settings</param>
    /// <returns>Encrypted XML data or null if failed</returns>
    public static async Task<string?> TryToEncryptedXmlAsync<T>(this T obj, IRSACryptographyService rsaService, string? keyIdentifier = null, XmlWriterSettings? xmlSettings = null) where T : class
    {
        try
        {
            return await obj.ToEncryptedXmlAsync(rsaService, keyIdentifier, xmlSettings);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Safely decrypts and deserializes XML data, returning null if failed
    /// </summary>
    /// <typeparam name="T">Type of object to deserialize to</typeparam>
    /// <param name="encryptedXml">Encrypted XML data</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Deserialized object or null if failed</returns>
    public static async Task<T?> TryFromEncryptedXmlAsync<T>(this string encryptedXml, IRSACryptographyService rsaService, string? keyIdentifier = null) where T : class
    {
        try
        {
            return await encryptedXml.FromEncryptedXmlAsync<T>(rsaService, keyIdentifier);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Creates an encrypted XML response object
    /// </summary>
    /// <typeparam name="T">Type of data to encrypt</typeparam>
    /// <param name="data">Data to encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="xmlSettings">Optional XML writer settings</param>
    /// <returns>Encrypted response object</returns>
    public static async Task<EncryptedXmlResponse<T>> ToEncryptedXmlResponseAsync<T>(this T data, IRSACryptographyService rsaService, string? keyIdentifier = null, XmlWriterSettings? xmlSettings = null) where T : class
    {
        var encryptedData = await data.ToEncryptedXmlAsync(rsaService, keyIdentifier, xmlSettings);
        return new EncryptedXmlResponse<T>
        {
            Encrypted = true,
            Data = encryptedData,
            Timestamp = DateTime.UtcNow,
            KeyIdentifier = keyIdentifier
        };
    }

    /// <summary>
    /// Creates an encrypted XML response with metadata
    /// </summary>
    /// <typeparam name="T">Type of data to encrypt</typeparam>
    /// <param name="data">Data to encrypt</param>
    /// <param name="rsaService">RSA service</param>
    /// <param name="metadata">Additional metadata</param>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <param name="xmlSettings">Optional XML writer settings</param>
    /// <returns>Encrypted response object with metadata</returns>
    public static async Task<EncryptedXmlResponse<T>> ToEncryptedXmlResponseWithMetadataAsync<T>(this T data, IRSACryptographyService rsaService, Dictionary<string, object> metadata, string? keyIdentifier = null, XmlWriterSettings? xmlSettings = null) where T : class
    {
        var response = await data.ToEncryptedXmlResponseAsync(rsaService, keyIdentifier, xmlSettings);
        response.Metadata = metadata;
        return response;
    }

    /// <summary>
    /// Serializes an object to XML string
    /// </summary>
    /// <typeparam name="T">Type of object to serialize</typeparam>
    /// <param name="obj">Object to serialize</param>
    /// <param name="settings">XML writer settings</param>
    /// <returns>XML string</returns>
    private static string SerializeToXml<T>(T obj, XmlWriterSettings settings) where T : class
    {
        var serializer = new XmlSerializer(typeof(T));
        using var stringWriter = new StringWriter();
        using var xmlWriter = XmlWriter.Create(stringWriter, settings);
        
        var ns = new XmlSerializerNamespaces();
        ns.Add("", ""); // Remove default namespace
        
        serializer.Serialize(xmlWriter, obj, ns);
        return stringWriter.ToString();
    }

    /// <summary>
    /// Deserializes an object from XML string
    /// </summary>
    /// <typeparam name="T">Type of object to deserialize to</typeparam>
    /// <param name="xml">XML string</param>
    /// <returns>Deserialized object</returns>
    private static T? DeserializeFromXml<T>(string xml) where T : class
    {
        var serializer = new XmlSerializer(typeof(T));
        using var stringReader = new StringReader(xml);
        return (T?)serializer.Deserialize(stringReader);
    }
}

/// <summary>
/// Encrypted XML response wrapper
/// </summary>
/// <typeparam name="T">Type of the original data</typeparam>
public class EncryptedXmlResponse<T> where T : class
{
    /// <summary>
    /// Indicates if the data is encrypted
    /// </summary>
    public bool Encrypted { get; set; }

    /// <summary>
    /// The encrypted data
    /// </summary>
    public string Data { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp when the data was encrypted
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Key identifier used for encryption
    /// </summary>
    public string? KeyIdentifier { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// Decrypts and returns the original data
    /// </summary>
    /// <param name="rsaService">RSA service</param>
    /// <returns>Original data</returns>
    public async Task<T> DecryptAsync(IRSACryptographyService rsaService)
    {
        return await Data.FromEncryptedXmlAsync<T>(rsaService, KeyIdentifier);
    }
} 