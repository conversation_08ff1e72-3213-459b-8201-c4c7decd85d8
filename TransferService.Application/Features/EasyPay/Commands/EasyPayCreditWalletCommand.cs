using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Features.EasyPay.Commands;

public record EasyPayCreditWalletCommand : IRequest<EasyPayCreditWalletResultDto>
{
    public decimal Amount { get; init; }
}

public class EasyPayCreditWalletCommandValidator : AbstractValidator<EasyPayCreditWalletCommand>
{
    public EasyPayCreditWalletCommandValidator()
    {
        RuleFor(x => x.Amount)
            .GreaterThan(0)
            .WithMessage("Amount must be greater than zero")
            .LessThanOrEqualTo(10000000)
            .WithMessage("Amount cannot exceed 10,000,000");
    }
}

public class EasyPayCreditWalletCommandHandler(
    IEasyPayTransferService easyPayService,
    ILogger<EasyPayCreditWalletCommandHandler> logger)
    : IRequestHandler<EasyPayCreditWalletCommand, EasyPayCreditWalletResultDto>
{
    private readonly IEasyPayTransferService _easyPayService = easyPayService ?? throw new ArgumentNullException(nameof(easyPayService));
    private readonly ILogger<EasyPayCreditWalletCommandHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<EasyPayCreditWalletResultDto> Handle(EasyPayCreditWalletCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing EasyPay wallet credit of {Amount}", request.Amount);

        try
        {
            var result = await _easyPayService.CreditWalletAsync(request.Amount, cancellationToken);

            _logger.LogInformation("EasyPay wallet credit completed. Success: {IsSuccessful}, Amount: {Amount}", 
                result.IsSuccessful, request.Amount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing EasyPay wallet credit of {Amount}", request.Amount);
            throw;
        }
    }
}
