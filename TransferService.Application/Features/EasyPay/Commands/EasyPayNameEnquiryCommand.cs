using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Features.EasyPay.Commands;

public record EasyPayNameEnquiryCommand : IRequest<EasyPayNameEnquiryResultDto>
{
    public string AccountNumber { get; init; } = string.Empty;
    public string BankCode { get; init; } = string.Empty;
    // Additional XML mapping properties for comprehensive deserialization
    public string? SessionId { get; init; }
    public string? ChannelCode { get; init; }
}

public class EasyPayNameEnquiryCommandValidator : AbstractValidator<EasyPayNameEnquiryCommand>
{
    public EasyPayNameEnquiryCommandValidator()
    {
        RuleFor(x => x.AccountNumber)
            .NotEmpty()
            .WithMessage("Account number is required")
            .Length(10, 10)
            .WithMessage("Account number must be exactly 10 digits")
            .Matches(@"^\d+$")
            .WithMessage("Account number must contain only digits");

        RuleFor(x => x.BankCode)
            .NotEmpty()
            .WithMessage("Bank code is required")
            .Length(3, 6)
            .WithMessage("Bank code must be between 3 and 6 characters")
            .Matches(@"^\d+$")
            .WithMessage("Bank code must contain only digits");
    }
}

public class EasyPayNameEnquiryCommandHandler(
    IEasyPayTransferService easyPayService,
    ILogger<EasyPayNameEnquiryCommandHandler> logger)
    : IRequestHandler<EasyPayNameEnquiryCommand, EasyPayNameEnquiryResultDto>
{
    private readonly IEasyPayTransferService _easyPayService = easyPayService ?? throw new ArgumentNullException(nameof(easyPayService));
    private readonly ILogger<EasyPayNameEnquiryCommandHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<EasyPayNameEnquiryResultDto> Handle(EasyPayNameEnquiryCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing EasyPay name enquiry for account {AccountNumber} at bank {BankCode}", 
            request.AccountNumber, request.BankCode);

        try
        {
            var result = await _easyPayService.NameEnquiryAsync(
                request.AccountNumber, 
                request.BankCode, 
                cancellationToken);
            
            _logger.LogInformation("EasyPay name enquiry completed for account {AccountNumber}. Success: {IsSuccessful}", 
                request.AccountNumber, result.IsSuccessful);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing EasyPay name enquiry for account {AccountNumber}", request.AccountNumber);
            throw;
        }
    }
}
