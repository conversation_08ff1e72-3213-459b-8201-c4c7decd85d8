using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Features.EasyPay.Commands;

public record EasyPayTransferCommand : IRequest<EasyPayTransferResultDto>
{
    public string? SessionId { get; set; }
    public int  ChannelCode { get; set; }
    public decimal Amount { get; init; }
    public string AccountNumber { get; init; } = string.Empty;
    public string BankCode { get; init; } = string.Empty;
    public string AccountName { get; init; } = string.Empty;
    public string? Narration { get; init; } = string.Empty;

    // Originator information (sender details)
    public string OriginatorAccountName { get; init; } = string.Empty;
    public string OriginatorAccountNumber { get; init; } = string.Empty;
    public string? OriginatorBankVerificationNumber { get; init; } = string.Empty;
    public string? OriginatorKycLevel { get; init; } = string.Empty;

    // Additional transfer details
    public string? TransactionLocation { get; init; } = string.Empty;
    public string? PaymentReference { get; init; } = string.Empty;
    public string? NameEnquiryRef { get; init; } = string.Empty;
}

public class EasyPayTransferCommandValidator : AbstractValidator<EasyPayTransferCommand>
{
    public EasyPayTransferCommandValidator()
    {
        RuleFor(x => x.Amount)
            .GreaterThan(0)
            .WithMessage("Amount must be greater than zero")
            .LessThanOrEqualTo(1000000)
            .WithMessage("Amount cannot exceed 1,000,000");

        RuleFor(x => x.AccountNumber)
            .NotEmpty()
            .WithMessage("Account number is required")
            .Length(10, 10)
            .WithMessage("Account number must be exactly 10 digits")
            .Matches(@"^\d+$")
            .WithMessage("Account number must contain only digits");

        RuleFor(x => x.BankCode)
            .NotEmpty()
            .WithMessage("Bank code is required")
            .Length(3, 6)
            .WithMessage("Bank code must be between 3 and 6 characters")
            .Matches(@"^\d+$")
            .WithMessage("Bank code must contain only digits");

        RuleFor(x => x.AccountName)
            .NotEmpty()
            .WithMessage("Account name is required")
            .MaximumLength(100)
            .WithMessage("Account name cannot exceed 100 characters");

        RuleFor(x => x.Narration)
            .NotEmpty()
            .WithMessage("Narration is required")
            .MaximumLength(200)
            .WithMessage("Narration cannot exceed 200 characters");
    }
}

public class EasyPayTransferCommandHandler(
    IEasyPayTransferService easyPayService,
    ILogger<EasyPayTransferCommandHandler> logger)
    : IRequestHandler<EasyPayTransferCommand, EasyPayTransferResultDto>
{
    private readonly IEasyPayTransferService _easyPayService = easyPayService ?? throw new ArgumentNullException(nameof(easyPayService));
    private readonly ILogger<EasyPayTransferCommandHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<EasyPayTransferResultDto> Handle(EasyPayTransferCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing EasyPay transfer of {Amount} to account {AccountNumber} at bank {BankCode}", 
            request.Amount, request.AccountNumber, request.BankCode);

        try
        {

            var result = await _easyPayService.TransferAsync(request, cancellationToken);
            _logger.LogInformation("EasyPay transfer completed for account {AccountNumber}. Success: {IsSuccessful}, TransactionId: {TransactionId}", 
                request.AccountNumber, result.IsSuccessful, result.TransactionId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing EasyPay transfer for account {AccountNumber}", request.AccountNumber);
            throw;
        }
    }
}
