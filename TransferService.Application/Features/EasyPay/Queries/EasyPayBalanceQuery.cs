using MediatR;
using Microsoft.Extensions.Logging;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Features.EasyPay.Queries;

public record EasyPayBalanceQuery : IRequest<EasyPayBalanceDto>
{
    // No parameters needed for balance query
}

public class EasyPayBalanceQueryHandler : IRequestHandler<EasyPayBalanceQuery, EasyPayBalanceDto>
{
    private readonly IEasyPayTransferService _easyPayService;
    private readonly ILogger<EasyPayBalanceQueryHandler> _logger;

    public EasyPayBalanceQueryHandler(
        IEasyPayTransferService easyPayService,
        ILogger<EasyPayBalanceQueryHandler> logger)
    {
        _easyPayService = easyPayService ?? throw new ArgumentNullException(nameof(easyPayService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<EasyPayBalanceDto> Handle(EasyPayBalanceQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing EasyPay wallet balance query");

        try
        {
            var result = await _easyPayService.GetWalletBalanceAsync(cancellationToken);

            _logger.LogInformation("EasyPay wallet balance query completed. Success: {IsSuccessful}, Balance: {Balance}", 
                result.IsSuccessful, result.Balance);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing EasyPay wallet balance query");
            throw;
        }
    }
}
