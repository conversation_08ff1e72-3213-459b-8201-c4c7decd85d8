using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Features.EasyPay.Queries;

public record EasyPayTSQQuery : IRequest<EasyPayTSQResultDto>
{
    public string SessionId { get; init; } = string.Empty;
}

public class EasyPayTSQQueryValidator : AbstractValidator<EasyPayTSQQuery>
{
    public EasyPayTSQQueryValidator()
    {
        RuleFor(x => x.SessionId)
            .NotEmpty()
            .WithMessage("Session ID is required")
            .MaximumLength(50)
            .WithMessage("Session ID cannot exceed 50 characters");
    }
}

public class EasyPayTSQQueryHandler : IRequestHandler<EasyPayTSQQuery, EasyPayTSQResultDto>
{
    private readonly IEasyPayTransferService _easyPayService;
    private readonly ILogger<EasyPayTSQQueryHandler> _logger;

    public EasyPayTSQQueryHandler(
        IEasyPayTransferService easyPayService,
        ILogger<EasyPayTSQQueryHandler> logger)
    {
        _easyPayService = easyPayService ?? throw new ArgumentNullException(nameof(easyPayService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<EasyPayTSQResultDto> Handle(EasyPayTSQQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing EasyPay TSQ for session {SessionId}", request.SessionId);

        try
        {
            var result = await _easyPayService.TransactionStatusQueryAsync(request.SessionId, cancellationToken);

            _logger.LogInformation("EasyPay TSQ completed for session {SessionId}. Success: {IsSuccessful}, Status: {Status}", 
                request.SessionId, result.IsSuccessful, result.Status);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing EasyPay TSQ for session {SessionId}", request.SessionId);
            throw;
        }
    }
}
