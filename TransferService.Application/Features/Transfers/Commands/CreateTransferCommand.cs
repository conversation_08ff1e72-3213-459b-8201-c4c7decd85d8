using MediatR;
using Microsoft.Extensions.Logging;
using FluentValidation;
using AutoMapper;
using TransferService.Domain.Entities;
using TransferService.Domain.Interfaces;
using TransferService.Domain.Enums;
using TransferService.Application.Features.Transfers.Models;

namespace TransferService.Application.Features.Transfers.Commands;

public record CreateTransferCommand : IRequest<TransferDto>
{
    public string FromAccount { get; init; } = string.Empty;
    public string ToAccount { get; init; } = string.Empty;
    public decimal Amount { get; init; }
    public string Currency { get; init; } = "NGN";
    public string Description { get; init; } = string.Empty;
}

public class CreateTransferCommandValidator : AbstractValidator<CreateTransferCommand>
{
    public CreateTransferCommandValidator()
    {
        RuleFor(x => x.FromAccount)
            .NotEmpty()
            .WithMessage("From account is required")
            .Length(5, 20)
            .WithMessage("From account must be between 5 and 20 characters");

        RuleFor(x => x.ToAccount)
            .NotEmpty()
            .WithMessage("To account is required")
            .Length(5, 20)
            .WithMessage("To account must be between 5 and 20 characters")
            .NotEqual(x => x.FromAccount)
            .WithMessage("To account cannot be the same as from account");

        RuleFor(x => x.Amount)
            .GreaterThan(0)
            .WithMessage("Amount must be greater than zero")
            .LessThanOrEqualTo(1000000)
            .WithMessage("Amount cannot exceed 1,000,000");

        RuleFor(x => x.Currency)
            .NotEmpty()
            .WithMessage("Currency is required")
            .Length(3)
            .WithMessage("Currency must be 3 characters")
            .Must(BeValidCurrency)
            .WithMessage("Currency must be a valid ISO currency code");

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage("Description cannot exceed 500 characters");
    }

    private static bool BeValidCurrency(string currency)
    {
        var validCurrencies = new[] { "NGN", "USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CHF", "CNY" };
        return validCurrencies.Contains(currency.ToUpper());
    }
}

public class CreateTransferCommandHandler(
    ITransferApiService transferApiService,
    IMapper mapper,
    ILogger<CreateTransferCommandHandler> logger)
    : IRequestHandler<CreateTransferCommand, TransferDto>
{
    public async Task<TransferDto> Handle(CreateTransferCommand request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Processing transfer request from {FromAccount} to {ToAccount} for {Amount} {Currency}",
            request.FromAccount, request.ToAccount, request.Amount, request.Currency);

        // Create transfer entity
        var transfer = new TransferRequest
        {
            FromAccount = request.FromAccount,
            ToAccount = request.ToAccount,
            Amount = request.Amount,
            Currency = request.Currency,
            Description = request.Description,
            Status = TransferStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };

        try
        {
            // Update status to processing
            transfer.Status = TransferStatus.Processing;

            // Call external API
            var externalTransactionId = await transferApiService.ProcessTransferAsync(
                request.FromAccount,
                request.ToAccount,
                request.Amount,
                request.Currency,
                cancellationToken);

            // Update transfer with external transaction ID
            transfer.ExternalTransactionId = externalTransactionId;
            transfer.Status = TransferStatus.Completed;
            transfer.ProcessedAt = DateTime.UtcNow;

            logger.LogInformation("Transfer completed successfully. External Transaction ID: {ExternalTransactionId}",
                externalTransactionId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Transfer failed for request from {FromAccount} to {ToAccount}",
                request.FromAccount, request.ToAccount);

            transfer.Status = TransferStatus.Failed;
            transfer.ErrorMessage = ex.Message;
        }

        // Map to DTO and return
        return mapper.Map<TransferDto>(transfer);
    }
}
