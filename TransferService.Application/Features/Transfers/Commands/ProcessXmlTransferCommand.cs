using MediatR;
using Microsoft.Extensions.Logging;
using FluentValidation;
using AutoMapper;
using TransferService.Domain.Entities;
using TransferService.Domain.Interfaces;
using TransferService.Domain.Enums;
using TransferService.Application.Features.Transfers.Models;

namespace TransferService.Application.Features.Transfers.Commands;

public record ProcessXmlTransferCommand : IRequest<XmlTransferResponseDto>
{
    public string FromAccount { get; init; } = string.Empty;
    public string ToAccount { get; init; } = string.Empty;
    public decimal Amount { get; init; }
    public string Currency { get; init; } = "NGN";
    public string Description { get; init; } = string.Empty;
    public string RequestId { get; init; } = string.Empty;
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
}

public class ProcessXmlTransferCommandValidator : AbstractValidator<ProcessXmlTransferCommand>
{
    public ProcessXmlTransferCommandValidator()
    {
        RuleFor(x => x.FromAccount)
            .NotEmpty()
            .WithMessage("From account is required")
            .Length(5, 20)
            .WithMessage("From account must be between 5 and 20 characters");

        RuleFor(x => x.ToAccount)
            .NotEmpty()
            .WithMessage("To account is required")
            .Length(5, 20)
            .WithMessage("To account must be between 5 and 20 characters")
            .NotEqual(x => x.FromAccount)
            .WithMessage("To account cannot be the same as from account");

        RuleFor(x => x.Amount)
            .GreaterThan(0)
            .WithMessage("Amount must be greater than zero")
            .LessThanOrEqualTo(1000000)
            .WithMessage("Amount cannot exceed 1,000,000");

        RuleFor(x => x.Currency)
            .NotEmpty()
            .WithMessage("Currency is required")
            .Length(3)
            .WithMessage("Currency must be 3 characters")
            .Must(BeValidCurrency)
            .WithMessage("Currency must be a valid ISO currency code");

        RuleFor(x => x.RequestId)
            .NotEmpty()
            .WithMessage("Request ID is required");

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage("Description cannot exceed 500 characters");
    }

    private static bool BeValidCurrency(string currency)
    {
        var validCurrencies = new[] { "NGN", "USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CHF", "CNY" };
        return validCurrencies.Contains(currency.ToUpper());
    }
}

public class ProcessXmlTransferCommandHandler : IRequestHandler<ProcessXmlTransferCommand, XmlTransferResponseDto>
{
    private readonly ITransferApiService _transferApiService;
    private readonly IMapper _mapper;
    private readonly ILogger<ProcessXmlTransferCommandHandler> _logger;

    public ProcessXmlTransferCommandHandler(
        ITransferApiService transferApiService,
        IMapper mapper,
        ILogger<ProcessXmlTransferCommandHandler> logger)
    {
        _transferApiService = transferApiService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<XmlTransferResponseDto> Handle(ProcessXmlTransferCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing XML transfer request {RequestId} from {FromAccount} to {ToAccount} for {Amount} {Currency}",
            request.RequestId, request.FromAccount, request.ToAccount, request.Amount, request.Currency);

        // Create transfer entity
        var transfer = new TransferRequest
        {
            FromAccount = request.FromAccount,
            ToAccount = request.ToAccount,
            Amount = request.Amount,
            Currency = request.Currency,
            Description = request.Description,
            Status = TransferStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };

        var response = new XmlTransferResponseDto
        {
            TransactionId = Guid.NewGuid().ToString(),
            FromAccount = request.FromAccount,
            ToAccount = request.ToAccount,
            Amount = request.Amount,
            Currency = request.Currency,
            RequestId = request.RequestId,
            ProcessedAt = DateTime.UtcNow
        };

        try
        {
            // Update status to processing
            transfer.Status = TransferStatus.Processing;

            // Call external API (simulated for this example)
            var externalTransactionId = await _transferApiService.ProcessTransferAsync(
                request.FromAccount,
                request.ToAccount,
                request.Amount,
                request.Currency,
                cancellationToken);

            // Update transfer with external transaction ID
            transfer.ExternalTransactionId = externalTransactionId;
            transfer.Status = TransferStatus.Completed;
            transfer.ProcessedAt = DateTime.UtcNow;

            // Update response
            response.Status = "SUCCESS";
            response.Message = "Transfer processed successfully";
            response.TransactionId = externalTransactionId;

            _logger.LogInformation("XML transfer completed successfully. Request ID: {RequestId}, Transaction ID: {TransactionId}",
                request.RequestId, externalTransactionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "XML transfer failed for request {RequestId} from {FromAccount} to {ToAccount}",
                request.RequestId, request.FromAccount, request.ToAccount);

            transfer.Status = TransferStatus.Failed;
            transfer.ErrorMessage = ex.Message;

            // Update response with error
            response.Status = "FAILED";
            response.Message = "Transfer processing failed";
            response.ErrorCode = "PROCESSING_ERROR";
            response.ErrorDetails = ex.Message;
        }

        return response;
    }
}
