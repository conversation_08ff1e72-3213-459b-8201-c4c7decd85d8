using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TransferService.Application.Features.Transfers.Models;

// Request/Response DTOs for EasyPay API operations
/// <summary>
/// EasyPay Name Enquiry Request - matches exact API payload structure
/// </summary>
public record EasyPayNameEnquiryRequest
{
    [Required]
    [JsonPropertyName("accountNumber")]
    public string AccountNumber { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("channelCode")]
    public string ChannelCode { get; init; } = "1";

    [Required]
    [JsonPropertyName("destinationInstitutionCode")]
    public string DestinationInstitutionCode { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("transactionId")]
    public string TransactionId { get; init; } = string.Empty;
}

public record EasyPayNameEnquiryResponse
{
    public string AccountName { get; init; } = string.Empty;
    public string AccountNumber { get; init; } = string.Empty;
    public string ResponseCode { get; init; } = string.Empty;
    public string ResponseMessage { get; init; } = string.Empty;
}

/// <summary>
/// EasyPay Fund Transfer Request - matches exact API payload structure
/// </summary>
public record EasyPayTransferRequest
{
    [Required]
    [JsonPropertyName("sourceInstitutionCode")]
    public string SourceInstitutionCode { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("amount")]
    public decimal Amount { get; init; }

    [Required]
    [JsonPropertyName("beneficiaryAccountName")]
    public string BeneficiaryAccountName { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("beneficiaryAccountNumber")]
    public string BeneficiaryAccountNumber { get; init; } = string.Empty;

    [JsonPropertyName("beneficiaryBankVerificationNumber")]
    public string? BeneficiaryBankVerificationNumber { get; init; } = "***********";

    [JsonPropertyName("beneficiaryKYCLevel")]
    public int BeneficiaryKYCLevel { get; init; } = 1;

    [Required]
    [JsonPropertyName("channelCode")]
    public int ChannelCode { get; init; } = 1;

    [Required]
    [JsonPropertyName("originatorAccountName")]
    public string OriginatorAccountName { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("originatorAccountNumber")]
    public string OriginatorAccountNumber { get; init; } = string.Empty;

    [JsonPropertyName("originatorBankVerificationNumber")]
    public long OriginatorBankVerificationNumber { get; init; } = ***********;

    [JsonPropertyName("originatorKYCLevel")]
    public int OriginatorKYCLevel { get; init; } = 1;

    [Required]
    [JsonPropertyName("destinationInstitutionCode")]
    public long DestinationInstitutionCode { get; init; }

    [Required]
    [JsonPropertyName("mandateReferenceNumber")]
    public string MandateReferenceNumber { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("nameEnquiryRef")]
    public string? NameEnquiryRef { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("originatorNarration")]
    public string? OriginatorNarration { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("paymentReference")]
    public string? PaymentReference { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("transactionId")]
    public string? TransactionId { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("transactionLocation")]
    public string? TransactionLocation { get; init; } = "1.38716,3.05117";

    [Required]
    [JsonPropertyName("beneficiaryNarration")]
    public string? BeneficiaryNarration { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("billerId")]
    public string BillerId { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("initiatorAccountNumber")]
    public string InitiatorAccountNumber { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("initiatorAccountName")]
    public string InitiatorAccountName { get; init; } = string.Empty;
}

/// <summary>
/// EasyPay Balance Enquiry Request - matches exact API payload structure
/// </summary>
public record EasyPayBalanceEnquiryRequest
{
    [Required]
    [JsonPropertyName("channelCode")]
    public string ChannelCode { get; init; } = "1";

    [Required]
    [JsonPropertyName("targetAccountName")]
    public string TargetAccountName { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("targetAccountNumber")]
    public string TargetAccountNumber { get; init; } = string.Empty;

    [JsonPropertyName("targetBankVerificationNumber")]
    public string TargetBankVerificationNumber { get; init; } = "***********";

    [Required]
    [JsonPropertyName("authorizationCode")]
    public string AuthorizationCode { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("destinationInstitutionCode")]
    public string DestinationInstitutionCode { get; init; } = "999998";

    [Required]
    [JsonPropertyName("billerId")]
    public string BillerId { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("transactionId")]
    public string TransactionId { get; init; } = string.Empty;
}

public record EasyPayBalanceEnquiryResponse
{
    public string StatusCode { get; init; } = string.Empty;
    public decimal Balance { get; init; }
    public string ResponseMessage { get; init; } = string.Empty;
}

public record EasyPayTSQRequest
{
    [Required]
    public string TransactionID { get; init; } = string.Empty;
}

public record EasyPayTSQResponse
{
    public string StatusCode { get; init; } = string.Empty;
    public decimal Balance { get; init; }
    public string ResponseMessage { get; init; } = string.Empty;
}

// Token management DTOs
public record EasyPayTokenRequest
{
    public string ClientId { get; init; } = string.Empty;
    public string Scope { get; init; } = string.Empty;
    public string ClientSecret { get; init; } = string.Empty;
    public string GrantType { get; init; } = "client_credentials";
}

public record EasyPayTokenResponse
{
    public string AccessToken { get; init; } = string.Empty;
    public string ExpiresIn { get; init; } = string.Empty;
    public string TokenType { get; init; } = "Bearer";
}

/// <summary>
/// Fund Transfer DTO for payment transfer responses
/// </summary>
public record FundTransferDto
{
    [JsonPropertyName("responseCode")]
    public string ResponseCode { get; init; } = string.Empty;

    [JsonPropertyName("sessionID")]
    public string SessionID { get; init; } = string.Empty;

    [JsonPropertyName("transactionId")]
    public string TransactionId { get; init; } = string.Empty;

    [JsonPropertyName("channelCode")]
    public int ChannelCode { get; init; }

    [JsonPropertyName("nameEnquiryRef")]
    public string NameEnquiryRef { get; init; } = string.Empty;

    [JsonPropertyName("destinationInstitutionCode")]
    public string DestinationInstitutionCode { get; init; } = string.Empty;

    [JsonPropertyName("beneficiaryAccountName")]
    public string BeneficiaryAccountName { get; init; } = string.Empty;

    [JsonPropertyName("beneficiaryAccountNumber")]
    public string BeneficiaryAccountNumber { get; init; } = string.Empty;

    [JsonPropertyName("beneficiaryKYCLevel")]
    public string BeneficiaryKYCLevel { get; init; } = string.Empty;

    [JsonPropertyName("beneficiaryBankVerificationNumber")]
    public string BeneficiaryBankVerificationNumber { get; init; } = string.Empty;

    [JsonPropertyName("originatorAccountName")]
    public string OriginatorAccountName { get; init; } = string.Empty;

    [JsonPropertyName("originatorAccountNumber")]
    public string OriginatorAccountNumber { get; init; } = string.Empty;

    [JsonPropertyName("originatorBankVerificationNumber")]
    public string OriginatorBankVerificationNumber { get; init; } = string.Empty;

    [JsonPropertyName("originatorKYCLevel")]
    public string OriginatorKYCLevel { get; init; } = string.Empty;

    [JsonPropertyName("transactionLocation")]
    public string TransactionLocation { get; init; } = string.Empty;

    [JsonPropertyName("narration")]
    public string? Narration { get; init; } = string.Empty;

    [JsonPropertyName("paymentReference")]
    public string PaymentReference { get; init; } = string.Empty;

    [JsonPropertyName("amount")]
    public decimal Amount { get; init; }
}

// Application-level DTOs for EasyPay operations
public record EasyPayNameEnquiryDto
{
    public string AccountNumber { get; init; } = string.Empty;
    public string BankCode { get; init; } = string.Empty;
}

public record EasyPayNameEnquiryResultDto
{
    public string AccountName { get; init; } = string.Empty;
    public string AccountNumber { get; init; } = string.Empty;
    public bool IsSuccessful { get; init; }
    public string ErrorMessage { get; init; } = string.Empty;
    public string TransactionId { get; init; } = string.Empty;
}

public record EasyPayTransferDto
{
    // Core transfer fields (matching TypeScript exactly)
    public decimal Amount { get; init; }
    public string BeneficiaryAccountName { get; init; } = string.Empty;
    public string BeneficiaryAccountNumber { get; init; } = string.Empty;
    public string BeneficiaryBankVerificationNumber { get; init; } = string.Empty;
    public string BeneficiaryKYCLevel { get; init; } = string.Empty;
    public int ChannelCode { get; init; }
    public string OriginatorAccountName { get; init; } = string.Empty;
    public string OriginatorAccountNumber { get; init; } = string.Empty;
    public string OriginatorBankVerificationNumber { get; init; } = string.Empty;
    public string OriginatorKYCLevel { get; init; } = string.Empty;
    public string DestinationInstitutionCode { get; init; } = string.Empty;
    public string TransactionLocation { get; init; } = string.Empty;
    public string OriginatorNarration { get; init; } = string.Empty;
    public string PaymentReference { get; init; } = string.Empty;
    public string BeneficiaryNarration { get; init; } = string.Empty;
    public string BillerId { get; init; } = string.Empty;
    public string MandateReferenceNumber { get; init; } = string.Empty;
    public string SourceInstitutionCode { get; init; } = string.Empty;
    public string TransactionId { get; init; } = string.Empty;
    public string InitiatorAccountName { get; init; } = string.Empty;
    public string InitiatorAccountNumber { get; init; } = string.Empty;

    // Backward compatibility fields (for simple API calls)
    public string AccountNumber { get; init; } = string.Empty;
    public string BankCode { get; init; } = string.Empty;
    public string AccountName { get; init; } = string.Empty;
    public string Narration { get; init; } = string.Empty;
}

public record EasyPayTransferResultDto
{
    public string TransactionId { get; init; } = string.Empty;
    public string Status { get; init; } = string.Empty;
    public bool RequiresProcessing { get; init; }
    public EasyPayTransactionDto? FullResult { get; init; }
    public bool IsSuccessful { get; init; }
    public string ErrorMessage { get; init; } = string.Empty;
}

/// <summary>
/// Transaction DTO for internal processing that matches the TypeScript TransactionDto
/// </summary>
public record EasyPayTransactionDto
{
    public string Amount { get; init; } = string.Empty;
    public string BenAccountName { get; init; } = string.Empty;
    public string BenAccountNumber { get; init; } = string.Empty;
    public string BenBVN { get; init; } = string.Empty;
    public string BenKYC { get; init; } = string.Empty;
    public string ChannelName { get; init; } = string.Empty;
    public string DestinationInstitution { get; init; } = string.Empty;
    public string? Narration { get; init; } = string.Empty;
    public string OrigAccountName { get; init; } = string.Empty;
    public string OrigAccountNumber { get; init; } = string.Empty;
    public string Reference { get; init; } = string.Empty;
    public string OrigBVN { get; init; } = string.Empty;
    public string OrigKYC { get; init; } = string.Empty;
    public string StatusCode { get; init; } = string.Empty;
    public string StatusMessage { get; init; } = string.Empty;
    public string TransactionID { get; init; } = string.Empty;
}

public record EasyPayBalanceDto
{
    public decimal Balance { get; init; }
    public string Status { get; init; } = string.Empty;
    public bool IsSuccessful { get; init; }
    public string ErrorMessage { get; init; } = string.Empty;
}

public record EasyPayTSQDto
{
    public string SessionId { get; init; } = string.Empty;
}

public record EasyPayTSQResultDto
{
    public string Status { get; init; } = string.Empty;
    public bool IsSuccessful { get; init; }
    public string ErrorMessage { get; init; } = string.Empty;
}

public record EasyPayCreditWalletDto
{
    public decimal Amount { get; init; }
}

public record EasyPayCreditWalletResultDto
{
    public string Status { get; init; } = string.Empty;
    public bool IsSuccessful { get; init; }
    public string ErrorMessage { get; init; } = string.Empty;
}
