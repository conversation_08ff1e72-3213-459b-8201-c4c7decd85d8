using System.Xml.Serialization;

namespace TransferService.Application.Features.Transfers.Models;

/// <summary>
/// Generic XML DTOs for all payment services - used by central YARP gateway
/// </summary>

// Name Enquiry XML DTOs
[XmlRoot("NESingleRequest")]
public class NameEnquiryXmlRequest
{
    [XmlElement("SessionID")]
    public string SessionId { get; set; } = string.Empty;

    [XmlElement("DestinationInstitutionCode")]
    public string DestinationInstitutionCode { get; set; } = string.Empty;

    [XmlElement("ChannelCode")]
    public string ChannelCode { get; set; } = "BELEMA_VA";

    [XmlElement("AccountNumber")]
    public string AccountNumber { get; set; } = string.Empty;
}

[XmlRoot("NESingleResponse")]
public class NameEnquiryXmlResponse
{
    [XmlElement("SessionID")]
    public string? SessionId { get; set; } = string.Empty;
    
    [XmlElement("DestinationInstitutionCode")]
    public string DestinationInstitutionCode { get; set; } = string.Empty;
    
    [XmlElement("ChannelCode")]
    public string ChannelCode { get; set; } = string.Empty;
    
    [XmlElement("AccountNumber")]
    public string AccountNumber { get; set; } = string.Empty;
    
    [XmlElement("AccountName")]
    public string AccountName { get; set; } = string.Empty;
    
    [XmlElement("BankVerificationNumber")]
    public string BankVerificationNumber { get; set; } = string.Empty;
    
    [XmlElement("KYCLevel")]
    public string KycLevel { get; set; } = string.Empty;
    
    [XmlElement("ResponseCode")]
    public string ResponseCode { get; set; } = string.Empty;
}

// Transfer XML Models
[XmlRoot("FTSingleCreditRequest")]
public class TransferXmlRequest
{
    [XmlElement("SessionID")]
    public string? SessionId { get; set; } = string.Empty;

    [XmlElement("NameEnquiryRef")]
    public string NameEnquiryRef { get; set; } = string.Empty;

    [XmlElement("DestinationInstitutionCode")]
    public string DestinationInstitutionCode { get; set; } = string.Empty;

    [XmlElement("ChannelCode")]
    public int ChannelCode { get; set; } = 1;

    [XmlElement("BeneficiaryAccountName")]
    public string BeneficiaryAccountName { get; set; } = string.Empty;

    [XmlElement("BeneficiaryAccountNumber")]
    public string BeneficiaryAccountNumber { get; set; } = string.Empty;

    [XmlElement("BeneficiaryBankVerificationNumber")]
    public string BeneficiaryBankVerificationNumber { get; set; } = string.Empty;

    [XmlElement("BeneficiaryKYCLevel")]
    public string BeneficiaryKycLevel { get; set; } = string.Empty;

    [XmlElement("OriginatorAccountName")]
    public string OriginatorAccountName { get; set; } = string.Empty;

    [XmlElement("OriginatorAccountNumber")]
    public string OriginatorAccountNumber { get; set; } = string.Empty;

    [XmlElement("OriginatorBankVerificationNumber")]
    public string OriginatorBankVerificationNumber { get; set; } = string.Empty;

    [XmlElement("OriginatorKYCLevel")]
    public string OriginatorKycLevel { get; set; } = string.Empty;

    [XmlElement("TransactionLocation")]
    public string TransactionLocation { get; set; } = "LAGOS";

    [XmlElement("Narration")]
    public string Narration { get; set; } = string.Empty;

    [XmlElement("PaymentReference")]
    public string PaymentReference { get; set; } = string.Empty;

    [XmlElement("Amount")]
    public string Amount { get; set; } = string.Empty;
}

[XmlRoot("FTSingleCreditResponse")]
public class TransferXmlResponse
{
    [XmlElement("SessionID")]
    public string? SessionId { get; set; } = string.Empty;
    
    [XmlElement("NameEnquiryRef")]
    public string NameEnquiryRef { get; set; } = string.Empty;
    
    [XmlElement("DestinationInstitutionCode")]
    public string DestinationInstitutionCode { get; set; } = string.Empty;
    
    [XmlElement("ChannelCode")]
    public int ChannelCode { get; set; } = 1;
    
    [XmlElement("BeneficiaryAccountName")]
    public string BeneficiaryAccountName { get; set; } = string.Empty;
    
    [XmlElement("BeneficiaryAccountNumber")]
    public string BeneficiaryAccountNumber { get; set; } = string.Empty;
    
    [XmlElement("BeneficiaryBankVerificationNumber")]
    public string BeneficiaryBankVerificationNumber { get; set; } = string.Empty;
    
    [XmlElement("BeneficiaryKYCLevel")]
    public string BeneficiaryKycLevel { get; set; } = string.Empty;
    
    [XmlElement("OriginatorAccountName")]
    public string OriginatorAccountName { get; set; } = string.Empty;
    
    [XmlElement("OriginatorAccountNumber")]
    public string OriginatorAccountNumber { get; set; } = string.Empty;
    
    [XmlElement("OriginatorBankVerificationNumber")]
    public string OriginatorBankVerificationNumber { get; set; } = string.Empty;
    
    [XmlElement("OriginatorKYCLevel")]
    public string OriginatorKycLevel { get; set; } = string.Empty;
    
    [XmlElement("TransactionLocation")]
    public string TransactionLocation { get; set; } = string.Empty;
    
    [XmlElement("Narration")]
    public string? Narration { get; set; } = string.Empty;
    
    [XmlElement("PaymentReference")]
    public string PaymentReference { get; set; } = string.Empty;
    
    [XmlElement("Amount")]
    public string Amount { get; set; } = string.Empty;
    
    [XmlElement("ResponseCode")]
    public string ResponseCode { get; set; } = string.Empty;
    
    [XmlElement("ResponseMessage")]
    public string ResponseMessage { get; set; } = string.Empty;
    
    [XmlElement("TransactionReference")]
    public string TransactionReference { get; set; } = string.Empty;
}
