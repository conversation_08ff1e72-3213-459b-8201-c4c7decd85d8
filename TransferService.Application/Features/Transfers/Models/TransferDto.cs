using TransferService.Domain.Enums;

namespace TransferService.Application.Features.Transfers.Models;

public record TransferDto
{
    public Guid Id { get; init; }
    public string FromAccount { get; init; } = string.Empty;
    public string ToAccount { get; init; } = string.Empty;
    public decimal Amount { get; init; }
    public string Currency { get; init; } = string.Empty;
    public string Description { get; init; } = string.Empty;
    public TransferStatus Status { get; init; }
    public string? ExternalTransactionId { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? ProcessedAt { get; init; }
}

public record CreateTransferRequest
{
    public string FromAccount { get; init; } = string.Empty;
    public string ToAccount { get; init; } = string.Empty;
    public decimal Amount { get; init; }
    public string Currency { get; init; } = "NGN";
    public string Description { get; init; } = string.Empty;
}
