using System.Xml.Serialization;
using TransferService.Domain.Enums;

namespace TransferService.Application.Features.Transfers.Models;

[XmlRoot("TransferRequest")]
public class XmlTransferRequestDto
{
    [XmlElement("FromAccount")]
    public string FromAccount { get; set; } = string.Empty;

    [XmlElement("ToAccount")]
    public string ToAccount { get; set; } = string.Empty;

    [XmlElement("Amount")]
    public decimal Amount { get; set; }

    [XmlElement("Currency")]
    public string Currency { get; set; } = "NGN";

    [XmlElement("Description")]
    public string Description { get; set; } = string.Empty;

    [XmlElement("RequestId")]
    public string RequestId { get; set; } = string.Empty;

    [XmlElement("Timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

[XmlRoot("TransferResponse")]
public class XmlTransferResponseDto
{
    [XmlElement("TransactionId")]
    public string TransactionId { get; set; } = string.Empty;

    [XmlElement("Status")]
    public string Status { get; set; } = string.Empty;

    [XmlElement("Message")]
    public string Message { get; set; } = string.Empty;

    [XmlElement("ProcessedAt")]
    public DateTime ProcessedAt { get; set; }

    [XmlElement("FromAccount")]
    public string FromAccount { get; set; } = string.Empty;

    [XmlElement("ToAccount")]
    public string ToAccount { get; set; } = string.Empty;

    [XmlElement("Amount")]
    public decimal Amount { get; set; }

    [XmlElement("Currency")]
    public string Currency { get; set; } = string.Empty;

    [XmlElement("RequestId")]
    public string RequestId { get; set; } = string.Empty;

    [XmlElement("ErrorCode")]
    public string? ErrorCode { get; set; }

    [XmlElement("ErrorDetails")]
    public string? ErrorDetails { get; set; }
}
