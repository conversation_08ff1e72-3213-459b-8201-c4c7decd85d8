using MediatR;
using Microsoft.Extensions.Logging;
using FluentValidation;
using TransferService.Application.Features.Transfers.Models;

namespace TransferService.Application.Features.Transfers.Queries;

public record GetTransferQuery : IRequest<TransferDto?>
{
    public Guid Id { get; init; }
}

public class GetTransferQueryValidator : AbstractValidator<GetTransferQuery>
{
    public GetTransferQueryValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Transfer ID is required");
    }
}

public class GetTransferQueryHandler(ILogger<GetTransferQueryHandler> logger)
    : IRequestHandler<GetTransferQuery, TransferDto?>
{
    public async Task<TransferDto?> Handle(GetTransferQuery request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Retrieving transfer with ID: {TransferId}", request.Id);
        
        // For now, return a mock transfer
        await Task.Delay(100, cancellationToken); // Simulate database call

        var mockTransfer = new TransferDto
        {
            Id = request.Id,
            FromAccount = "ACC001",
            ToAccount = "ACC002",
            Amount = 1000.00m,
            Currency = "USD",
            Description = "Sample transfer",
            Status = Domain.Enums.TransferStatus.Completed,
            ExternalTransactionId = "EXT-" + Guid.NewGuid().ToString()[..8],
            CreatedAt = DateTime.UtcNow.AddMinutes(-5),
            ProcessedAt = DateTime.UtcNow.AddMinutes(-2)
        };

        logger.LogInformation("Retrieved transfer: {TransferId} with status {Status}", 
            mockTransfer.Id, mockTransfer.Status);

        return mockTransfer;
    }
}

public record GetTransfersQuery : IRequest<IEnumerable<TransferDto>>
{
    public string? FromAccount { get; init; }
    public string? ToAccount { get; init; }
    public int Page { get; init; } = 1;
    public int PageSize { get; init; } = 10;
}

public class GetTransfersQueryValidator : AbstractValidator<GetTransfersQuery>
{
    public GetTransfersQueryValidator()
    {
        RuleFor(x => x.Page)
            .GreaterThan(0)
            .WithMessage("Page must be greater than 0");

        RuleFor(x => x.PageSize)
            .GreaterThan(0)
            .WithMessage("Page size must be greater than 0")
            .LessThanOrEqualTo(100)
            .WithMessage("Page size cannot exceed 100");
    }
}

public class GetTransfersQueryHandler(ILogger<GetTransfersQueryHandler> logger)
    : IRequestHandler<GetTransfersQuery, IEnumerable<TransferDto>>
{
    public async Task<IEnumerable<TransferDto>> Handle(GetTransfersQuery request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Retrieving transfers - Page: {Page}, PageSize: {PageSize}, FromAccount: {FromAccount}, ToAccount: {ToAccount}",
            request.Page, request.PageSize, request.FromAccount, request.ToAccount);

        // In a real implementation, this would query a database with filtering and pagination
        await Task.Delay(200, cancellationToken); // Simulate database call

        var mockTransfers = new List<TransferDto>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromAccount = "ACC001",
                ToAccount = "ACC002",
                Amount = 50000.00m,
                Currency = "NGN",
                Description = "Sample transfer 1 - Nigerian Naira",
                Status = Domain.Enums.TransferStatus.Completed,
                ExternalTransactionId = "EXT-" + Guid.NewGuid().ToString()[..8],
                CreatedAt = DateTime.UtcNow.AddHours(-2),
                ProcessedAt = DateTime.UtcNow.AddHours(-2).AddMinutes(3)
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromAccount = "ACC003",
                ToAccount = "ACC004",
                Amount = 25000.00m,
                Currency = "NGN",
                Description = "Sample transfer 2 - Nigerian Naira",
                Status = Domain.Enums.TransferStatus.Processing,
                ExternalTransactionId = null,
                CreatedAt = DateTime.UtcNow.AddMinutes(-30),
                ProcessedAt = null
            }
        };

        logger.LogInformation("Retrieved {Count} transfers", mockTransfers.Count);

        return mockTransfers;
    }
}
