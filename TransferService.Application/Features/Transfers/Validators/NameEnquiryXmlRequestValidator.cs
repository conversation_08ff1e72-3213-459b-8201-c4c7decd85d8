using FluentValidation;
using TransferService.Application.Features.Transfers.Models;

namespace TransferService.Application.Features.Transfers.Validators;

public class NameEnquiryXmlRequestValidator : AbstractValidator<NameEnquiryXmlRequest>
{
    public NameEnquiryXmlRequestValidator()
    {
        RuleFor(x => x.SessionId)
            .NotEmpty()
            .WithMessage("SessionID is required")
            .MaximumLength(50)
            .WithMessage("SessionID must not exceed 50 characters");

        RuleFor(x => x.DestinationInstitutionCode)
            .NotEmpty()
            .WithMessage("DestinationInstitutionCode is required")
            .MaximumLength(10)
            .WithMessage("DestinationInstitutionCode must not exceed 10 characters");

        RuleFor(x => x.ChannelCode)
            .NotEmpty()
            .WithMessage("ChannelCode is required")
            .MaximumLength(10)
            .WithMessage("ChannelCode must not exceed 10 characters");

        RuleFor(x => x.AccountNumber)
            .NotEmpty()
            .WithMessage("AccountNumber is required")
            .Length(10)
            .WithMessage("AccountNumber must be exactly 10 digits")
            .Matches(@"^\d{10}$")
            .WithMessage("AccountNumber must contain only digits");
    }
}
