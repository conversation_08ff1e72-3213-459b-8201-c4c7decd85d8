using FluentValidation;
using TransferService.Application.Features.Transfers.Models;

namespace TransferService.Application.Features.Transfers.Validators;

public class TransferXmlRequestValidator : AbstractValidator<TransferXmlRequest>
{
    public TransferXmlRequestValidator()
    {
        // RuleFor(x => x.SessionId)
        //     .NotEmpty()
        //     .WithMessage("SessionID is required")
        //     .MaximumLength(50)
        //     .WithMessage("SessionID must not exceed 50 characters");

        // RuleFor(x => x.NameEnquiryRef)
        //     .NotEmpty()
        //     .WithMessage("NameEnquiryRef is required")
        //     .MaximumLength(50)
        //     .WithMessage("NameEnquiryRef must not exceed 50 characters");

        RuleFor(x => x.DestinationInstitutionCode)
            .NotEmpty()
            .WithMessage("DestinationInstitutionCode is required")
            .MaximumLength(10)
            .WithMessage("DestinationInstitutionCode must not exceed 10 characters");

        // RuleFor<string>(x => x.ChannelCode)
        //     //.NotEmpty()
        //     //.WithMessage<TransferXmlRequest, string>(88)
        //     .MaximumLength(10)
        //     .WithMessage("ChannelCode must not exceed 10 characters");

        RuleFor(x => x.BeneficiaryAccountName)
            .NotEmpty()
            .WithMessage("BeneficiaryAccountName is required")
            .MaximumLength(100)
            .WithMessage("BeneficiaryAccountName must not exceed 100 characters");

        RuleFor(x => x.BeneficiaryAccountNumber)
            .NotEmpty()
            .WithMessage("BeneficiaryAccountNumber is required")
            .Length(10)
            .WithMessage("BeneficiaryAccountNumber must be exactly 10 digits")
            .Matches(@"^\d{10}$")
            .WithMessage("BeneficiaryAccountNumber must contain only digits");

        RuleFor(x => x.BeneficiaryBankVerificationNumber)
            .Length(11)
            .WithMessage("BeneficiaryBankVerificationNumber must be exactly 11 digits")
            .Matches(@"^\d{11}$")
            .WithMessage("BeneficiaryBankVerificationNumber must contain only digits")
            .When(x => !string.IsNullOrEmpty(x.BeneficiaryBankVerificationNumber));

        // RuleFor(x => x.BeneficiaryKycLevel)
        //     .NotEmpty()
        //     .WithMessage("BeneficiaryKYCLevel is required")
        //     .InclusiveBetween(1, 3)
        //     .WithMessage("BeneficiaryKYCLevel must be 1, 2, or 3");

        RuleFor(x => x.OriginatorAccountName)
            .NotEmpty()
            .WithMessage("OriginatorAccountName is required")
            .MaximumLength(100)
            .WithMessage("OriginatorAccountName must not exceed 100 characters");

        RuleFor(x => x.OriginatorAccountNumber)
            .NotEmpty()
            .WithMessage("OriginatorAccountNumber is required")
            .Length(10)
            .WithMessage("OriginatorAccountNumber must be exactly 10 digits")
            .Matches(@"^\d{10}$")
            .WithMessage("OriginatorAccountNumber must contain only digits");

        RuleFor(x => x.OriginatorBankVerificationNumber)
            .Length(11)
            .WithMessage("OriginatorBankVerificationNumber must be exactly 11 digits")
            .Matches(@"^\d{11}$")
            .WithMessage("OriginatorBankVerificationNumber must contain only digits")
            .When(x => !string.IsNullOrEmpty(x.OriginatorBankVerificationNumber));

        // RuleFor(x => x.OriginatorKycLevel)
        //     .NotEmpty()
        //     .WithMessage("OriginatorKYCLevel is required")
        //     .InclusiveBetween(1, 3)
        //     .WithMessage("OriginatorKYCLevel must be 1, 2, or 3");

        RuleFor(x => x.TransactionLocation)
            .NotEmpty()
            .WithMessage("TransactionLocation is required")
            .MaximumLength(50)
            .WithMessage("TransactionLocation must not exceed 50 characters");

        RuleFor(x => x.Narration)
            .NotEmpty()
            .WithMessage("Narration is required")
            .MaximumLength(200)
            .WithMessage("Narration must not exceed 200 characters");

        RuleFor(x => x.PaymentReference)
            .NotEmpty()
            .WithMessage("PaymentReference is required")
            .MaximumLength(50)
            .WithMessage("PaymentReference must not exceed 50 characters");

        RuleFor(x => x.Amount)
            .NotEmpty()
            .WithMessage("Amount is required")
            .Matches(@"^\d+(\.\d{1,2})?$")
            .WithMessage("Amount must be a valid decimal number")
            .Must(BeValidAmount)
            .WithMessage("Amount must be between 0.01 and 999,999,999.99");
    }

    private static bool BeValidAmount(string amount)
    {
        if (decimal.TryParse(amount, out var value))
        {
            return value is >= 0.01m and <= 999999999.99m;
        }
        return false;
    }
}
