using MediatR;
using Microsoft.Extensions.Logging;
using FluentValidation;
using AutoMapper;
using TransferService.Domain.Entities;
using TransferService.Domain.Interfaces;
using TransferService.Domain.Enums;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Domain.Interfaces.UnifiedPayment;
using TransferService.Application.Features.UnifiedPayment.Models;
using System.Text.Json;
using Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel;
using System.Xml.Serialization;
using TransferService.Application.Common.Formatters;

namespace TransferService.Application.Features.UnifiedPayment.Command;

public record NameEnqCommand : IRequest<NameEnqDto?>
{
    public string AccountNumber { get; }
    public string BankCode{ get; }
    public NameEnqCommand( string bankcode,string accountNumber)
    {
        AccountNumber = accountNumber;
        BankCode = bankcode;
    }
}

public class NameEnqCommandValidator : AbstractValidator<NameEnqCommand>
{
    public NameEnqCommandValidator()
    {
        RuleFor(x => x.AccountNumber)
            .NotEmpty()
            .WithMessage("Account number is required")
            .MaximumLength(10)
            .WithMessage("Account number must be 10 digits");

        RuleFor(x => x.BankCode).NotEmpty().WithMessage("BankCode required");

    }

}

public class NameEnqCommandHandler(
    IUnifiedPaymentService unifiedPaymentService,
    ILogger<NameEnqCommandHandler> logger)
    : IRequestHandler<NameEnqCommand, NameEnqDto?>
{
    public async Task<NameEnqDto?> Handle(NameEnqCommand request, CancellationToken cancellationToken)
    {
        HttpContent nameEnqResponse = null;
        try
        {
            logger.LogInformation($"Name enquiry to {request.AccountNumber}");
            nameEnqResponse = await unifiedPaymentService.PerformNameEnqiryAsync(request.BankCode,request.AccountNumber, cancellationToken);
            logger.LogInformation($"Name enquiry successful for - {request.AccountNumber}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Name enquiry failed for - {request.AccountNumber}");
        }
        // convert xml to NameEnqDto (no automapping)
        var nameEnqResult = XmlToObject<NameEnqDto>.Convert(nameEnqResponse);
        return nameEnqResult; 
    }
}
