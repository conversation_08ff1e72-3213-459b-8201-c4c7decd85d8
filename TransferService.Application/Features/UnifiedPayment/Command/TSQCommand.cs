using MediatR;
using Microsoft.Extensions.Logging;
using FluentValidation;
using AutoMapper;
using TransferService.Domain.Entities;
using TransferService.Domain.Interfaces;
using TransferService.Domain.Enums;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Domain.Interfaces.UnifiedPayment;
using TransferService.Application.Features.UnifiedPayment.Models;
using System.Text.Json;
using TransferService.Application.Common.Formatters;

namespace TransferService.Application.Features.UnifiedPayment.Command;

public record TSQCommand : IRequest<TsqDto?>
{
    public string TransactionId { get; }
    public TSQCommand(string transactionId)
    {
        TransactionId = transactionId;
    }
}

public class TSQCommandValidator : AbstractValidator<TSQCommand>
{
    public TSQCommandValidator()
    {
        RuleFor(x => x.TransactionId)
            .NotEmpty()
            .WithMessage("Transaction Id is required");

    }

}

public class  TSQCommandHandler(
    IUnifiedPaymentService unifiedPaymentService,
    ILogger< TSQCommandHandler> logger)
    : IRequestHandler<TSQCommand, TsqDto?>
{
    public async Task<TsqDto?> Handle(TSQCommand request, CancellationToken cancellationToken)
    {
        HttpContent tsqEnqResponse = null;
        try
        {
            logger.LogInformation($"Tsq for - {request.TransactionId}");
            tsqEnqResponse = await unifiedPaymentService.TSQEnqiryAsync(request.TransactionId, cancellationToken);

            logger.LogInformation($"Tsq for - {request.TransactionId} is successful");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Tsq for - {request.TransactionId} failed");
        }

        // convert xml to TsqDto (no automapping)
        var tsqResult = XmlToObject<TsqDto>.Convert(tsqEnqResponse);
        return tsqResult; 
    }
}
