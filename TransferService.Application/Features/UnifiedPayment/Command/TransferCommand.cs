using MediatR;
using Microsoft.Extensions.Logging;
using FluentValidation;
using AutoMapper;
using TransferService.Domain.Entities;
using TransferService.Domain.Interfaces;
using TransferService.Domain.Enums;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Domain.Interfaces.UnifiedPayment;
using TransferService.Application.Features.UnifiedPayment.Models;
using System.Text.Json;
using TransferService.Application.Common.Formatters;
using TransferService.Application.Interfaces;
using TransferService.Application.Common.Extensions;
using Microsoft.Extensions.Configuration;

namespace TransferService.Application.Features.UnifiedPayment.Command;

public record TransferCommand : IRequest<TransferCreditDto?>
{
    public PaymentTransferDto transferRequest { get; }
    public TransferCommand(PaymentTransferDto transferRequest)
    {
        this.transferRequest = transferRequest;
    }
}

public class TransferCommandValidator : AbstractValidator<TransferCommand>
{
    public TransferCommandValidator()
    {
        RuleFor(x => x.transferRequest.SessionId)
            .NotEmpty()
            .WithMessage("Transaction Id is required");

        RuleFor(x => x.transferRequest.PaymentReference)
       .NotEmpty()
       .WithMessage("Reference Id is required");

        RuleFor(x => x.transferRequest.AccountNumber)
       .NotEmpty()
       .WithMessage("Beneficiary account number is required");

        RuleFor(x => x.transferRequest.BankCode)
       .NotEmpty()
       .WithMessage("Beneficiary bank number is required");

        RuleFor(x => x.transferRequest.OriginatorAccountName)
       .NotEmpty()
       .WithMessage("Sender name is required");

        RuleFor(x => x.transferRequest.OriginatorAccountNumber)
       .NotEmpty()
       .WithMessage("Sender account number is required");

        RuleFor(x => x.transferRequest.Amount)
       .NotEmpty()
       .WithMessage("Amount  is required");

    }

}

public class TransferCommandHandler(
    IUnifiedPaymentService unifiedPaymentService,
    ILogger<TransferCommandHandler> logger,
    IConfiguration configuration)
    : IRequestHandler<TransferCommand, TransferCreditDto?>
{
    public async Task<Models.TransferCreditDto?> Handle(TransferCommand request, CancellationToken cancellationToken)
    {
        HttpContent transferResponse = null;
        try
        {
            transferResponse = await unifiedPaymentService.ProcessTransferAsync(request.transferRequest, cancellationToken);

            logger.LogInformation("Transfer successful from {FromAccount} to {ToAccount} for {Amount}",
                           request.transferRequest.OriginatorAccountNumber, request.transferRequest.AccountNumber, request.transferRequest.Amount);
        }
        catch (Exception ex)
        {
        }

        //for UP, request should be encrypted. the request body should like this:
                     //<CreditRequest><Data>HEX</Data></CreditRequest>

           // var readContent = await transferResponse.ReadAsStringAsync();
        

        // convert application/xml response to an object TransferCreditDto (no automapping)
        var tsqResult = XmlToObject<TransferCreditDto>.Convert(transferResponse);
        return tsqResult;
    }
}
