using System.ComponentModel.DataAnnotations;
using System.Xml.Serialization;

namespace TransferService.Application.Features.UnifiedPayment.Models;


public abstract record EncryptedData
{
    public string? Data { get; set; }
    public string? ErrorMessage { get; set; }
}

[XmlRoot("NameEnquiryResponse")]
public sealed record NameEnqDto : EncryptedData
{
    public string? ResponseCode { get; set; }

    [Required]
    public string? AccountNumber { get; set; }

    public string? AccountName { get; set; }

    public string? PhoneNumber { get; set; }
    [Required]
    public string? BankCode { get; set; }

}

// public record NameEnquiryResponse
// {
//     public string? Data { get; set; }
// }
[XmlRoot("CreditResponse")]
public record TsqDto:EncryptedData
{

}


[XmlRoot("CreditStatus")]
public record TsqResponse : EncryptedData
{
    public string? RefId { get; set; }
    public string? TrxId { get; set; }

    public string? StatusCode { get; set; }

    public string? StatusMessage { get; set; }
}


[XmlRoot("CreditResponse")]
public sealed record TransferCreditDto : EncryptedData
{

}

[XmlRoot("CreditStatus")]
public sealed record TransferResult : TsqResponse
{

}
