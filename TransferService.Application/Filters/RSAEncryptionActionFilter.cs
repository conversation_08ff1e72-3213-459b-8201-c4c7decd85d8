using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Reflection;
using TransferService.Application.Attributes;
using TransferService.Application.Configuration;
using TransferService.Application.Common.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Filters;

/// <summary>
/// Action filter for RSA encryption/decryption based on attributes
/// </summary>
public class RSAEncryptionActionFilter : IActionFilter
{
    private readonly ILogger<RSAEncryptionActionFilter> _logger;
    private readonly RSAOptions _rsaOptions;
    private readonly IRSACryptographyService _rsaService;

    public RSAEncryptionActionFilter(
        ILogger<RSAEncryptionActionFilter> logger,
        IOptions<RSAOptions> rsaOptions,
        IRSACryptographyService rsaService)
    {
        _logger = logger;
        _rsaOptions = rsaOptions.Value;
        _rsaService = rsaService;
    }

    public void OnActionExecuting(ActionExecutingContext context)
    {
        if (!_rsaOptions.Middleware.Enabled)
        {
            return;
        }

        try
        {
            var rsaAttributes = GetRSAAttributes(context);
            
            // Check if RSA should be bypassed
            if (HasBypassAttribute(context))
            {
                _logger.LogDebug("RSA encryption bypassed for {Action} due to RSABypassAttribute", 
                    context.ActionDescriptor.DisplayName);
                return;
            }

            // Process request decryption if needed
            var decryptAttribute = GetAttribute<RSADecryptedAttribute>(context);
            if (decryptAttribute != null && ShouldDecryptRequest(context, decryptAttribute))
            {
                await DecryptRequestAsync(context, decryptAttribute);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RSA action filter OnActionExecuting for {Action}", 
                context.ActionDescriptor.DisplayName);
            HandleFilterError(context, ex);
        }
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        if (!_rsaOptions.Middleware.Enabled)
        {
            return;
        }

        try
        {
            var rsaAttributes = GetRSAAttributes(context);
            
            // Check if RSA should be bypassed
            if (HasBypassAttribute(context))
            {
                return;
            }

            // Process response encryption if needed
            var encryptAttribute = GetAttribute<RSAEncryptResponseAttribute>(context) ?? 
                                 GetAttribute<RSAEncryptedAttribute>(context);
            
            if (encryptAttribute != null && ShouldEncryptResponse(context, encryptAttribute))
            {
                await EncryptResponseAsync(context, encryptAttribute);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RSA action filter OnActionExecuted for {Action}", 
                context.ActionDescriptor.DisplayName);
            HandleFilterError(context, ex);
        }
    }

    private List<Attribute> GetRSAAttributes(ActionExecutingContext context)
    {
        var attributes = new List<Attribute>();

        // Get attributes from controller
        var controllerAttributes = context.Controller.GetType().GetCustomAttributes<Attribute>();
        attributes.AddRange(controllerAttributes);

        // Get attributes from action
        var actionAttributes = context.ActionDescriptor.EndpointMetadata.OfType<Attribute>();
        attributes.AddRange(actionAttributes);

        return attributes.Where(a => a is RSAEncryptedAttribute or RSADecryptedAttribute or 
                                   RSAEncryptResponseAttribute or RSABypassAttribute).ToList();
    }

    private List<Attribute> GetRSAAttributes(ActionExecutedContext context)
    {
        var attributes = new List<Attribute>();

        // Get attributes from controller
        var controllerAttributes = context.Controller.GetType().GetCustomAttributes<Attribute>();
        attributes.AddRange(controllerAttributes);

        // Get attributes from action
        var actionAttributes = context.ActionDescriptor.EndpointMetadata.OfType<Attribute>();
        attributes.AddRange(actionAttributes);

        return attributes.Where(a => a is RSAEncryptedAttribute or RSADecryptedAttribute or 
                                   RSAEncryptResponseAttribute or RSABypassAttribute).ToList();
    }

    private T? GetAttribute<T>(ActionExecutingContext context) where T : Attribute
    {
        // Check action first, then controller
        var actionAttribute = context.ActionDescriptor.EndpointMetadata.OfType<T>().FirstOrDefault();
        if (actionAttribute != null)
        {
            return actionAttribute;
        }

        return context.Controller.GetType().GetCustomAttribute<T>();
    }

    private T? GetAttribute<T>(ActionExecutedContext context) where T : Attribute
    {
        // Check action first, then controller
        var actionAttribute = context.ActionDescriptor.EndpointMetadata.OfType<T>().FirstOrDefault();
        if (actionAttribute != null)
        {
            return actionAttribute;
        }

        return context.Controller.GetType().GetCustomAttribute<T>();
    }

    private bool HasBypassAttribute(ActionExecutingContext context)
    {
        return GetAttribute<RSABypassAttribute>(context) != null;
    }

    private bool HasBypassAttribute(ActionExecutedContext context)
    {
        return GetAttribute<RSABypassAttribute>(context) != null;
    }

    private bool ShouldDecryptRequest(ActionExecutingContext context, RSADecryptedAttribute attribute)
    {
        var request = context.HttpContext.Request;
        var method = request.Method;
        var contentType = request.ContentType;

        // Check if it's a health check
        if (attribute.BypassHealthChecks && IsHealthCheckRequest(context.HttpContext))
        {
            return false;
        }

        // Check if request has content
        return request.ContentLength > 0 && !string.IsNullOrEmpty(contentType);
    }

    private bool ShouldEncryptResponse(ActionExecutedContext context, Attribute attribute)
    {
        var response = context.HttpContext.Response;
        var contentType = response.ContentType;

        // Check if it's a health check
        if (attribute is RSAEncryptedAttribute encryptedAttr && encryptedAttr.BypassHealthChecks && 
            IsHealthCheckRequest(context.HttpContext))
        {
            return false;
        }

        if (attribute is RSAEncryptResponseAttribute encryptAttr && encryptAttr.BypassHealthChecks && 
            IsHealthCheckRequest(context.HttpContext))
        {
            return false;
        }

        // Check if response has content and matches content types
        return !string.IsNullOrEmpty(contentType) && 
               (attribute is RSAEncryptedAttribute or RSAEncryptResponseAttribute);
    }

    private bool IsHealthCheckRequest(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        return path?.Contains("health") == true || path?.Contains("ping") == true;
    }

    private async Task DecryptRequestAsync(ActionExecutingContext context, RSADecryptedAttribute attribute)
    {
        var request = context.HttpContext.Request;
        var keyIdentifier = attribute.KeyIdentifier ?? GetDefaultKeyIdentifier(context);

        // Read the request body
        request.EnableBuffering();
        using var reader = new StreamReader(request.Body, leaveOpen: true);
        var encryptedBody = await reader.ReadToEndAsync();
        request.Body.Position = 0;

        if (string.IsNullOrEmpty(encryptedBody))
        {
            return;
        }

        // Decrypt the request body
        var decryptResult = await _rsaService.DecryptAsync(encryptedBody, keyIdentifier);
        
        if (!decryptResult.IsSuccess)
        {
            var errorMessage = attribute.DecryptionErrorMessage ?? "Request decryption failed";
            throw new InvalidOperationException($"{errorMessage}: {decryptResult.ErrorMessage}");
        }

        // Replace the request body with decrypted content
        var decryptedBytes = System.Text.Encoding.UTF8.GetBytes(decryptResult.Data!);
        request.Body = new MemoryStream(decryptedBytes);
        request.ContentLength = decryptedBytes.Length;

        _logger.LogDebug("Request decrypted successfully for {Action} using key {KeyId}", 
            context.ActionDescriptor.DisplayName, keyIdentifier);
    }

    private async Task EncryptResponseAsync(ActionExecutedContext context, Attribute attribute)
    {
        var response = context.HttpContext.Response;
        var keyIdentifier = GetKeyIdentifier(attribute, context);

        // Get the response body
        if (context.Result is ObjectResult objectResult)
        {
            var responseBody = System.Text.Json.JsonSerializer.Serialize(objectResult.Value);
            
            if (!string.IsNullOrEmpty(responseBody))
            {
                // Encrypt the response body
                var encryptResult = await _rsaService.EncryptAsync(responseBody, keyIdentifier);
                
                if (!encryptResult.IsSuccess)
                {
                    var errorMessage = GetEncryptionErrorMessage(attribute);
                    throw new InvalidOperationException($"{errorMessage}: {encryptResult.ErrorMessage}");
                }

                // Replace the response with encrypted content
                context.Result = new ContentResult
                {
                    Content = encryptResult.Data,
                    ContentType = "text/plain",
                    StatusCode = response.StatusCode
                };

                _logger.LogDebug("Response encrypted successfully for {Action} using key {KeyId}", 
                    context.ActionDescriptor.DisplayName, keyIdentifier);
            }
        }
    }

    private string GetKeyIdentifier(Attribute attribute, ActionExecutedContext context)
    {
        if (attribute is RSAEncryptedAttribute encryptedAttr && !string.IsNullOrEmpty(encryptedAttr.KeyIdentifier))
        {
            return encryptedAttr.KeyIdentifier;
        }

        if (attribute is RSAEncryptResponseAttribute encryptAttr && !string.IsNullOrEmpty(encryptAttr.KeyIdentifier))
        {
            return encryptAttr.KeyIdentifier;
        }

        return GetDefaultKeyIdentifier(context);
    }

    private string GetDefaultKeyIdentifier(ActionExecutingContext context)
    {
        // Try to get from endpoint configuration
        var path = context.HttpContext.Request.Path.Value?.ToLowerInvariant();
        foreach (var endpoint in _rsaOptions.Endpoints)
        {
            if (path?.StartsWith(endpoint.Key.ToLowerInvariant()) == true)
            {
                return endpoint.Value.KeyIdentifier ?? _rsaOptions.Encryption.DefaultKeyIdentifier ?? "default";
            }
        }

        return _rsaOptions.Encryption.DefaultKeyIdentifier ?? "default";
    }

    private string GetDefaultKeyIdentifier(ActionExecutedContext context)
    {
        // Try to get from endpoint configuration
        var path = context.HttpContext.Request.Path.Value?.ToLowerInvariant();
        foreach (var endpoint in _rsaOptions.Endpoints)
        {
            if (path?.StartsWith(endpoint.Key.ToLowerInvariant()) == true)
            {
                return endpoint.Value.KeyIdentifier ?? _rsaOptions.Encryption.DefaultKeyIdentifier ?? "default";
            }
        }

        return _rsaOptions.Encryption.DefaultKeyIdentifier ?? "default";
    }

    private string GetEncryptionErrorMessage(Attribute attribute)
    {
        if (attribute is RSAEncryptedAttribute encryptedAttr && !string.IsNullOrEmpty(encryptedAttr.EncryptionErrorMessage))
        {
            return encryptedAttr.EncryptionErrorMessage;
        }

        if (attribute is RSAEncryptResponseAttribute encryptAttr && !string.IsNullOrEmpty(encryptAttr.EncryptionErrorMessage))
        {
            return encryptAttr.EncryptionErrorMessage;
        }

        return "Response encryption failed";
    }

    private void HandleFilterError(ActionExecutingContext context, Exception ex)
    {
        var errorAction = _rsaOptions.Middleware.ErrorHandling.OnError;
        
        switch (errorAction)
        {
            case RSAErrorAction.ReturnError:
                context.Result = new BadRequestObjectResult(new
                {
                    error = "RSA Processing Error",
                    message = _rsaOptions.Middleware.ErrorHandling.IncludeDetailsInErrors ? ex.Message : "Request processing failed"
                });
                break;

            case RSAErrorAction.Bypass:
                // Continue without RSA processing
                break;

            case RSAErrorAction.ThrowException:
                throw;

            default:
                throw;
        }
    }

    private void HandleFilterError(ActionExecutedContext context, Exception ex)
    {
        var errorAction = _rsaOptions.Middleware.ErrorHandling.OnError;
        
        switch (errorAction)
        {
            case RSAErrorAction.ReturnError:
                context.Result = new BadRequestObjectResult(new
                {
                    error = "RSA Processing Error",
                    message = _rsaOptions.Middleware.ErrorHandling.IncludeDetailsInErrors ? ex.Message : "Response processing failed"
                });
                break;

            case RSAErrorAction.Bypass:
                // Continue without RSA processing
                break;

            case RSAErrorAction.ThrowException:
                throw;

            default:
                throw;
        }
    }
} 