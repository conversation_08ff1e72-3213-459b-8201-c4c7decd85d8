namespace TransferService.Application.Interfaces;

/// <summary>
/// Centralized caching service interface for application-wide caching operations
/// </summary>
public interface ICacheService
{
    /// <summary>
    /// Gets a cached value by key
    /// </summary>
    /// <typeparam name="T">Type of the cached value</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached value if exists, default(T) otherwise</returns>
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Tries to get a cached value by key
    /// </summary>
    /// <typeparam name="T">Type of the cached value</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cache result with value and success indicator</returns>
    Task<CacheResult<T>> TryGetAsync<T>(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets a value in cache with specified duration
    /// </summary>
    /// <typeparam name="T">Type of the value to cache</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="value">Value to cache</param>
    /// <param name="duration">Cache duration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SetAsync<T>(string key, T value, TimeSpan duration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets a value in cache with advanced options
    /// </summary>
    /// <typeparam name="T">Type of the value to cache</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="value">Value to cache</param>
    /// <param name="options">Cache entry options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SetAsync<T>(string key, T value, CacheEntryOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets or sets a value in cache using a factory function
    /// </summary>
    /// <typeparam name="T">Type of the value</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="factory">Factory function to create the value if not cached</param>
    /// <param name="duration">Cache duration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached or newly created value</returns>
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan duration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets or sets a value in cache using a factory function with advanced options
    /// </summary>
    /// <typeparam name="T">Type of the value</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="factory">Factory function to create the value if not cached</param>
    /// <param name="options">Cache entry options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached or newly created value</returns>
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, CacheEntryOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes a value from cache
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes multiple values from cache by pattern
    /// </summary>
    /// <param name="pattern">Key pattern (supports wildcards)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a key exists in cache
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if key exists, false otherwise</returns>
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Refreshes a cached value by calling the factory function
    /// </summary>
    /// <typeparam name="T">Type of the value</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="factory">Factory function to create the new value</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RefreshAsync<T>(string key, Func<Task<T>> factory, CancellationToken cancellationToken = default);
}

/// <summary>
/// Cache result wrapper
/// </summary>
/// <typeparam name="T">Type of the cached value</typeparam>
public record CacheResult<T>
{
    public T? Value { get; init; }
    public bool HasValue { get; init; }
    public bool IsStale { get; init; }
    public DateTime? LastModified { get; init; }

    public static CacheResult<T> Hit(T value, bool isStale = false, DateTime? lastModified = null) =>
        new() { Value = value, HasValue = true, IsStale = isStale, LastModified = lastModified };

    public static CacheResult<T> Miss() =>
        new() { Value = default, HasValue = false, IsStale = false, LastModified = null };
}

/// <summary>
/// Cache entry options for advanced caching scenarios
/// </summary>
public record CacheEntryOptions
{
    /// <summary>
    /// Cache duration
    /// </summary>
    public TimeSpan Duration { get; init; } = TimeSpan.FromMinutes(30);

    /// <summary>
    /// Cache priority for memory pressure situations
    /// </summary>
    public CachePriority Priority { get; init; } = CachePriority.Normal;

    /// <summary>
    /// Size of the cache entry (for memory management)
    /// </summary>
    public long Size { get; init; } = 1;

    /// <summary>
    /// Enable fail-safe mode (serve stale data if source is unavailable)
    /// </summary>
    public bool EnableFailSafe { get; init; } = true;

    /// <summary>
    /// Maximum duration to serve stale data
    /// </summary>
    public TimeSpan FailSafeMaxDuration { get; init; } = TimeSpan.FromHours(1);

    /// <summary>
    /// Enable eager refresh (refresh before expiration)
    /// </summary>
    public bool EnableEagerRefresh { get; init; } = true;

    /// <summary>
    /// Threshold for eager refresh (0.0 to 1.0)
    /// </summary>
    public float EagerRefreshThreshold { get; init; } = 0.8f;

    /// <summary>
    /// Tags for cache invalidation
    /// </summary>
    public string[]? Tags { get; init; }

    /// <summary>
    /// Default cache options for tokens
    /// </summary>
    public static CacheEntryOptions ForTokens => new()
    {
        Duration = TimeSpan.FromMinutes(55), // Slightly less than typical 1-hour token expiration
        Priority = CachePriority.High,
        Size = 1,
        EnableFailSafe = true,
        FailSafeMaxDuration = TimeSpan.FromHours(2),
        EnableEagerRefresh = true,
        EagerRefreshThreshold = 0.9f, // Refresh when 90% of duration has passed
        Tags = new[] { "tokens", "authentication" }
    };

    /// <summary>
    /// Default cache options for user data
    /// </summary>
    public static CacheEntryOptions ForUserData => new()
    {
        Duration = TimeSpan.FromMinutes(15),
        Priority = CachePriority.Normal,
        Size = 10,
        EnableFailSafe = true,
        FailSafeMaxDuration = TimeSpan.FromHours(1),
        EnableEagerRefresh = true,
        EagerRefreshThreshold = 0.8f,
        Tags = new[] { "users", "data" }
    };

    /// <summary>
    /// Default cache options for configuration data
    /// </summary>
    public static CacheEntryOptions ForConfiguration => new()
    {
        Duration = TimeSpan.FromHours(1),
        Priority = CachePriority.High,
        Size = 5,
        EnableFailSafe = true,
        FailSafeMaxDuration = TimeSpan.FromDays(1),
        EnableEagerRefresh = true,
        EagerRefreshThreshold = 0.7f,
        Tags = new[] { "configuration", "settings" }
    };
}

/// <summary>
/// Cache priority levels
/// </summary>
public enum CachePriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}
