namespace TransferService.Application.Interfaces;

/// <summary>
/// Interface for EasyPay OAuth2 token management
/// </summary>
public interface IEasyPayTokenService
{
    /// <summary>
    /// Gets a valid access token, refreshing if necessary
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Valid access token</returns>
    Task<string> GetAccessTokenAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Forces token refresh
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>New access token</returns>
    Task<string> RefreshTokenAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Clears cached token
    /// </summary>
    void ClearToken();
}
