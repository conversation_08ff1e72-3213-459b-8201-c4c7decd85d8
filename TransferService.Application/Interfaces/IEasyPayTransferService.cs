using TransferService.Application.Features.EasyPay.Commands;
using TransferService.Application.Features.Transfers.Models;

namespace TransferService.Application.Interfaces;

/// <summary>
/// Interface for NIBSS EasyPay Transfer Service operations
/// </summary>
public interface IEasyPayTransferService
{
    /// <summary>
    /// Performs name enquiry to validate account details
    /// </summary>
    /// <param name="accountNumber">The account number to validate</param>
    /// <param name="bankCode">The bank code/institution code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Account name and number if successful</returns>
    Task<EasyPayNameEnquiryResultDto> NameEnquiryAsync(string accountNumber, string bankCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs fund transfer operation with full payment command including originator details
    /// </summary>
    /// <param name="request">Payment transfer command with all transfer details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Transfer result with transaction details</returns>
    Task<EasyPayTransferResultDto> TransferAsync(EasyPayTransferCommand request, CancellationToken cancellationToken = default);


    /// <summary>
    /// Queries wallet balance
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current wallet balance</returns>
    Task<EasyPayBalanceDto> GetWalletBalanceAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Credits wallet with specified amount
    /// </summary>
    /// <param name="amount">Amount to credit</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Credit operation result</returns>
    Task<EasyPayCreditWalletResultDto> CreditWalletAsync(decimal amount, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs Transaction Status Query (TSQ)
    /// </summary>
    /// <param name="sessionId">Session ID to query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Transaction status</returns>
    Task<EasyPayTSQResultDto> TransactionStatusQueryAsync(string sessionId, CancellationToken cancellationToken = default);
}
