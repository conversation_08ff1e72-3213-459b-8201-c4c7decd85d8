namespace TransferService.Application.Interfaces;

/// <summary>
/// Service for handling encrypted XML request/response processing
/// </summary>
public interface IEncryptedXmlService
{
    /// <summary>
    /// Decrypts and deserializes an encrypted XML payload
    /// </summary>
    /// <typeparam name="T">Type to deserialize to</typeparam>
    /// <param name="encryptedXmlPayload">Encrypted base64 XML payload</param>
    /// <returns>Deserialized object or null if failed</returns>
    Task<T?> DecryptAndDeserializeAsync<T>(string encryptedXmlPayload) where T : class;

    /// <summary>
    /// Serializes and encrypts an object to XML
    /// </summary>
    /// <typeparam name="T">Type of object to serialize</typeparam>
    /// <param name="obj">Object to serialize and encrypt</param>
    /// <returns>Encrypted base64 XML string</returns>
    Task<string> SerializeAndEncryptAsync<T>(T obj) where T : class;

    /// <summary>
    /// Creates an encrypted error response
    /// </summary>
    /// <typeparam name="T">Type of error response</typeparam>
    /// <param name="errorResponse">Error response object</param>
    /// <returns>Encrypted error response</returns>
    Task<string> CreateEncryptedErrorResponseAsync<T>(T errorResponse) where T : class;
}
