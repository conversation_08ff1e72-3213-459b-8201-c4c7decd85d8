namespace TransferService.Application.Interfaces;

/// <summary>
/// PGP encryption service for secure message encryption and decryption
/// Uses standard PGP encryption to support payloads of any size
/// </summary>
public interface IPGPService
{
    /// <summary>
    /// Encrypts data using PGP encryption
    /// Supports payloads of any size using hybrid encryption
    /// </summary>
    /// <param name="plainText">Text to encrypt</param>
    /// <returns>PGP encrypted data as base64 string</returns>
    string Encrypt(string plainText);

    /// <summary>
    /// Decrypts PGP encrypted data
    /// </summary>
    /// <param name="encryptedData">PGP encrypted data as base64 string</param>
    /// <returns>Decrypted plain text</returns>
    string Decrypt(string encryptedData);

    /// <summary>
    /// Signs data using your private key
    /// </summary>
    /// <param name="data">Data to sign</param>
    /// <returns>Signature as base64 string</returns>
    string Sign(string data);

    /// <summary>
    /// Verifies signature using third-party public key
    /// </summary>
    /// <param name="data">Original data</param>
    /// <param name="signature">Signature to verify</param>
    /// <returns>True if signature is valid</returns>
    bool VerifySignature(string data, string signature);
}
