using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using TransferService.Application.Features.UnifiedPayment.Models;

namespace TransferService.Application.Interfaces;

/// <summary>
/// Payment gateway service interface for routing transfers to appropriate providers
/// </summary>
public interface IPaymentGatewayService
{
    /// <summary>
    /// Process transfer through the payment gateway with load balancing and health checks
    /// </summary>
    Task<PaymentTransferResultDto> ProcessTransferAsync(PaymentTransferDto request, CancellationToken cancellationToken);
    /// <summary>
    /// Process name enquiry through the payment gateway
    /// </summary>
    Task<PaymentNameEnquiryResultDto> ProcessNameEnquiryAsync(PaymentNameEnquiryCommand request, CancellationToken cancellationToken);

    Task<TransferStatusResultDto> ProcessTSQEnquiryAsync(TransferStatusDto transfer, CancellationToken cancellationToken);

}

/// <summary>
/// Payment provider status information
/// </summary>
public record PaymentProviderStatus
{
    public string Provider { get; init; } = string.Empty;
    public bool IsHealthy { get; init; }
    public bool IsEnabled { get; init; }
    public int Priority { get; init; }
    public DateTime LastHealthCheck { get; init; }
}

/// <summary>
/// Payment name enquiry command
/// </summary>
public record PaymentNameEnquiryCommand
{
    public string AccountNumber { get; init; } = string.Empty;
    public string BankCode { get; init; } = string.Empty;
    public string? PreferredProvider { get; init; }
}

/// <summary>
/// Payment name enquiry result
/// </summary>
public record PaymentNameEnquiryResultDto
{
    public bool IsSuccessful { get; init; }
    public string? AccountName { get; init; }
    public string? ErrorMessage { get; init; }
    public string? Provider { get; init; }
    public string? TransactionId { get; init; }
}

/// <summary>
/// Enhanced result DTO with provider information and full transfer details
/// </summary>
public record PaymentTransferResultDto
{
    public bool IsSuccessful { get; init; }
    public string? TransactionId { get; init; }
    public string? SessionId { get; init; }
    public string? ErrorMessage { get; init; }
    public string? Provider { get; init; }

    /// <summary>
    /// Full transfer details for the completed transaction
    /// </summary>
    public Features.Transfers.Models.FundTransferDto? TransferDetails { get; init; }
}

public record TransferStatusDto
{
    [Required]
    public required string TransactionId { get; set; }
    public string? PreferredProvider { get; set; }
}

public record PaymentTransferDto
{
    public string? SessionId { get; init; }
    public int ChannelCode { get; init; }
    public decimal Amount { get; init; }
    public string AccountNumber { get; init; } = string.Empty;
    public string BankCode { get; init; } = string.Empty;
    public string AccountName { get; init; } = string.Empty;
    public string? Narration { get; init; } = string.Empty;
    public string? PreferredProvider { get; init; } // Optional: force specific provider

    // Originator information (sender details)
    public string OriginatorAccountName { get; init; } = string.Empty;
    public string OriginatorAccountNumber { get; init; } = string.Empty;
    public string? OriginatorBankVerificationNumber { get; init; } = string.Empty;
    public string? OriginatorKycLevel { get; init; } = string.Empty;

    // Additional transfer details
    public string? TransactionLocation { get; init; } = string.Empty;
    public string? PaymentReference { get; init; } = string.Empty;
    public string? NameEnquiryRef { get; init; } = string.Empty;
}

public record TransferStatusResultDto
{
    public string? TransactionId { get; set; }

    public string? ResponseCode { get; set; }

    public bool? IsSuccessful { get; set; }

    public string? Description { get; set; }

    public string? RefId { get; set; }

    public string? ErrorMessage { get; init; }

    public string? Provider { get; init; }

}