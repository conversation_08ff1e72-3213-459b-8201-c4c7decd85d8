using TransferService.Application.Common.Models;

namespace TransferService.Application.Interfaces;

/// <summary>
/// Interface for RSA cryptography operations providing encryption and decryption services
/// </summary>
public interface IRSACryptographyService
{
    /// <summary>
    /// Encrypts a plain text string using RSA public key
    /// </summary>
    /// <param name="plainText">The text to encrypt</param>
    /// <param name="keyIdentifier">Optional key identifier for multi-key scenarios</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Encrypted data as base64 string</returns>
    Task<RSAOperationResult<string>> EncryptAsync(string plainText, string? keyIdentifier = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Decrypts an encrypted string using RSA private key
    /// </summary>
    /// <param name="encryptedText">The encrypted text as base64 string</param>
    /// <param name="keyIdentifier">Optional key identifier for multi-key scenarios</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Decrypted plain text</returns>
    Task<RSAOperationResult<string>> DecryptAsync(string encryptedText, string? keyIdentifier = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Encrypts an object by serializing it to JSON/XML and then encrypting
    /// </summary>
    /// <typeparam name="T">Type of object to encrypt</typeparam>
    /// <param name="obj">Object to encrypt</param>
    /// <param name="serializationFormat">Serialization format (JSON or XML)</param>
    /// <param name="keyIdentifier">Optional key identifier for multi-key scenarios</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Encrypted data as base64 string</returns>
    Task<RSAOperationResult<string>> EncryptObjectAsync<T>(T obj, SerializationFormat serializationFormat = SerializationFormat.Json, string? keyIdentifier = null, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Decrypts and deserializes an encrypted object
    /// </summary>
    /// <typeparam name="T">Type of object to decrypt to</typeparam>
    /// <param name="encryptedData">Encrypted data as base64 string</param>
    /// <param name="serializationFormat">Serialization format (JSON or XML)</param>
    /// <param name="keyIdentifier">Optional key identifier for multi-key scenarios</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Decrypted and deserialized object</returns>
    Task<RSAOperationResult<T>> DecryptObjectAsync<T>(string encryptedData, SerializationFormat serializationFormat = SerializationFormat.Json, string? keyIdentifier = null, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Generates a new RSA key pair
    /// </summary>
    /// <param name="keySize">Key size in bits (2048, 3072, or 4096)</param>
    /// <param name="keyIdentifier">Identifier for the key pair</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generated key pair information</returns>
    Task<RSAOperationResult<RSAKeyPairInfo>> GenerateKeyPairAsync(int keySize = 2048, string? keyIdentifier = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the public key for a given identifier
    /// </summary>
    /// <param name="keyIdentifier">Key identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Public key as PEM string</returns>
    Task<RSAOperationResult<string>> GetPublicKeyAsync(string? keyIdentifier = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates if the RSA service is properly configured and operational
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    Task<RSAOperationResult<RSAHealthStatus>> ValidateServiceHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the maximum data size that can be encrypted with the current key size
    /// </summary>
    /// <param name="keyIdentifier">Optional key identifier</param>
    /// <returns>Maximum data size in bytes</returns>
    int GetMaxDataSize(string? keyIdentifier = null);
}

/// <summary>
/// Serialization format options for object encryption
/// </summary>
public enum SerializationFormat
{
    Json,
    Xml
}
