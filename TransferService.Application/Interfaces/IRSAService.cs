namespace TransferService.Application.Interfaces;

/// <summary>
/// Hybrid RSA+AES service for encryption and decryption operations
/// Supports large payloads using AES encryption with RSA-encrypted keys
/// </summary>
public interface IRSAService
{
    /// <summary>
    /// Encrypts data using hybrid encryption (AES + RSA)
    /// For small data: uses RSA directly
    /// For large data: uses AES encryption with RSA-encrypted key
    /// </summary>
    /// <param name="plainText">Text to encrypt</param>
    /// <returns>Encrypted data as base64 string</returns>
    string Encrypt(string plainText);

    /// <summary>
    /// Decrypts data using hybrid decryption (AES + RSA)
    /// Automatically detects encryption method and decrypts accordingly
    /// </summary>
    /// <param name="encryptedData">Encrypted data as base64 string</param>
    /// <returns>Decrypted plain text</returns>
    string Decrypt(string encryptedData);

    /// <summary>
    /// Signs data using your private key
    /// </summary>
    /// <param name="data">Data to sign</param>
    /// <returns>Signature as base64 string</returns>
    string Sign(string data);

    /// <summary>
    /// Verifies signature using third-party public key
    /// </summary>
    /// <param name="data">Original data</param>
    /// <param name="signature">Signature to verify</param>
    /// <returns>True if signature is valid</returns>
    bool VerifySignature(string data, string signature);
}
