using MediatR;
using TransferService.Application.Features.EasyPay.Commands;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;
using TransferService.Application.Services.Mappers;

namespace TransferService.Application.Services.Handlers;

/// <summary>
/// Handles payment operations for EasyPay provider
/// </summary>
public class EasyPayProviderHandler(
    IMediator mediator,
    EasyPayResultMapper transferMapper,
    EasyPayNameEnquiryResultMapper nameEnquiryMapper,
    IPaymentHealthManager healthManager)
    : IPaymentProviderHandler
{
    public string ProviderName => "EasyPay";

    public async Task<PaymentTransferResultDto> ProcessTransferAsync(PaymentTransferDto request, CancellationToken cancellationToken)
    {
        var easyPayCommand = new EasyPayTransferCommand
        {
            SessionId = request.SessionId,
            ChannelCode = request.ChannelCode,
            Amount = request.Amount,
            AccountNumber = request.AccountNumber,
            BankCode = request.BankCode,
            AccountName = request.AccountName,
            Narration = request.Narration,
            NameEnquiryRef = request.NameEnquiryRef,
            OriginatorAccountName = request.OriginatorAccountName,
            OriginatorAccountNumber = request.OriginatorAccountNumber,
            OriginatorBankVerificationNumber = request.OriginatorBankVerificationNumber,
            OriginatorKycLevel = request.OriginatorKycLevel,
            TransactionLocation = request.TransactionLocation,
            PaymentReference = request.PaymentReference
        };

        try
        {
            var result = await mediator.Send(easyPayCommand, cancellationToken);
            return transferMapper.MapTransferResult(result, ProviderName);
        }
        catch (Exception ex)
        {
            await healthManager.HandleProviderExceptionAsync(ProviderName, ex, cancellationToken);
            throw;
        }
    }

    public async Task<PaymentNameEnquiryResultDto> ProcessNameEnquiryAsync(PaymentNameEnquiryCommand request, CancellationToken cancellationToken)
    {
        var easyPayCommand = new EasyPayNameEnquiryCommand
        {
            AccountNumber = request.AccountNumber,
            BankCode = request.BankCode
        };

        try
        {
            var result = await mediator.Send(easyPayCommand, cancellationToken);
            return nameEnquiryMapper.MapNameEnquiryResult(result, ProviderName);
        }
        catch (Exception ex)
        {
            await healthManager.HandleProviderExceptionAsync(ProviderName, ex, cancellationToken);
            throw;
        }
    }

    public Task<TransferStatusResultDto> ProcessTSQEnquiryAsync(TransferStatusDto transfer, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
}
