using System.Security.Cryptography;
using MediatR;
using Microsoft.Extensions.Configuration;
using TransferService.Application.Common.Extensions;
using TransferService.Application.Common.Formatters;
using TransferService.Application.Features.EasyPay.Commands;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Features.UnifiedPayment.Command;
using TransferService.Application.Features.UnifiedPayment.Models;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;
using TransferService.Application.Services.Mappers;

namespace TransferService.Application.Services.Handlers;

/// <summary>
/// Handles payment operations for EasyPay provider
/// </summary>
public class UnnifiedPaymentProviderHandler(
    IMediator mediator,
    IConfiguration configuration,
    IPaymentHealthManager healthManager)
    : IPaymentProviderHandler
{
    public string ProviderName => "uppayment";

    public async Task<PaymentTransferResultDto> ProcessTransferAsync(PaymentTransferDto request, CancellationToken cancellationToken)
    {
        var command = new TransferCommand(request);

        try
        {
            var result = await mediator.Send(command, cancellationToken);

            //decrypt data to get account number and responsecode
            var decryptedHex = result?.Data?.AES_Decrypt(configuration["ExternalApis:UnifiedPayment:IV"], configuration["ExternalApis:UnifiedPayment:Key"]);

            var sanitizedXml = decryptedHex?.Substring(decryptedHex.IndexOf("<CreditStatus>"));

            TransferResult? response = XmlToObject<TransferResult>.Convert(sanitizedXml);

            return new PaymentTransferResultDto
            {
                IsSuccessful = response?.StatusCode == "00",
                TransactionId = response?.TrxId,
                ErrorMessage = response?.StatusCode != "00" ? response?.ErrorMessage : "",
                Provider = ProviderName
            };
        }
        catch (Exception ex)
        {
            await healthManager.HandleProviderExceptionAsync(ProviderName, ex, cancellationToken);
            throw;
        }
    }

    public async Task<PaymentNameEnquiryResultDto> ProcessNameEnquiryAsync(PaymentNameEnquiryCommand request, CancellationToken cancellationToken)
    {
        var command = new NameEnqCommand(request.BankCode, request.AccountNumber);

        try
        {
            var result = await mediator.Send(command, cancellationToken);

            //decrypt data to get account number and responsecode
            var decryptedHex = result?.Data?.AES_Decrypt(configuration["ExternalApis:UnifiedPayment:IV"], configuration["ExternalApis:UnifiedPayment:Key"]);

            var sanitizedXml = decryptedHex?.Substring(decryptedHex.IndexOf("<NameEnquiryResponse>"));

            NameEnqDto? response = XmlToObject<NameEnqDto>.Convert(sanitizedXml);

            return new PaymentNameEnquiryResultDto
            {
                IsSuccessful = response?.ResponseCode == "00",
                AccountName = response?.AccountName,
                ErrorMessage = response?.ResponseCode != "00" ? response?.ErrorMessage : "",
                Provider = ProviderName
            };
        }
        catch (Exception ex)
        {
            await healthManager.HandleProviderExceptionAsync(ProviderName, ex, cancellationToken);
            throw;
        }
    }

    public async Task<TransferStatusResultDto> ProcessTSQEnquiryAsync(TransferStatusDto transfer, CancellationToken cancellationToken)
    {
        var command = new TSQCommand(transfer.TransactionId);

        try
        {
            var result = await mediator.Send(command, cancellationToken);

            //decrypt data to get account number and responsecode
            var decryptedHex = result?.Data?.AES_Decrypt(configuration["ExternalApis:UnifiedPayment:IV"], configuration["ExternalApis:UnifiedPayment:Key"]);

            var sanitizedXml = decryptedHex?.Substring(decryptedHex.IndexOf("<CreditStatus>"));

            TsqResponse? response = XmlToObject<TsqResponse>.Convert(sanitizedXml);

            return new TransferStatusResultDto
            {
                TransactionId = response?.TrxId,
                IsSuccessful = response?.StatusCode == "00",
                Description = response?.StatusMessage,
                RefId = response?.RefId,
                ErrorMessage = response?.ErrorMessage,
                Provider = ProviderName
            };
        }
        catch (Exception ex)
        {
            await healthManager.HandleProviderExceptionAsync(ProviderName, ex, cancellationToken);
            throw;
        }
    }
}
