using TransferService.Application.Common.Models;

namespace TransferService.Application.Services;

/// <summary>
/// Service for loading RSA keys from configuration
/// </summary>
public interface IRSAKeyLoadingService
{
    /// <summary>
    /// Loads all configured RSA keys from the configuration
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result indicating success/failure and loaded key information</returns>
    Task<RSAOperationResult<List<RSAKeyPairInfo>>> LoadAllConfiguredKeysAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Loads a specific RSA key pair by identifier
    /// </summary>
    /// <param name="keyIdentifier">The key identifier to load</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result indicating success/failure and loaded key information</returns>
    Task<RSAOperationResult<RSAKeyPairInfo>> LoadKeyPairAsync(string keyIdentifier, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates that all configured key files exist and are accessible
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result indicating validation status</returns>
    Task<RSAOperationResult<List<string>>> ValidateKeyFilesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the list of configured key identifiers
    /// </summary>
    /// <returns>List of configured key identifiers</returns>
    IEnumerable<string> GetConfiguredKeyIdentifiers();
} 