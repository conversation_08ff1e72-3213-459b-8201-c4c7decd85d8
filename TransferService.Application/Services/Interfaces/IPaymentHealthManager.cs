namespace TransferService.Application.Services.Interfaces;

/// <summary>
/// Interface for managing payment provider health status
/// </summary>
public interface IPaymentHealthManager
{
    /// <summary>
    /// Handles exceptions and determines if provider should be marked unhealthy
    /// </summary>
    Task HandleProviderExceptionAsync(string provider, Exception exception, CancellationToken cancellationToken);
    
    /// <summary>
    /// Determines if an exception represents a technical failure
    /// </summary>
    bool IsTechnicalFailure(Exception exception);
}
