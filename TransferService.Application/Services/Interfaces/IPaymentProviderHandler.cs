using TransferService.Application.Interfaces;
using TransferService.Domain.Enums;

namespace TransferService.Application.Services.Interfaces;

/// <summary>
/// Interface for handling payment operations for a specific provider
/// </summary>
public interface IPaymentProviderHandler
{
    /// <summary>
    /// The provider name this handler supports
    /// </summary>
    string ProviderName { get; }

    /// <summary>
    /// Processes a transfer request
    /// </summary>
    Task<PaymentTransferResultDto> ProcessTransferAsync(PaymentTransferDto request, CancellationToken cancellationToken);
    /// <summary>
    /// Processes a name enquiry request
    /// </summary>
    Task<PaymentNameEnquiryResultDto> ProcessNameEnquiryAsync(PaymentNameEnquiryCommand request, CancellationToken cancellationToken);

    Task<TransferStatusResultDto> ProcessTSQEnquiryAsync(TransferStatusDto transfer, CancellationToken cancellationToken);

}
