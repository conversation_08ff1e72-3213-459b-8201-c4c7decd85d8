using TransferService.Application.Interfaces;

namespace TransferService.Application.Services.Interfaces;

/// <summary>
/// Interface for routing payments to specific providers
/// </summary>
public interface IPaymentProviderRouter
{
    /// <summary>
    /// Routes a transfer request to the specified provider
    /// </summary>
    Task<PaymentTransferResultDto> RouteTransferAsync(string provider, PaymentTransferDto request, CancellationToken cancellationToken);
    
    /// <summary>
    /// Routes a name enquiry request to the specified provider
    /// </summary>
    Task<PaymentNameEnquiryResultDto> RouteNameEnquiryAsync(string provider, PaymentNameEnquiryCommand request, CancellationToken cancellationToken);

    Task<TransferStatusResultDto> RouteTSQEnquiryAsync(string provider,TransferStatusDto transfer, CancellationToken cancellationToken);
    
    /// <summary>
    /// Gets all supported providers
    /// </summary>
    IEnumerable<string> GetSupportedProviders();
}
