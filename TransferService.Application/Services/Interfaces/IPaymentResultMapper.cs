using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Services.Interfaces;

/// <summary>
/// Interface for mapping provider-specific results to standard payment results
/// </summary>
public interface IPaymentResultMapper<TProviderResult>
{
    /// <summary>
    /// Maps provider result to standard payment transfer result
    /// </summary>
    PaymentTransferResultDto MapTransferResult(TProviderResult providerResult, string provider);
    
    /// <summary>
    /// Maps provider result to standard payment name enquiry result
    /// </summary>
    PaymentNameEnquiryResultDto MapNameEnquiryResult(TProviderResult providerResult, string provider);
}
