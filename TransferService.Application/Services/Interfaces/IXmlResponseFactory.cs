using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Services.Interfaces;

/// <summary>
/// Factory for creating XML response objects
/// </summary>
public interface IXmlResponseFactory
{
    /// <summary>
    /// Creates a validation error response for name enquiry
    /// </summary>
    NameEnquiryXmlResponse CreateNameEnquiryValidationErrorResponse(NameEnquiryXmlRequest request);

    /// <summary>
    /// Creates a validation error response for transfer
    /// </summary>
    TransferXmlResponse CreateTransferValidationErrorResponse(TransferXmlRequest request);

    /// <summary>
    /// Creates a success response for name enquiry
    /// </summary>
    NameEnquiryXmlResponse CreateNameEnquirySuccessResponse(NameEnquiryXmlRequest request, PaymentNameEnquiryResultDto result);

    /// <summary>
    /// Creates a success response for transfer
    /// </summary>
    TransferXmlResponse CreateTransferSuccessResponse(TransferXmlRequest request, PaymentTransferResultDto result);

    /// <summary>
    /// Creates an error response for name enquiry
    /// </summary>
    NameEnquiryXmlResponse CreateNameEnquiryErrorResponse(NameEnquiryXmlRequest request);

    /// <summary>
    /// Creates an error response for transfer
    /// </summary>
    TransferXmlResponse CreateTransferErrorResponse(TransferXmlRequest request);
}
