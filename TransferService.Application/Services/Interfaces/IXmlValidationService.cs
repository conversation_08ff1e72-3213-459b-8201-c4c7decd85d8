using TransferService.Application.Features.Transfers.Models;

namespace TransferService.Application.Services.Interfaces;

/// <summary>
/// Service for validating XML request objects
/// </summary>
public interface IXmlValidationService
{
    /// <summary>
    /// Validates a name enquiry XML request
    /// </summary>
    /// <param name="request">The XML request to validate</param>
    /// <returns>Validation result with success status and error messages</returns>
    Task<ValidationResult> ValidateAsync(NameEnquiryXmlRequest request);

    /// <summary>
    /// Validates a transfer XML request
    /// </summary>
    /// <param name="request">The XML request to validate</param>
    /// <returns>Validation result with success status and error messages</returns>
    Task<ValidationResult> ValidateAsync(TransferXmlRequest request);
}

/// <summary>
/// Result of XML validation
/// </summary>
public record ValidationResult
{
    public bool IsValid { get; init; }
    public IEnumerable<string> Errors { get; init; } = Enumerable.Empty<string>();
}
