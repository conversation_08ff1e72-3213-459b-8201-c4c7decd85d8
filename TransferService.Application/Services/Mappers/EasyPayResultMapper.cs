using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;

namespace TransferService.Application.Services.Mappers;

/// <summary>
/// Maps EasyPay results to standard payment results
/// </summary>
public class EasyPayResultMapper : IPaymentResultMapper<EasyPayTransferResultDto>
{
    public PaymentTransferResultDto MapTransferResult(EasyPayTransferResultDto providerResult, string provider)
    {
        // Map EasyPayTransactionDto to FundTransferDto
        FundTransferDto? transferDetails = null;
        if (providerResult.FullResult != null)
        {
            transferDetails = new FundTransferDto
            {
                ResponseCode = providerResult.FullResult.StatusCode,
                SessionID = providerResult.TransactionId,
                TransactionId = providerResult.TransactionId ?? string.Empty,
                ChannelCode = int.TryParse(providerResult.FullResult.ChannelName, out var channelCode) ? channelCode : 1,
                NameEnquiryRef = providerResult.FullResult.Reference,
                DestinationInstitutionCode = providerResult.FullResult.DestinationInstitution,
                BeneficiaryAccountName = providerResult.FullResult.BenAccountName,
                BeneficiaryAccountNumber = providerResult.FullResult.BenAccountNumber,
                BeneficiaryKYCLevel = providerResult.FullResult.BenKYC,
                BeneficiaryBankVerificationNumber = providerResult.FullResult.BenBVN,
                OriginatorAccountName = providerResult.FullResult.OrigAccountName,
                OriginatorAccountNumber = providerResult.FullResult.OrigAccountNumber,
                OriginatorBankVerificationNumber = providerResult.FullResult.OrigBVN,
                OriginatorKYCLevel = providerResult.FullResult.OrigKYC,
                TransactionLocation = "Nigeria",
                Narration = providerResult.FullResult.Narration,
                PaymentReference = providerResult.FullResult.Reference,
                Amount = Convert.ToDecimal(providerResult.FullResult.Amount)
            };
        }

        return new PaymentTransferResultDto
        {
            IsSuccessful = providerResult.IsSuccessful,
            TransactionId = providerResult.TransactionId,
            SessionId = providerResult.FullResult?.TransactionID,
            ErrorMessage = providerResult.ErrorMessage,
            Provider = provider,
            TransferDetails = transferDetails
        };
    }

    public PaymentNameEnquiryResultDto MapNameEnquiryResult(EasyPayTransferResultDto providerResult, string provider)
    {
        // This mapper is for transfer results, name enquiry would use a different mapper
        throw new NotSupportedException("Use EasyPayNameEnquiryResultMapper for name enquiry results");
    }
}

/// <summary>
/// Maps EasyPay name enquiry results to standard payment results
/// </summary>
public class EasyPayNameEnquiryResultMapper : IPaymentResultMapper<EasyPayNameEnquiryResultDto>
{
    public PaymentTransferResultDto MapTransferResult(EasyPayNameEnquiryResultDto providerResult, string provider)
    {
        throw new NotSupportedException("Use EasyPayResultMapper for transfer results");
    }

    public PaymentNameEnquiryResultDto MapNameEnquiryResult(EasyPayNameEnquiryResultDto providerResult, string provider)
    {
        return new PaymentNameEnquiryResultDto
        {
            IsSuccessful = providerResult.IsSuccessful,
            AccountName = providerResult.AccountName,
            ErrorMessage = providerResult.ErrorMessage,
            Provider = provider,
            TransactionId = providerResult.TransactionId
        };
    }
}
