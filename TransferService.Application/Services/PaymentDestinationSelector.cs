using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TransferService.Application.Configuration;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Services;

/// <summary>
/// Service responsible for selecting payment destinations based on health and load balancing
/// </summary>
public interface IPaymentDestinationSelector
{
    Task<PaymentDestination?> SelectDestinationAsync(string? preferredProvider, CancellationToken cancellationToken);
    Task<bool> IsDestinationHealthyAsync(string destinationKey, CancellationToken cancellationToken);
    Task MarkDestinationUnhealthyAsync(string destinationKey, CancellationToken cancellationToken);
}

public class PaymentDestinationSelector(
    IOptions<PaymentGatewayOptions> gatewayOptions,
    ICacheService cacheService,
    ILogger<PaymentDestinationSelector> logger) : IPaymentDestinationSelector
{
    private readonly PaymentGatewayOptions _gatewayOptions = gatewayOptions.Value;

    public async Task<PaymentDestination?> SelectDestinationAsync(string? preferredProvider, CancellationToken cancellationToken)
    {
        // Check if specific provider requested
        if (!string.IsNullOrEmpty(preferredProvider))
        {
            var preferredDestination = _gatewayOptions.Destinations
                .FirstOrDefault(d => d.Value.Metadata.GetValueOrDefault("provider") == preferredProvider.ToLowerInvariant());
            
            if (preferredDestination.Value is { Enabled: true })
            {
                var isHealthy = await IsDestinationHealthyAsync(preferredDestination.Key, cancellationToken);
                if (isHealthy)
                {
                    return preferredDestination.Value;
                }
            }
        }

        // Get healthy destinations
        var healthyDestinations = new List<(string Key, PaymentDestination Destination)>();
        
        foreach (var dest in _gatewayOptions.Destinations.Where(d => d.Value.Enabled))
        {
            var isHealthy = await IsDestinationHealthyAsync(dest.Key, cancellationToken);
            if (isHealthy)
            {
                healthyDestinations.Add((dest.Key, dest.Value));
            }
        }

        if (healthyDestinations.Count == 0)
        {
            return null;
        }

        // Apply load balancing policy
        return _gatewayOptions.LoadBalancingPolicy.ToLowerInvariant() switch
        {
            "priority" => healthyDestinations.OrderBy(d => d.Destination.Priority).First().Destination,
            "roundrobin" => GetRoundRobinDestination(healthyDestinations),
            "weighted" => GetWeightedDestination(healthyDestinations),
            _ => healthyDestinations.OrderBy(d => d.Destination.Priority).First().Destination
        };
    }

    public async Task<bool> IsDestinationHealthyAsync(string destinationKey, CancellationToken cancellationToken)
    {
        if (!_gatewayOptions.HealthCheck.Enabled)
        {
            return true;
        }

        var cacheKey = $"payment_destination_health_{destinationKey}";
        
        // Check cached health status
        var cachedHealth = await cacheService.TryGetAsync<bool>(cacheKey, cancellationToken);
        if (cachedHealth.HasValue)
        {
            return cachedHealth.Value;
        }

        // Perform health check
        try
        {
            var destination = _gatewayOptions.Destinations[destinationKey];
            var isHealthy = destination.Enabled; // Simplified health check
            
            // Cache result
            var cacheDuration = isHealthy ? _gatewayOptions.HealthCheck.Interval : TimeSpan.FromSeconds(10);
            await cacheService.SetAsync(cacheKey, isHealthy, cacheDuration, cancellationToken);
            
            return isHealthy;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Health check failed for destination {Destination}", destinationKey);
            await cacheService.SetAsync(cacheKey, false, TimeSpan.FromSeconds(10), cancellationToken);
            return false;
        }
    }

    public async Task MarkDestinationUnhealthyAsync(string destinationKey, CancellationToken cancellationToken)
    {
        var cacheKey = $"payment_destination_health_{destinationKey}";
        await cacheService.SetAsync(cacheKey, false, TimeSpan.FromMinutes(1), cancellationToken);
    }

    private static PaymentDestination GetRoundRobinDestination(List<(string Key, PaymentDestination Destination)> destinations)
    {
        // Simple round-robin implementation
        var index = Environment.TickCount % destinations.Count;

        //safe version(from charles)
        //var index = Random.Shared.Next(destinations.Count);
        return destinations[index].Destination;
    }

    private static PaymentDestination GetWeightedDestination(List<(string Key, PaymentDestination Destination)> destinations)
    {
        // Weighted selection based on Weight property
        var totalWeight = destinations.Sum(d => d.Destination.Weight);
        var random = new Random().Next(totalWeight);
        
        var currentWeight = 0;
        foreach (var dest in destinations)
        {
            currentWeight += dest.Destination.Weight;
            if (random < currentWeight)
            {
                return dest.Destination;
            }
        }
        
        return destinations.First().Destination;
    }
}
