using Microsoft.Extensions.Logging;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;

namespace TransferService.Application.Services;

/// <summary>
/// Main payment gateway service that orchestrates payment routing
/// </summary>
public class PaymentGatewayService(
    IPaymentDestinationSelector destinationSelector,
    IPaymentProviderRouter paymentRouter,
    ILogger<PaymentGatewayService> logger) : IPaymentGatewayService
{
    public async Task<PaymentTransferResultDto> ProcessTransferAsync(PaymentTransferDto request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Processing payment transfer of {Amount} to account {AccountNumber} via payment gateway",
            request.Amount, request.AccountNumber);

        // Select destination
        var destination = await destinationSelector.SelectDestinationAsync(request.PreferredProvider, cancellationToken);
        if (destination == null)
        {
            logger.LogError("No healthy payment destinations available");
            return new PaymentTransferResultDto
            {
                IsSuccessful = false,
                ErrorMessage = "No payment providers available"
            };
        }

        logger.LogInformation("Routing payment to destination: {Provider}", destination.Provider);

        // Route to specific provider using the clean router
        return await paymentRouter.RouteTransferAsync(destination.Provider, request, cancellationToken);
    }

    public async Task<PaymentNameEnquiryResultDto> ProcessNameEnquiryAsync(PaymentNameEnquiryCommand request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Processing name enquiry for account {AccountNumber} via payment gateway", request.AccountNumber);

        // Select destination
        var destination = await destinationSelector.SelectDestinationAsync(request.PreferredProvider, cancellationToken);
        if (destination == null)
        {
            logger.LogError("No healthy payment destinations available");
            return new PaymentNameEnquiryResultDto
            {
                IsSuccessful = false,
                ErrorMessage = "No payment providers available"
            };
        }

        logger.LogInformation("Routing name enquiry to destination: {Provider}", destination.Provider);

        // Route to specific provider using the clean router
        return await paymentRouter.RouteNameEnquiryAsync(destination.Provider, request, cancellationToken);
    }

    public async Task<TransferStatusResultDto> ProcessTSQEnquiryAsync(TransferStatusDto transfer, CancellationToken cancellationToken)
    {
        var destination = await destinationSelector.SelectDestinationAsync(transfer.PreferredProvider, cancellationToken);
         if (destination == null)
        {
            logger.LogError("No healthy payment destinations available");
            return new TransferStatusResultDto
            {
                IsSuccessful = false
            };
        }
        return await paymentRouter.RouteTSQEnquiryAsync(destination.Provider, transfer, cancellationToken);
    }
}
