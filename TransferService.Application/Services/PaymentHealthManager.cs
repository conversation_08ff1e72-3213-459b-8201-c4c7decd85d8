using Microsoft.Extensions.Logging;
using System.Net.Http;
using System.Net.Sockets;
using System.Security;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;

namespace TransferService.Application.Services;

/// <summary>
/// Manages payment provider health status based on exception types
/// </summary>
public class PaymentHealthManager(
    IPaymentDestinationSelector destinationSelector,
    ILogger<PaymentHealthManager> logger)
    : IPaymentHealthManager
{
    public async Task HandleProviderExceptionAsync(string provider, Exception exception, CancellationToken cancellationToken)
    {
        if (IsTechnicalFailure(exception))
        {
            logger.LogError(exception, "{Provider} failed due to technical issue, marking destination unhealthy", provider);
            await destinationSelector.MarkDestinationUnhealthyAsync(provider.ToLowerInvariant(), cancellationToken);
        }
        else
        {
            logger.LogWarning(exception, "{Provider} failed due to business logic issue, not marking destination unhealthy", provider);
        }
    }

    public bool IsTechnicalFailure(Exception exception)
    {
        // Technical failures that should mark destination as unhealthy:
        // - Network/connectivity issues
        // - Authentication failures
        // - Service unavailable errors
        // - Timeout exceptions
        // - HTTP client exceptions
        
        return exception switch
        {
            HttpRequestException => true,           // Network/HTTP issues
            TaskCanceledException => true,         // Timeout issues
            SocketException => true,               // Network connectivity issues
            TimeoutException => true,              // Explicit timeout
            UnauthorizedAccessException => true,   // Authentication issues
            SecurityException => true,             // Security/auth issues
            FluentValidation.ValidationException => false, // Business logic validation
            ArgumentException => false,            // Business logic validation
            InvalidOperationException => false,    // Business logic issues
            _ => false                             // Default: don't mark as unhealthy for unknown exceptions
        };
    }
}
