using Microsoft.Extensions.Logging;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;

namespace TransferService.Application.Services;

/// <summary>
/// Clean, enterprise-level payment provider router following SOLID principles
/// </summary>
public class PaymentProviderRouter : IPaymentProviderRouter
{
    private readonly IEnumerable<IPaymentProviderHandler> _providerHandlers;
    private readonly ILogger<PaymentProviderRouter> _logger;
    private readonly Dictionary<string, IPaymentProviderHandler> _handlerMap;

    public PaymentProviderRouter(
        IEnumerable<IPaymentProviderHandler> providerHandlers,
        ILogger<PaymentProviderRouter> logger)
    {
        _providerHandlers = providerHandlers;
        _logger = logger;
        
        // Create a case-insensitive lookup map for providers
        _handlerMap = _providerHandlers.ToDictionary(
            h => h.ProviderName.ToLowerInvariant(), 
            h => h, 
            StringComparer.OrdinalIgnoreCase);
    }

    public async Task<PaymentTransferResultDto> RouteTransferAsync(string provider, PaymentTransferDto request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Routing transfer to provider: {Provider}", provider);

        var handler = GetProviderHandler(provider);
        return await handler.ProcessTransferAsync(request, cancellationToken);
    }

    public async Task<PaymentNameEnquiryResultDto> RouteNameEnquiryAsync(string provider, PaymentNameEnquiryCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Routing name enquiry to provider: {Provider}", provider);

        var handler = GetProviderHandler(provider);
        return await handler.ProcessNameEnquiryAsync(request, cancellationToken);
    }

     public async Task<TransferStatusResultDto> RouteTSQEnquiryAsync(string provider,TransferStatusDto transfer, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Routing transfer enquiry to provider: {Provider}", provider);

        var handler = GetProviderHandler(provider);
        return await handler.ProcessTSQEnquiryAsync(transfer, cancellationToken);
    }

    public IEnumerable<string> GetSupportedProviders()
    {
        return _providerHandlers.Select(h => h.ProviderName);
    }

    private IPaymentProviderHandler GetProviderHandler(string provider)
    {
        if (!_handlerMap.TryGetValue(provider.ToLowerInvariant(), out var handler))
        {
            var supportedProviders = string.Join(", ", GetSupportedProviders());
            throw new InvalidOperationException(
                $"No handler found for provider '{provider}'. Supported providers: {supportedProviders}");
        }

        return handler;
    }
}
