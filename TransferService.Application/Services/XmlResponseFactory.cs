using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Services.Interfaces;
using TransferService.Application.Common.Utilities;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Services;

/// <summary>
/// Factory for creating XML response objects
/// </summary>
public class XmlResponseFactory(string institutionCode) : IXmlResponseFactory
{
    private readonly string _institutionCode = institutionCode ?? throw new ArgumentNullException(nameof(institutionCode));
    
    public NameEnquiryXmlResponse CreateNameEnquiryValidationErrorResponse(NameEnquiryXmlRequest request)
    {
        return new NameEnquiryXmlResponse
        {
            SessionId = request?.SessionId ?? "",
            DestinationInstitutionCode = request?.DestinationInstitutionCode ?? "",
            ChannelCode = request?.ChannelCode ?? "",
            AccountNumber = request?.AccountNumber ?? "",
            AccountName = "",
            BankVerificationNumber = "",
            KycLevel = "",
            ResponseCode = "01" 
        };
    }

    public TransferXmlResponse CreateTransferValidationErrorResponse(TransferXmlRequest request)
    {
        return new TransferXmlResponse
        {
            SessionId = request?.SessionId ?? "",
            NameEnquiryRef = request?.NameEnquiryRef ?? "",
            DestinationInstitutionCode = request?.DestinationInstitutionCode ?? "",
            ChannelCode = request?.ChannelCode ?? 0,
            BeneficiaryAccountNumber = request?.BeneficiaryAccountNumber ?? "",
            Amount = request?.Amount ?? "",
            ResponseCode = "01",
            ResponseMessage = "VALIDATION_FAILED",
            TransactionReference = ""
        };
    }

    public NameEnquiryXmlResponse CreateNameEnquirySuccessResponse(NameEnquiryXmlRequest request, PaymentNameEnquiryResultDto result)
    {
        return new NameEnquiryXmlResponse
        {
            SessionId = request.SessionId,
            DestinationInstitutionCode = request.DestinationInstitutionCode,
            ChannelCode = request.ChannelCode,
            AccountNumber = request.AccountNumber,
            AccountName = (result.AccountName ?? "").EscapeXml(),
            BankVerificationNumber = "***********",
            KycLevel = "3",
            ResponseCode = result.IsSuccessful ? "00" : "01"
        };
    }

    public TransferXmlResponse CreateTransferSuccessResponse(TransferXmlRequest request, PaymentTransferResultDto result)
    {
        return new TransferXmlResponse
        {
            SessionId = request.SessionId,
            NameEnquiryRef = request.NameEnquiryRef,
            DestinationInstitutionCode = request.DestinationInstitutionCode,
            ChannelCode = request.ChannelCode,
            BeneficiaryAccountName = request.BeneficiaryAccountName.EscapeXml(),
            BeneficiaryAccountNumber = request.BeneficiaryAccountNumber,
            BeneficiaryBankVerificationNumber = request.BeneficiaryBankVerificationNumber,
            BeneficiaryKycLevel = request.BeneficiaryKycLevel,
            OriginatorAccountName = request.OriginatorAccountName.EscapeXml(),
            OriginatorAccountNumber = request.OriginatorAccountNumber,
            OriginatorBankVerificationNumber = request.OriginatorBankVerificationNumber,
            OriginatorKycLevel = request.OriginatorKycLevel,
            TransactionLocation = request.TransactionLocation,
            Narration = request.Narration?.EscapeXml(),
            PaymentReference = request.PaymentReference,
            Amount = request.Amount,
            // Add response-specific fields
            ResponseCode = result.IsSuccessful ? "00" : (result.TransferDetails?.ResponseCode ?? "01"),
            ResponseMessage = result.IsSuccessful ? "SUCCESS" : (result.ErrorMessage ?? "FAILED"),
            TransactionReference = result.TransactionId ?? ""
        };
    }

    public NameEnquiryXmlResponse CreateNameEnquiryErrorResponse(NameEnquiryXmlRequest request)
    {
        return new NameEnquiryXmlResponse
        {
            SessionId = request?.SessionId ?? "",
            ResponseCode = "99"
        };
    }

    public TransferXmlResponse CreateTransferErrorResponse(TransferXmlRequest request)
    {
        return new TransferXmlResponse
        {
            SessionId = request.SessionId ?? "",
            ResponseCode = "99",
            ResponseMessage = "Internal server error"
        };
    }
}
