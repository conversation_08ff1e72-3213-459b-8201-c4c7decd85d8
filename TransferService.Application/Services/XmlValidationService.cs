using FluentValidation;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Services.Interfaces;

namespace TransferService.Application.Services;

/// <summary>
/// Service for validating XML request objects using FluentValidation
/// </summary>
public class XmlValidationService(
    IValidator<NameEnquiryXmlRequest> nameEnquiryValidator,
    IValidator<TransferXmlRequest> transferValidator)
    : IXmlValidationService
{
    public async Task<ValidationResult> ValidateAsync(NameEnquiryXmlRequest request)
    {
        var validationResult = await nameEnquiryValidator.ValidateAsync(request);
        
        return new ValidationResult
        {
            IsValid = validationResult.IsValid,
            Errors = validationResult.Errors.Select(e => e.ErrorMessage)
        };
    }

    public async Task<ValidationResult> ValidateAsync(TransferXmlRequest request)
    {
        var validationResult = await transferValidator.ValidateAsync(request);
        
        return new ValidationResult
        {
            IsValid = validationResult.IsValid,
            Errors = validationResult.Errors.Select(e => e.ErrorMessage)
        };
    }
}
