<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\TransferService.Domain\TransferService.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="AutoMapper" Version="12.0.1" />
      <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
      <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
      <PackageReference Include="MediatR" Version="13.0.0" />
    </ItemGroup>

</Project>
