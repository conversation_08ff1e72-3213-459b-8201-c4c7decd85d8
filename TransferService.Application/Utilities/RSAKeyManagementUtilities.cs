using System.Security.Cryptography;
using System.Text;
using TransferService.Application.Common.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Application.Utilities;

/// <summary>
/// Utilities for RSA key management operations
/// </summary>
public static class RSAKeyManagementUtilities
{
    /// <summary>
    /// Validates if a string is a valid PEM-formatted RSA key
    /// </summary>
    /// <param name="pemKey">PEM key string to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    public static bool IsValidPemKey(string pemKey)
    {
        if (string.IsNullOrWhiteSpace(pemKey))
            return false;

        try
        {
            using var rsa = RSA.Create();
            rsa.ImportFromPem(pemKey);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Validates if a string is a valid PEM-formatted RSA public key
    /// </summary>
    /// <param name="pemKey">PEM public key string to validate</param>
    /// <returns>True if valid public key, false otherwise</returns>
    public static bool IsValidPublicKey(string pemKey)
    {
        if (string.IsNullOrWhiteSpace(pemKey))
            return false;

        try
        {
            using var rsa = RSA.Create();
            rsa.ImportFromPem(pemKey);
            
            // Check if it's a public key (no private key parameters)
            var parameters = rsa.ExportParameters(false);
            return parameters.Modulus != null && parameters.Exponent != null;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Validates if a string is a valid PEM-formatted RSA private key
    /// </summary>
    /// <param name="pemKey">PEM private key string to validate</param>
    /// <returns>True if valid private key, false otherwise</returns>
    public static bool IsValidPrivateKey(string pemKey)
    {
        if (string.IsNullOrWhiteSpace(pemKey))
            return false;

        try
        {
            using var rsa = RSA.Create();
            rsa.ImportFromPem(pemKey);
            
            // Check if it's a private key (has private key parameters)
            var parameters = rsa.ExportParameters(true);
            return parameters.Modulus != null && parameters.Exponent != null && 
                   parameters.D != null && parameters.P != null && parameters.Q != null;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Gets the key size from a PEM-formatted RSA key
    /// </summary>
    /// <param name="pemKey">PEM key string</param>
    /// <returns>Key size in bits, or null if invalid</returns>
    public static int? GetKeySize(string pemKey)
    {
        if (string.IsNullOrWhiteSpace(pemKey))
            return null;

        try
        {
            using var rsa = RSA.Create();
            rsa.ImportFromPem(pemKey);
            return rsa.KeySize;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Gets the key fingerprint (SHA-256 hash of the public key)
    /// </summary>
    /// <param name="pemKey">PEM key string</param>
    /// <returns>Key fingerprint as hex string, or null if invalid</returns>
    public static string? GetKeyFingerprint(string pemKey)
    {
        if (string.IsNullOrWhiteSpace(pemKey))
            return null;

        try
        {
            using var rsa = RSA.Create();
            rsa.ImportFromPem(pemKey);
            
            var parameters = rsa.ExportParameters(false);
            var keyData = parameters.Modulus!;
            
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(keyData);
            
            return Convert.ToHexString(hash).ToLowerInvariant();
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Extracts the public key from a private key
    /// </summary>
    /// <param name="privateKeyPem">PEM private key string</param>
    /// <returns>Public key in PEM format, or null if invalid</returns>
    public static string? ExtractPublicKey(string privateKeyPem)
    {
        if (string.IsNullOrWhiteSpace(privateKeyPem))
            return null;

        try
        {
            using var rsa = RSA.Create();
            rsa.ImportFromPem(privateKeyPem);
            return rsa.ExportRSAPublicKeyPem();
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Validates if two keys form a valid key pair
    /// </summary>
    /// <param name="publicKeyPem">PEM public key string</param>
    /// <param name="privateKeyPem">PEM private key string</param>
    /// <returns>True if they form a valid pair, false otherwise</returns>
    public static bool IsValidKeyPair(string publicKeyPem, string privateKeyPem)
    {
        if (string.IsNullOrWhiteSpace(publicKeyPem) || string.IsNullOrWhiteSpace(privateKeyPem))
            return false;

        try
        {
            using var publicRsa = RSA.Create();
            using var privateRsa = RSA.Create();
            
            publicRsa.ImportFromPem(publicKeyPem);
            privateRsa.ImportFromPem(privateKeyPem);

            // Check if key sizes match
            if (publicRsa.KeySize != privateRsa.KeySize)
                return false;

            // Check if they form a valid pair by testing encryption/decryption
            var testData = Encoding.UTF8.GetBytes("Test");
            var encrypted = publicRsa.Encrypt(testData, RSAEncryptionPadding.OaepSHA256);
            var decrypted = privateRsa.Decrypt(encrypted, RSAEncryptionPadding.OaepSHA256);
            
            return testData.SequenceEqual(decrypted);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Gets the maximum data size that can be encrypted with a given key size
    /// </summary>
    /// <param name="keySize">Key size in bits</param>
    /// <returns>Maximum data size in bytes</returns>
    public static int GetMaxDataSize(int keySize)
    {
        // For OAEP SHA-256 padding, the maximum data size is:
        // key_size_in_bytes - 2 * hash_length - 2
        // For SHA-256: hash_length = 32 bytes
        return (keySize / 8) - (2 * 32) - 2;
    }

    /// <summary>
    /// Validates if data size is within the limit for a given key size
    /// </summary>
    /// <param name="dataSize">Data size in bytes</param>
    /// <param name="keySize">Key size in bits</param>
    /// <returns>True if data size is valid, false otherwise</returns>
    public static bool IsValidDataSize(int dataSize, int keySize)
    {
        var maxSize = GetMaxDataSize(keySize);
        return dataSize > 0 && dataSize <= maxSize;
    }

    /// <summary>
    /// Formats a key size for display
    /// </summary>
    /// <param name="keySize">Key size in bits</param>
    /// <returns>Formatted key size string</returns>
    public static string FormatKeySize(int keySize)
    {
        return $"{keySize} bits ({keySize / 8} bytes)";
    }

    /// <summary>
    /// Formats a key fingerprint for display
    /// </summary>
    /// <param name="fingerprint">Key fingerprint</param>
    /// <returns>Formatted fingerprint string</returns>
    public static string FormatFingerprint(string fingerprint)
    {
        if (string.IsNullOrWhiteSpace(fingerprint))
            return "N/A";

        // Format as: XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX
        var formatted = string.Join(":", Enumerable.Range(0, fingerprint.Length / 2)
            .Select(i => fingerprint.Substring(i * 2, 2)));
        
        return formatted.ToUpperInvariant();
    }

    /// <summary>
    /// Validates RSA key pair information
    /// </summary>
    /// <param name="keyPairInfo">Key pair information to validate</param>
    /// <returns>Validation result</returns>
    public static RSAKeyValidationResult ValidateKeyPairInfo(RSAKeyPairInfo keyPairInfo)
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        // Validate key identifier
        if (string.IsNullOrWhiteSpace(keyPairInfo.KeyIdentifier))
        {
            errors.Add("Key identifier is required");
        }

        // Validate public key
        if (string.IsNullOrWhiteSpace(keyPairInfo.PublicKeyPem))
        {
            errors.Add("Public key is required");
        }
        else if (!IsValidPublicKey(keyPairInfo.PublicKeyPem))
        {
            errors.Add("Public key is not valid");
        }

        // Validate private key
        if (string.IsNullOrWhiteSpace(keyPairInfo.PrivateKeyPem))
        {
            warnings.Add("Private key is not provided (encryption-only mode)");
        }
        else if (!IsValidPrivateKey(keyPairInfo.PrivateKeyPem))
        {
            errors.Add("Private key is not valid");
        }

        // Validate key size
        if (keyPairInfo.KeySize != 2048 && keyPairInfo.KeySize != 3072 && keyPairInfo.KeySize != 4096)
        {
            errors.Add($"Invalid key size: {keyPairInfo.KeySize}. Supported sizes are 2048, 3072, and 4096 bits.");
        }

        // Validate key pair if both keys are provided
        if (!string.IsNullOrWhiteSpace(keyPairInfo.PublicKeyPem) && !string.IsNullOrWhiteSpace(keyPairInfo.PrivateKeyPem))
        {
            if (!IsValidKeyPair(keyPairInfo.PublicKeyPem, keyPairInfo.PrivateKeyPem))
            {
                errors.Add("Public and private keys do not form a valid key pair");
            }
        }

        // Validate creation date
        if (keyPairInfo.CreatedAt > DateTime.UtcNow)
        {
            warnings.Add("Creation date is in the future");
        }

        // Validate expiration date if provided
        if (keyPairInfo.ExpiresAt.HasValue)
        {
            if (keyPairInfo.ExpiresAt.Value <= keyPairInfo.CreatedAt)
            {
                errors.Add("Expiration date must be after creation date");
            }
            
            if (keyPairInfo.ExpiresAt.Value <= DateTime.UtcNow)
            {
                warnings.Add("Key has expired");
            }
        }

        return new RSAKeyValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors,
            Warnings = warnings
        };
    }
}

/// <summary>
/// Result of RSA key validation
/// </summary>
public class RSAKeyValidationResult
{
    /// <summary>
    /// Whether the key is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Validation errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Validation warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();
} 