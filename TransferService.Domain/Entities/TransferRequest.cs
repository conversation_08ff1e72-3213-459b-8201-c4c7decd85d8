using TransferService.Domain.Enums;

namespace TransferService.Domain.Entities;

public class TransferRequest : BaseEntity
{
    public string FromAccount { get; set; } = string.Empty;
    public string ToAccount { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "USD";
    public string Description { get; set; } = string.Empty;
    public TransferStatus Status { get; set; } = TransferStatus.Pending;
    public string? ExternalTransactionId { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime? ProcessedAt { get; set; }
}
