namespace TransferService.Domain.Exceptions;

/// <summary>
/// Base exception for EasyPay operations
/// </summary>
public abstract class EasyPayException : Exception
{
    protected EasyPayException(string message) : base(message) { }
    protected EasyPayException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when EasyPay authentication fails
/// </summary>
public class EasyPayAuthenticationException : EasyPayException
{
    public EasyPayAuthenticationException(string message) : base(message) { }
    public EasyPayAuthenticationException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when EasyPay API returns an error
/// </summary>
public class EasyPayApiException : EasyPayException
{
    public string? ResponseCode { get; }
    public string? ResponseMessage { get; }

    public EasyPayApiException(string message, string? responseCode = null, string? responseMessage = null) 
        : base(message)
    {
        ResponseCode = responseCode;
        ResponseMessage = responseMessage;
    }

    public EasyPayApiException(string message, Exception innerException, string? responseCode = null, string? responseMessage = null) 
        : base(message, innerException)
    {
        ResponseCode = responseCode;
        ResponseMessage = responseMessage;
    }
}

/// <summary>
/// Exception thrown when name enquiry fails
/// </summary>
public class EasyPayNameEnquiryException : EasyPayException
{
    public string AccountNumber { get; }
    public string BankCode { get; }

    public EasyPayNameEnquiryException(string accountNumber, string bankCode, string message) 
        : base(message)
    {
        AccountNumber = accountNumber;
        BankCode = bankCode;
    }

    public EasyPayNameEnquiryException(string accountNumber, string bankCode, string message, Exception innerException) 
        : base(message, innerException)
    {
        AccountNumber = accountNumber;
        BankCode = bankCode;
    }
}

/// <summary>
/// Exception thrown when transfer operation fails
/// </summary>
public class EasyPayTransferException : EasyPayException
{
    public decimal Amount { get; }
    public string AccountNumber { get; }
    public string BankCode { get; }

    public EasyPayTransferException(decimal amount, string accountNumber, string bankCode, string message) 
        : base(message)
    {
        Amount = amount;
        AccountNumber = accountNumber;
        BankCode = bankCode;
    }

    public EasyPayTransferException(decimal amount, string accountNumber, string bankCode, string message, Exception innerException) 
        : base(message, innerException)
    {
        Amount = amount;
        AccountNumber = accountNumber;
        BankCode = bankCode;
    }
}

/// <summary>
/// Exception thrown when configuration is invalid or missing
/// </summary>
public class EasyPayConfigurationException : EasyPayException
{
    public EasyPayConfigurationException(string message) : base(message) { }
    public EasyPayConfigurationException(string message, Exception innerException) : base(message, innerException) { }
}
