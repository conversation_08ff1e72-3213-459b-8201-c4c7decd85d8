namespace TransferService.Domain.Interfaces.UnifiedPayment;

public interface IUnifiedPaymentService
{
    Task<HttpContent> PerformNameEnqiryAsync(string bankCode, string accountNumber, CancellationToken cancellationToken = default);

    Task<HttpContent> TSQEnqiryAsync(string transactionId, CancellationToken cancellationToken = default);
    /// <summary>
    /// Perform unified payment outbound credit transfer
    /// </summary>
    /// <param name="transferRequest">Of Type PaymentTransferDto in Application Layer</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<HttpContent> ProcessTransferAsync(object transferRequest, CancellationToken cancellationToken = default);
}
