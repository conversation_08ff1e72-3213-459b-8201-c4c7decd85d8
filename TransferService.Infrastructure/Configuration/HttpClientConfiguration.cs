using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Refit;
using TransferService.Infrastructure.ExternalServices;
using TransferService.Infrastructure.ExternalServices.EasyPay;
using System.Text.Json;
using Polly;
using Polly.Extensions.Http;
using System.Text;

namespace TransferService.Infrastructure.Configuration;

public static class HttpClientConfiguration
{
    public static IServiceCollection AddHttpClients(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure JSON serialization options
        var jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        // Configure Refit settings
        var refitSettings = new RefitSettings
        {
            ContentSerializer = new SystemTextJsonContentSerializer(jsonOptions)
        };

        // Configure XML serialization for Refit
        var xmlRefitSettings = new RefitSettings
        {
            ContentSerializer = new SystemTextJsonContentSerializer(jsonOptions)
        };

        //custom Xml serlizer for refit
        var xmlSettings = new RefitSettings
        {
            ContentSerializer = new XmlContentSerializer()
        };

        // Add Transfer API client
        services.AddRefitClient<ITransferApiClient>(refitSettings)
            .ConfigureHttpClient(client =>
            {
                var baseUrl = configuration.GetValue<string>("ExternalApis:TransferApi:BaseUrl") ?? "https://api.example.com";
                client.BaseAddress = new Uri(baseUrl);
                client.Timeout = TimeSpan.FromSeconds(30);

                // Add default headers
                client.DefaultRequestHeaders.Add("User-Agent", "TransferService/1.0");

                // Add API key if configured
                var apiKey = configuration.GetValue<string>("ExternalApis:TransferApi:ApiKey");
                if (!string.IsNullOrEmpty(apiKey))
                {
                    client.DefaultRequestHeaders.Add("X-API-Key", apiKey);
                }
            })
            .AddPolicyHandler(GetRetryPolicy())
            .AddPolicyHandler(GetCircuitBreakerPolicy());

        //Add NameEnq API client for UP
        services.AddRefitClient<IUnifiedPaymentClient>(xmlSettings)
        .ConfigureHttpClient(client =>
        {
            var baseUrl = configuration.GetValue<string>("ExternalApis:UnifiedPayment:BaseUrl") ?? "";
            client.BaseAddress = new Uri(baseUrl);
            client.Timeout = TimeSpan.FromSeconds(configuration.GetValue<int>("ExternalApis:UnifiedPayment:NameEnq:TimeoutSeconds"));

            // Add default headers
            //client.DefaultRequestHeaders.Add("User-Agent", "TransferService/1.0");

            // BASIC AUTH
            var username = configuration.GetValue<string>("ExternalApis:UnifiedPayment:Username");
            var password = configuration.GetValue<string>("ExternalApis:UnifiedPayment:Password");

            var credentialByte = Encoding.UTF8.GetBytes($"{username}:{password}");
            string basicAuthCredential = Convert.ToBase64String(credentialByte);

            client.DefaultRequestHeaders.Add("Authorization", $"Basic {basicAuthCredential}");

        })
        .AddPolicyHandler(GetRetryPolicy())
        .AddPolicyHandler(GetCircuitBreakerPolicy());

        // Add XML API client
        services.AddRefitClient<IXmlApiClient>(refitSettings)
            .ConfigureHttpClient(client =>
            {
                var baseUrl = configuration.GetValue<string>("ExternalApis:XmlApi:BaseUrl") ?? "https://xmlapi.example.com";
                client.BaseAddress = new Uri(baseUrl);
                client.Timeout = TimeSpan.FromSeconds(45);

                client.DefaultRequestHeaders.Add("User-Agent", "TransferService/1.0");

                var apiKey = configuration.GetValue<string>("ExternalApis:XmlApi:ApiKey");
                if (!string.IsNullOrEmpty(apiKey))
                {
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
                }
            })
            .AddPolicyHandler(GetRetryPolicy())
            .AddPolicyHandler(GetCircuitBreakerPolicy());

        // Add EasyPay Token API client
        services.AddRefitClient<IEasyPayTokenClient>(refitSettings)
            .ConfigureHttpClient(client =>
            {
                // Use separate token reset URL for token client
                var tokenBaseUrl = configuration.GetValue<string>("ExternalApis:EasyPay:TokenResetUrl") ?? "https://apitest.nibss-plc.com.ng";
                // Remove the endpoint path since it's defined in the interface
                var baseUri = new Uri(tokenBaseUrl);
                var baseUrl = $"{baseUri.Scheme}://{baseUri.Host}";
                if (!baseUri.IsDefaultPort) baseUrl += $":{baseUri.Port}";

                client.BaseAddress = new Uri(baseUrl);
                client.Timeout = TimeSpan.FromSeconds(configuration.GetValue<int>("ExternalApis:EasyPay:TimeoutSeconds", 30));

                client.DefaultRequestHeaders.Add("User-Agent", "TransferService/1.0");

                // Add API key header for token requests
                var apiKey = configuration.GetValue<string>("ExternalApis:EasyPay:ApiKey");
                if (!string.IsNullOrEmpty(apiKey))
                {
                    client.DefaultRequestHeaders.Add("apikey", apiKey);
                }
            })
            .AddPolicyHandler(GetRetryPolicy())
            .AddPolicyHandler(GetCircuitBreakerPolicy());

        // Add EasyPay API client with authentication handler
        services.AddRefitClient<IEasyPayApiClient>(refitSettings)
            .ConfigureHttpClient(client =>
            {
                var baseUrl = configuration.GetValue<string>("ExternalApis:EasyPay:BaseUrl") ?? "https://easypay-api.nibss.com";
                client.BaseAddress = new Uri(baseUrl);
                client.Timeout = TimeSpan.FromSeconds(configuration.GetValue<int>("ExternalApis:EasyPay:TimeoutSeconds", 30));

                client.DefaultRequestHeaders.Add("User-Agent", "TransferService/1.0");
            })
            .AddHttpMessageHandler<EasyPayAuthenticationHandler>()
            .AddPolicyHandler(GetRetryPolicy())
            .AddPolicyHandler(GetCircuitBreakerPolicy());

        return services;
    }

    private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    Console.WriteLine($"Retry {retryCount} after {timespan} seconds");
                });
    }

    private static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 3,
                durationOfBreak: TimeSpan.FromSeconds(30),
                onBreak: (exception, duration) =>
                {
                    Console.WriteLine($"Circuit breaker opened for {duration}");
                },
                onReset: () =>
                {
                    Console.WriteLine("Circuit breaker reset");
                });
    }
}


