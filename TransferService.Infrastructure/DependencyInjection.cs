using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using TransferService.Domain.Interfaces;
using TransferService.Infrastructure.Configuration;
using TransferService.Infrastructure.ExternalServices;
using TransferService.Infrastructure.ExternalServices.EasyPay;
using TransferService.Infrastructure.Logging;
using TransferService.Infrastructure.Monitoring;
using TransferService.Infrastructure.Services;
using TransferService.Infrastructure.Options;
using TransferService.Application.Interfaces;
using TransferService.Application.Configuration;
using TransferService.Domain.Interfaces.UnifiedPayment;
using TransferService.Infrastructure.ExternalServices.UnifiedPayment;

namespace TransferService.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration, IHostEnvironment environment)
    {
        // Add logging
        services.AddSerilogLogging(configuration, environment);

        // Add HTTP clients and external services
        services.AddHttpClients(configuration);

        // Register services
        services.AddScoped<ITransferApiService, TransferApiService>();
        services.AddScoped<IXmlConverterService, XmlConverterService>();

        //unified payment services
        services.AddScoped<IUnifiedPaymentService, UnifiedPaymentService>();

        // Configure centralized caching service
        ConfigureCacheService(services, configuration);

        // Register EasyPay services
        services.AddScoped<IEasyPayTokenService, EasyPayTokenService>();
        services.AddScoped<IEasyPayAuthenticatedClient, EasyPayAuthenticatedClient>();
        services.AddScoped<IEasyPayTransferService, EasyPayTransferService>();
        services.AddScoped<EasyPayAuthenticationHandler>();
        services.AddScoped<IEasyPayMetrics, EasyPayMetrics>();

        // Register YARP-style Payment Gateway
        services.Configure<PaymentGatewayOptions>(configuration.GetSection("PaymentGateway"));
        services.AddScoped<IPaymentGatewayService, TransferService.Application.Services.PaymentGatewayService>();
        services.AddScoped<TransferService.Application.Services.IPaymentDestinationSelector, TransferService.Application.Services.PaymentDestinationSelector>();

        // Register simple RSA service as singleton for better performance
        services.AddSingleton<IRSAService, SimpleRSAService>();

        // Register encrypted XML service
        services.AddScoped<IEncryptedXmlService, EncryptedXmlService>();

        // Add health checks
        services.AddHealthChecks()
            .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy());

        // Add configuration options
        services.Configure<ExternalApiOptions>(configuration.GetSection("ExternalApis"));

        return services;
    }



    /// <summary>
    /// Configures centralized cache service based on configuration
    /// </summary>
    private static void ConfigureCacheService(IServiceCollection services, IConfiguration configuration)
    {
        var easyPayOptions = configuration.GetSection("ExternalApis:EasyPay").Get<EasyPayOptions>();
        var cacheStrategy = easyPayOptions?.CacheStrategy ?? CacheStrategy.FusionCache;

        switch (cacheStrategy)
        {
            case CacheStrategy.FusionCache:
                ConfigureFusionCache(services, easyPayOptions);

                // Register centralized cache service
                services.AddSingleton<ICacheService, FusionCacheService>();

                // Register EasyPay token store using the centralized cache service
                services.AddSingleton<IEasyPayTokenStore, EasyPayTokenStore>();
                break;

            default:
                throw new InvalidOperationException($"Unsupported cache strategy: {cacheStrategy}");
        }
    }

    /// <summary>
    /// Configures FusionCache with Redis for high-performance multi-level caching
    /// </summary>
    private static void ConfigureFusionCache(IServiceCollection services, EasyPayOptions? options)
    {
        // Configure Redis if connection string is provided
        if (!string.IsNullOrEmpty(options?.RedisConnectionString))
        {
            services.AddStackExchangeRedisCache(redisOptions =>
            {
                redisOptions.Configuration = options.RedisConnectionString;
                redisOptions.InstanceName = "TransferService";
            });
        }
        else
        {
            // Fallback to in-memory distributed cache for development
            services.AddDistributedMemoryCache();
        }

        // Configure FusionCache with simple setup
        services.AddFusionCache();
    }
}
