using Microsoft.Extensions.Logging;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Domain.Exceptions;
using TransferService.Application.Interfaces;
using System.Text.Json;

namespace TransferService.Infrastructure.ExternalServices.EasyPay;

/// <summary>
/// Authenticated wrapper for EasyPay API client that handles token injection
/// </summary>
public class EasyPayAuthenticatedClient(
    IEasyPayApiClient apiClient,
    IEasyPayTokenService tokenService,
    ILogger<EasyPayAuthenticatedClient> logger)
    : IEasyPayAuthenticatedClient
{
    private readonly IEasyPayApiClient _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
    private readonly IEasyPayTokenService _tokenService = tokenService ?? throw new ArgumentNullException(nameof(tokenService));
    private readonly ILogger<EasyPayAuthenticatedClient> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<EasyPayNameEnquiryResponse> NameEnquiryAsync(EasyPayNameEnquiryRequest request, CancellationToken cancellationToken = default)
    {
        // Log the request
        var requestJson = JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true });
        _logger.LogInformation("NIBS API NameEnquiry Request: {Request}", requestJson);
        
        return await ExecuteWithAuthenticationAsync(
            async () => await _apiClient.NameEnquiryAsync(request, cancellationToken),
            "NameEnquiry",
            cancellationToken);
    }

    public async Task<FundTransferDto> FundTransferAsync(EasyPayTransferRequest request, CancellationToken cancellationToken = default)
    {
        // Log the request
        var requestJson = JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true });
        _logger.LogInformation("NIBS API FundTransfer Request: {Request}", requestJson);
        
        return await ExecuteWithAuthenticationAsync(
            async () => await _apiClient.FundTransferAsync(request, cancellationToken),
            "FundTransfer",
            cancellationToken);
    }

    public async Task<EasyPayBalanceEnquiryResponse> BalanceEnquiryAsync(EasyPayBalanceEnquiryRequest request, CancellationToken cancellationToken = default)
    {
        // Log the request
        var requestJson = JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true });
        _logger.LogInformation("NIBS API BalanceEnquiry Request: {Request}", requestJson);
        
        return await ExecuteWithAuthenticationAsync(
            async () => await _apiClient.BalanceEnquiryAsync(request, cancellationToken),
            "BalanceEnquiry",
            cancellationToken);
    }

    public async Task<EasyPayTSQResponse> TransactionStatusQueryAsync(EasyPayTSQRequest request, CancellationToken cancellationToken = default)
    {
        // Log the request
        var requestJson = JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true });
        _logger.LogInformation("NIBS API TransactionStatusQuery Request: {Request}", requestJson);
        
        return await ExecuteWithAuthenticationAsync(
            async () => await _apiClient.TransactionStatusQueryAsync(request, cancellationToken),
            "TransactionStatusQuery",
            cancellationToken);
    }

    private async Task<T> ExecuteWithAuthenticationAsync<T>(
        Func<Task<T>> operation,
        string operationName,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Executing EasyPay {OperationName} operation", operationName);

            // Get access token (will use cached token if valid)
            var accessToken = await _tokenService.GetAccessTokenAsync(cancellationToken);
            
            // Note: Token injection will be handled by HTTP client configuration in DI
            // The actual Authorization header will be set by a DelegatingHandler
            
            var result = await operation();
            
            _logger.LogDebug("Successfully completed EasyPay {OperationName} operation", operationName);
            
            // Log the raw API response as JSON string
            var responseJson = JsonSerializer.Serialize(result, new JsonSerializerOptions { WriteIndented = true });
            _logger.LogInformation("NIBS API {OperationName} Response: {Response}", operationName, responseJson);
            
            return result;
        }
        catch (Refit.ApiException ex) when (ex.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            _logger.LogWarning("Received 401 Unauthorized for {OperationName}. Attempting token refresh", operationName);
            
            // Clear cached token and try to refresh
            _tokenService.ClearToken();
            
            try
            {
                // Get fresh token
                var newAccessToken = await _tokenService.RefreshTokenAsync(cancellationToken);
                
                // Retry the operation with new token
                var result = await operation();
                
                _logger.LogInformation("Successfully retried EasyPay {OperationName} operation after token refresh", operationName);
                
                return result;
            }
            catch (Exception retryEx)
            {
                _logger.LogError(retryEx, "Failed to retry EasyPay {OperationName} operation after token refresh", operationName);
                throw new EasyPayApiException(
                    $"Authentication failed for {operationName} operation", 
                    retryEx, 
                    ex.StatusCode.ToString(), 
                    ex.Content);
            }
        }
        catch (Refit.ApiException ex)
        {
            _logger.LogError(ex, "EasyPay API error for {OperationName}. Status: {StatusCode}, Content: {Content}", 
                operationName, ex.StatusCode, ex.Content);
            
            throw new EasyPayApiException(
                $"API error for {operationName} operation", 
                ex, 
                ex.StatusCode.ToString(), 
                ex.Content);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error for EasyPay {OperationName} operation", operationName);
            throw new EasyPayApiException($"Network error for {operationName} operation", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout for EasyPay {OperationName} operation", operationName);
            throw new EasyPayApiException($"Timeout for {operationName} operation", ex);
        }
    }
}

/// <summary>
/// Interface for authenticated EasyPay API client
/// </summary>
public interface IEasyPayAuthenticatedClient
{
    Task<EasyPayNameEnquiryResponse> NameEnquiryAsync(EasyPayNameEnquiryRequest request, CancellationToken cancellationToken = default);
    Task<FundTransferDto> FundTransferAsync(EasyPayTransferRequest request, CancellationToken cancellationToken = default);
    Task<EasyPayBalanceEnquiryResponse> BalanceEnquiryAsync(EasyPayBalanceEnquiryRequest request, CancellationToken cancellationToken = default);
    Task<EasyPayTSQResponse> TransactionStatusQueryAsync(EasyPayTSQRequest request, CancellationToken cancellationToken = default);
}
