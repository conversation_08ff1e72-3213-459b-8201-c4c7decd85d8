using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.ExternalServices.EasyPay;

/// <summary>
/// HTTP message handler that automatically injects Bearer tokens for EasyPay API requests
/// </summary>
public class EasyPayAuthenticationHandler(
    IEasyPayTokenService tokenService,
    ILogger<EasyPayAuthenticationHandler> logger)
    : DelegatingHandler
{
    private readonly IEasyPayTokenService _tokenService = tokenService ?? throw new ArgumentNullException(nameof(tokenService));
    private readonly ILogger<EasyPayAuthenticationHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        try
        {
            // Get access token for the request
            var accessToken = await _tokenService.GetAccessTokenAsync(cancellationToken);

            // Add Authorization header
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            _logger.LogDebug("Added Bearer token to EasyPay API request: {Method} {Uri}", 
                request.Method, request.RequestUri);

            // Send the request
            var response = await base.SendAsync(request, cancellationToken);

            // If we get 401, the token might be expired - let the authenticated client handle retry
            if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("Received 401 Unauthorized from EasyPay API for {Method} {Uri}", 
                    request.Method, request.RequestUri);
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in EasyPay authentication handler for {Method} {Uri}", 
                request.Method, request.RequestUri);
            throw;
        }
    }
}
