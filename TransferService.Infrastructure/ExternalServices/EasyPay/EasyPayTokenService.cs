using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TransferService.Domain.Exceptions;
using TransferService.Application.Interfaces;
using TransferService.Infrastructure.Options;
using System.Text.Json;

namespace TransferService.Infrastructure.ExternalServices.EasyPay;

/// <summary>
/// Service for managing EasyPay OAuth2 tokens with caching and automatic refresh
/// </summary>
public class EasyPayTokenService : IEasyPayTokenService, IDisposable
{
    private readonly IEasyPayTokenStore _tokenStore;
    private readonly IEasyPayTokenClient _tokenClient;
    private readonly EasyPayOptions _options;
    private readonly ILogger<EasyPayTokenService> _logger;
    private readonly SemaphoreSlim _tokenSemaphore = new(1, 1);

    public EasyPayTokenService(
        IEasyPayTokenStore tokenStore,
        IEasyPayTokenClient tokenClient,
        IOptions<ExternalApiOptions> options,
        ILogger<EasyPayTokenService> logger)
    {
        _tokenStore = tokenStore ?? throw new ArgumentNullException(nameof(tokenStore));
        _tokenClient = tokenClient ?? throw new ArgumentNullException(nameof(tokenClient));
        _options = options?.Value?.EasyPay ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        ValidateConfiguration();
    }

    public async Task<string> GetAccessTokenAsync(CancellationToken cancellationToken = default)
    {
        // Try to get cached token first
        var cachedToken = _tokenStore.GetToken();
        if (!string.IsNullOrEmpty(cachedToken))
        {
            _logger.LogDebug("Using cached EasyPay access token");
            return cachedToken;
        }

        // Need to refresh token
        return await RefreshTokenAsync(cancellationToken);
    }

    public async Task<string> RefreshTokenAsync(CancellationToken cancellationToken = default)
    {
        await _tokenSemaphore.WaitAsync(cancellationToken);
        try
        {
            // Double-check pattern: another thread might have refreshed the token
            var cachedToken = _tokenStore.GetToken();
            if (!string.IsNullOrEmpty(cachedToken))
            {
                _logger.LogDebug("Token was refreshed by another thread");
                return cachedToken;
            }

            _logger.LogInformation("Refreshing EasyPay access token");

            // Prepare token request
            var tokenRequest = new EasyPayTokenFormRequest
            {
                ClientId = _options.ClientId,
                Scope = $"{_options.ClientId}/.default",
                ClientSecret = _options.ClientSecret,
                GrantType = "client_credentials"
            };

            _logger.LogDebug("Requesting new EasyPay access token");

            // Log the token request (sanitized)
            var sanitizedRequest = new
            {
                ClientId = tokenRequest.ClientId,
                Scope = tokenRequest.Scope,
                ClientSecret = "***REDACTED***",
                GrantType = tokenRequest.GrantType
            };
            var requestJson = JsonSerializer.Serialize(sanitizedRequest, new JsonSerializerOptions { WriteIndented = true });
            _logger.LogInformation("NIBS API Token Request: {Request}", requestJson);

            var tokenResponse = await _tokenClient.GetTokenAsync(tokenRequest, _options.ApiKey, cancellationToken);

            if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.AccessToken))
            {
                _logger.LogError("Invalid token response received");
                throw new EasyPayAuthenticationException("Invalid token response received");
            }

            // Log the token response (sanitized)
            var sanitizedResponse = new
            {
                AccessToken = "***REDACTED***",
                ExpiresIn = tokenResponse.ExpiresIn,
                ExtExpiresIn = tokenResponse.ExtExpiresIn,
                TokenType = tokenResponse.TokenType
            };
            var responseJson = JsonSerializer.Serialize(sanitizedResponse, new JsonSerializerOptions { WriteIndented = true });
            _logger.LogInformation("NIBS API Token Response: {Response}", responseJson);

            // Use expiration time from response
            var expiresInSeconds = tokenResponse.ExpiresIn > 0 ? tokenResponse.ExpiresIn : 3599;

            // Cache the token
            _tokenStore.SetToken(tokenResponse.AccessToken, expiresInSeconds);

            _logger.LogInformation("Successfully obtained and cached EasyPay access token. Expires in {ExpiresIn} seconds", expiresInSeconds);

            return tokenResponse.AccessToken;
        }
        catch (Refit.ApiException ex)
        {
            _logger.LogError(ex, "API error while obtaining EasyPay access token. Status: {StatusCode}, Content: {Content}",
                ex.StatusCode, ex.Content);
            throw new EasyPayAuthenticationException($"API error while obtaining access token. Status: {ex.StatusCode}", ex);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while obtaining EasyPay access token");
            throw new EasyPayAuthenticationException("Network error while obtaining access token", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while obtaining EasyPay access token");
            throw new EasyPayAuthenticationException("Timeout while obtaining access token", ex);
        }
        finally
        {
            _tokenSemaphore.Release();
        }
    }

    public void ClearToken()
    {
        _logger.LogInformation("Clearing cached EasyPay access token");
        _tokenStore.ClearToken();
    }

    private void ValidateConfiguration()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(_options.TokenResetUrl))
            errors.Add("TokenResetUrl is required");

        if (string.IsNullOrWhiteSpace(_options.ClientId))
            errors.Add("ClientId is required");

        if (string.IsNullOrWhiteSpace(_options.ClientSecret))
            errors.Add("ClientSecret is required");

        if (string.IsNullOrWhiteSpace(_options.ApiKey))
            errors.Add("ApiKey is required");

        if (errors.Any())
        {
            var errorMessage = $"EasyPay configuration is invalid: {string.Join(", ", errors)}";
            _logger.LogError(errorMessage);
            throw new EasyPayConfigurationException(errorMessage);
        }
    }

    public void Dispose()
    {
        _tokenSemaphore?.Dispose();
    }
}
