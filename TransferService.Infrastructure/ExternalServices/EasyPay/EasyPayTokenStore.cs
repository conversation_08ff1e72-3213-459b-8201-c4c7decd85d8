using Microsoft.Extensions.Logging;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.ExternalServices.EasyPay;

/// <summary>
/// Thread-safe token store for EasyPay access tokens
/// </summary>
public interface IEasyPayTokenStore
{
    /// <summary>
    /// Gets the cached token if it exists and is not expired
    /// </summary>
    /// <returns>Token if valid, null otherwise</returns>
    string? GetToken();

    /// <summary>
    /// Stores a token with its expiration time
    /// </summary>
    /// <param name="token">Access token</param>
    /// <param name="expiresInSeconds">Token lifetime in seconds</param>
    void SetToken(string token, int expiresInSeconds);

    /// <summary>
    /// Clears the cached token
    /// </summary>
    void ClearToken();

    /// <summary>
    /// Checks if the current token is expired
    /// </summary>
    /// <returns>True if token is expired or doesn't exist</returns>
    bool IsTokenExpired();
}

/// <summary>
/// Centralized cache service implementation of EasyPay token store
/// </summary>
public class EasyPayTokenStore : IEasyPayTokenStore
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<EasyPayTokenStore> _logger;
    private const string TokenKey = "easypay_access_token";
    private const int ExpirationBufferSeconds = 60; // Refresh token 1 minute before expiration

    public EasyPayTokenStore(ICacheService cacheService, ILogger<EasyPayTokenStore> logger)
    {
        _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public string? GetToken()
    {
        try
        {
            var cachedToken = _cacheService.GetAsync<string>(TokenKey).GetAwaiter().GetResult();
            if (cachedToken != null)
            {
                _logger.LogDebug("Retrieved cached EasyPay access token from cache service");
                return cachedToken;
            }

            _logger.LogDebug("No valid cached EasyPay access token found in cache service");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving EasyPay access token from cache service");
            return null;
        }
    }

    public void SetToken(string token, int expiresInSeconds)
    {
        if (string.IsNullOrWhiteSpace(token))
        {
            throw new ArgumentException("Token cannot be null or empty", nameof(token));
        }

        if (expiresInSeconds <= 0)
        {
            throw new ArgumentException("Expiration time must be positive", nameof(expiresInSeconds));
        }

        try
        {
            // Apply buffer time to refresh token before it actually expires
            var adjustedExpirationSeconds = Math.Max(1, expiresInSeconds - ExpirationBufferSeconds);
            var duration = TimeSpan.FromSeconds(adjustedExpirationSeconds);

            // Use predefined token cache options with custom duration
            var options = CacheEntryOptions.ForTokens with { Duration = duration };

            _cacheService.SetAsync(TokenKey, token, options).GetAwaiter().GetResult();

            var expiresAt = DateTime.UtcNow.Add(duration);
            _logger.LogInformation("Cached EasyPay access token in cache service. Expires at: {ExpirationTime} UTC",
                expiresAt.ToString("yyyy-MM-dd HH:mm:ss"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching EasyPay access token in cache service");
            throw;
        }
    }

    public void ClearToken()
    {
        try
        {
            _cacheService.RemoveAsync(TokenKey).GetAwaiter().GetResult();
            _logger.LogInformation("Cleared cached EasyPay access token from cache service");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing EasyPay access token from cache service");
            throw;
        }
    }

    public bool IsTokenExpired()
    {
        try
        {
            var exists = _cacheService.ExistsAsync(TokenKey).GetAwaiter().GetResult();
            return !exists; // If token doesn't exist in cache, it's expired
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking token expiration in cache service");
            return true; // Assume expired on error
        }
    }
}
