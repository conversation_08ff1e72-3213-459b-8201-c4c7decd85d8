using System.Globalization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Domain.Exceptions;
using TransferService.Application.Interfaces;
using TransferService.Infrastructure.Options;
using TransferService.Application.Features.EasyPay.Commands;


namespace TransferService.Infrastructure.ExternalServices.EasyPay;

/// <summary>
/// Implementation of EasyPay transfer service operations
/// </summary>
public class EasyPayTransferService(
    IEasyPayAuthenticatedClient apiClient,
    IOptions<ExternalApiOptions> options,
    ILogger<EasyPayTransferService> logger)
    : IEasyPayTransferService
{
    private readonly IEasyPayAuthenticatedClient _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
    private readonly EasyPayOptions _options = options?.Value?.EasyPay ?? throw new ArgumentNullException(nameof(options));
    private readonly ILogger<EasyPayTransferService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<EasyPayNameEnquiryResultDto> NameEnquiryAsync(string accountNumber, string bankCode, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting name enquiry for account {AccountNumber} at bank {BankCode}", accountNumber, bankCode);

            var transactionId = GenerateTransactionId();
            
            var request = new EasyPayNameEnquiryRequest
            {
                AccountNumber = accountNumber,
                ChannelCode = "1",
                DestinationInstitutionCode = bankCode,
                TransactionId = transactionId
            };

            var response = await _apiClient.NameEnquiryAsync(request, cancellationToken);

            var isSuccessful = response.ResponseCode == "00";
            
            _logger.LogInformation("Name enquiry completed for account {AccountNumber}. Success: {IsSuccessful}, ResponseCode: {ResponseCode}", 
                accountNumber, isSuccessful, response.ResponseCode);

            return new EasyPayNameEnquiryResultDto
            {
                AccountName = response.AccountName,
                AccountNumber = response.AccountNumber,
                IsSuccessful = isSuccessful,
                ErrorMessage = isSuccessful ? string.Empty : response.ResponseMessage,
                TransactionId = transactionId
            };
        }
        catch (EasyPayApiException ex)
        {
            _logger.LogError(ex, "EasyPay API error during name enquiry for account {AccountNumber}", accountNumber);
            return new EasyPayNameEnquiryResultDto
            {
                AccountName = string.Empty,
                AccountNumber = accountNumber,
                IsSuccessful = false,
                ErrorMessage = ex.ResponseMessage ?? ex.Message,
                TransactionId = GenerateTransactionId()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during name enquiry for account {AccountNumber}", accountNumber);
            throw new EasyPayNameEnquiryException(accountNumber, bankCode, "Name enquiry failed", ex);
        }
    }

    /// <summary>
    /// Transfer with full payment command including originator details
    /// </summary>
    public async Task<EasyPayTransferResultDto> TransferAsync(EasyPayTransferCommand request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting transfer of {Amount} to account {AccountNumber} at bank {BankCode}",
                request.Amount, request.AccountNumber, request.BankCode);

            var transactionId = GenerateTransactionId();
            var paymentReference = GeneratePaymentReference();

            var easyPayRequest = new EasyPayTransferRequest
            {
                SourceInstitutionCode = _options.SourceInstitutionCode,
                Amount = request.Amount,
                BeneficiaryAccountName = request.AccountName,
                BeneficiaryAccountNumber = request.AccountNumber,
                BeneficiaryBankVerificationNumber = request.OriginatorBankVerificationNumber,
                BeneficiaryKYCLevel = 1,
                ChannelCode = request.ChannelCode,
                OriginatorAccountName = !string.IsNullOrEmpty(request.OriginatorAccountName) ? request.OriginatorAccountName : _options.OriginatorAccountName,
                OriginatorAccountNumber = !string.IsNullOrEmpty(request.OriginatorAccountNumber) ? request.OriginatorAccountNumber : _options.OriginatorAccountNumber,
                OriginatorBankVerificationNumber = !string.IsNullOrEmpty(request.OriginatorBankVerificationNumber) ? long.Parse(request.OriginatorBankVerificationNumber) : ***********, // Default fallback
                OriginatorKYCLevel = !string.IsNullOrEmpty(request.OriginatorKycLevel) ? int.Parse(request.OriginatorKycLevel) : 1, // Default fallback
                DestinationInstitutionCode = long.Parse(request.BankCode),
                MandateReferenceNumber = _options.MandateRef,
                NameEnquiryRef = request.NameEnquiryRef,
                OriginatorNarration = request.Narration,
                PaymentReference = !string.IsNullOrEmpty(request.PaymentReference) ? request.PaymentReference : paymentReference,
                TransactionId = request.SessionId,
                TransactionLocation = !string.IsNullOrEmpty(request.TransactionLocation) ? request.TransactionLocation : "1.38716,3.05117", // Default Lagos coordinates
                BeneficiaryNarration = request.Narration,
                BillerId = _options.BillerId,
                InitiatorAccountNumber = request.OriginatorAccountNumber,
                InitiatorAccountName = request.OriginatorAccountName
            };

            var response = await _apiClient.FundTransferAsync(easyPayRequest, cancellationToken);

            var isSuccessful = response.ResponseCode == "00";

            _logger.LogInformation("Transfer completed for payment reference {PaymentReference}. Success: {IsSuccessful}, ResponseCode: {ResponseCode}", 
                paymentReference, isSuccessful, response.ResponseCode);

            var transactionDto = MapToTransactionDto(response, paymentReference);

            return new EasyPayTransferResultDto
            {
                TransactionId = paymentReference,
                Status = response.ResponseCode, // Use original EasyPay response code
                RequiresProcessing = true,
                FullResult = transactionDto,
                IsSuccessful = isSuccessful,
                ErrorMessage = isSuccessful ? string.Empty : GetEasyPayErrorMessage(response.ResponseCode)
            };
        }
        catch (EasyPayApiException ex)
        {
            _logger.LogError(ex, "EasyPay API error during transfer of {Amount} to account {AccountNumber}", request.Amount, request.AccountNumber);
            
            // Return error result with fallback transaction data
            var fallbackTransactionDto = CreateFallbackTransactionDto(request.Amount, request.AccountNumber, request.BankCode, request.AccountName, request.Narration);
            return new EasyPayTransferResultDto
            {
                TransactionId = GeneratePaymentReference(),
                Status = "00",
                RequiresProcessing = true,
                FullResult = fallbackTransactionDto,
                IsSuccessful = false,
                ErrorMessage = ex.ResponseMessage ?? ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during transfer of {Amount} to account {AccountNumber}", request.Amount, request.AccountNumber);
            throw new EasyPayTransferException(request.Amount, request.AccountNumber, request.BankCode, "Transfer failed", ex);
        }
    }

    public async Task<EasyPayBalanceDto> GetWalletBalanceAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Querying wallet balance");

            var request = new EasyPayBalanceEnquiryRequest
            {
                ChannelCode = "1",
                TargetAccountName = _options.OriginatorAccountName,
                TargetAccountNumber = _options.OriginatorAccountNumber,
                TargetBankVerificationNumber = "***********",
                AuthorizationCode = _options.MandateRef,
                DestinationInstitutionCode = "888564",
                BillerId = _options.BillerId,
                TransactionId = GenerateTransactionId()
            };

            var response = await _apiClient.BalanceEnquiryAsync(request, cancellationToken);

            var isSuccessful = response.StatusCode == "00";

            _logger.LogInformation("Wallet balance query completed. Success: {IsSuccessful}, Balance: {Balance}", 
                isSuccessful, response.Balance);

            return new EasyPayBalanceDto
            {
                Balance = response.Balance,
                Status = response.StatusCode,
                IsSuccessful = isSuccessful,
                ErrorMessage = isSuccessful ? string.Empty : response.ResponseMessage
            };
        }
        catch (EasyPayApiException ex)
        {
            _logger.LogError(ex, "EasyPay API error during wallet balance query");
            return new EasyPayBalanceDto
            {
                Balance = 0,
                Status = "00",
                IsSuccessful = false,
                ErrorMessage = ex.ResponseMessage ?? ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during wallet balance query");
            throw new EasyPayApiException("Wallet balance query failed", ex);
        }
    }

    public async Task<EasyPayCreditWalletResultDto> CreditWalletAsync(decimal amount, CancellationToken cancellationToken = default)
    {
        // Based on TypeScript implementation, this always returns success
        _logger.LogInformation("Credit wallet requested for amount {Amount}", amount);
        
        await Task.Delay(100, cancellationToken); // Simulate processing
        
        return new EasyPayCreditWalletResultDto
        {
            Status = "00",
            IsSuccessful = true,
            ErrorMessage = string.Empty
        };
    }

    public async Task<EasyPayTSQResultDto> TransactionStatusQueryAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Querying transaction status for session {SessionId}", sessionId);

            var request = new EasyPayTSQRequest
            {
                TransactionID = sessionId
            };

            var response = await _apiClient.TransactionStatusQueryAsync(request, cancellationToken);

            var isSuccessful = !string.IsNullOrEmpty(response.StatusCode) && response.StatusCode != "0";

            _logger.LogInformation("Transaction status query completed for session {SessionId}. Status: {StatusCode}", 
                sessionId, response.StatusCode);

            return new EasyPayTSQResultDto
            {
                Status = response.StatusCode,
                IsSuccessful = isSuccessful,
                ErrorMessage = isSuccessful ? string.Empty : response.ResponseMessage
            };
        }
        catch (EasyPayApiException ex)
        {
            _logger.LogError(ex, "EasyPay API error during TSQ for session {SessionId}", sessionId);
            return new EasyPayTSQResultDto
            {
                Status = "0",
                IsSuccessful = false,
                ErrorMessage = ex.ResponseMessage ?? ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during TSQ for session {SessionId}", sessionId);
            return new EasyPayTSQResultDto
            {
                Status = "0",
                IsSuccessful = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private string GenerateTransactionId()
    {
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString()[^6..]; // Last 6 digits
        var randomPart = Random.Shared.Next(100000, 999999).ToString(); // 6 random digits
        var formattedNow = DateTime.UtcNow.ToString("yyMMddHHmmss");
        return $"{_options.InstitutionCode}{formattedNow}{timestamp}{randomPart}";
    }

    private string GeneratePaymentReference()
    {
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString()[^6..]; // Last 6 digits
        var randomPart = Random.Shared.Next(100000, 999999).ToString(); // 6 random digits
        var formattedNow = DateTime.UtcNow.ToString("yyMMddHHmmss");
        // Using a default company code since it's not in configuration
        return $"TSF{formattedNow}{timestamp}{randomPart}";
    }



    /// <summary>
    /// Gets the appropriate error message for EasyPay response codes
    /// Based on official NIBSS EasyPay response codes documentation
    /// </summary>
    /// <param name="responseCode">EasyPay response code</param>
    /// <returns>Descriptive error message</returns>
    private string GetEasyPayErrorMessage(string responseCode)
    {
        var message = responseCode switch
        {
            "01" => "Status unknown, please wait for settlement report",
            "03" => "Invalid Sender",
            "05" => "Do not honor",
            "06" => "Dormant Account",
            "07" => "Invalid Account",
            "08" => "Account Name Mismatch",
            "09" => "Request processing in progress",
            "12" => "Invalid transaction",
            "13" => "Invalid Amount",
            "14" => "Invalid Batch Number",
            "15" => "Invalid Session or Record ID",
            "16" => "Unknown Bank Code",
            "17" => "Invalid Channel",
            "18" => "Wrong Method Call",
            "21" => "No action taken",
            "25" => "Unable to locate record",
            "26" => "Duplicate record",
            "30" => "Format error",
            "34" => "Suspected fraud",
            "35" => "Contact sending bank",
            "51" => "Insufficient funds",
            "57" => "Transaction not permitted to sender",
            "58" => "Transaction not permitted on channel",
            "61" => "Transfer limit Exceeded",
            "63" => "Security violation",
            "65" => "Exceeds withdrawal frequency",
            "68" => "Response received too late",
            "69" => "Unsuccessful Account/Amount block",
            "70" => "Unsuccessful Account/Amount unblock",
            "71" => "Empty Mandate Reference Number",
            "91" => "Beneficiary Bank not available",
            "92" => "Routing error",
            "94" => "Duplicate transaction",
            "96" => "System malfunction",
            _ => "Transfer failed"
        };

        return $"{message} (Code: {responseCode})";
    }

    /// <summary>
    /// Gets the clean status message for EasyPay response codes (without code suffix)
    /// </summary>
    /// <param name="responseCode">EasyPay response code</param>
    /// <returns>Clean status message</returns>
    private string GetEasyPayStatusMessage(string responseCode)
    {
        return responseCode switch
        {
            "00" => "SUCCESS",
            "01" => "Status unknown, please wait for settlement report",
            "03" => "Invalid Sender",
            "05" => "Do not honor",
            "06" => "Dormant Account",
            "07" => "Invalid Account",
            "08" => "Account Name Mismatch",
            "09" => "Request processing in progress",
            "12" => "Invalid transaction",
            "13" => "Invalid Amount",
            "14" => "Invalid Batch Number",
            "15" => "Invalid Session or Record ID",
            "16" => "Unknown Bank Code",
            "17" => "Invalid Channel",
            "18" => "Wrong Method Call",
            "21" => "No action taken",
            "25" => "Unable to locate record",
            "26" => "Duplicate record",
            "30" => "Format error",
            "34" => "Suspected fraud",
            "35" => "Contact sending bank",
            "51" => "Insufficient funds",
            "57" => "Transaction not permitted to sender",
            "58" => "Transaction not permitted on channel",
            "61" => "Transfer limit Exceeded",
            "63" => "Security violation",
            "65" => "Exceeds withdrawal frequency",
            "68" => "Response received too late",
            "69" => "Unsuccessful Account/Amount block",
            "70" => "Unsuccessful Account/Amount unblock",
            "71" => "Empty Mandate Reference Number",
            "91" => "Beneficiary Bank not available",
            "92" => "Routing error",
            "94" => "Duplicate transaction",
            "96" => "System malfunction",
            _ => "FAILED"
        };
    }

    private EasyPayTransactionDto MapToTransactionDto(FundTransferDto response, string paymentReference)
    {
        return new EasyPayTransactionDto
        {
            Amount = response.Amount.ToString(CultureInfo.InvariantCulture),
            BenAccountName = response.BeneficiaryAccountName,
            BenAccountNumber = response.BeneficiaryAccountNumber,
            BenBVN = response.BeneficiaryBankVerificationNumber,
            BenKYC = response.BeneficiaryKYCLevel,
            ChannelName = response.ChannelCode.ToString(),
            DestinationInstitution = response.DestinationInstitutionCode,
            Narration = response.Narration,
            OrigAccountName = response.OriginatorAccountName,
            OrigAccountNumber = response.OriginatorAccountNumber,
            Reference = response.PaymentReference,
            OrigBVN = response.OriginatorBankVerificationNumber,
            OrigKYC = response.OriginatorKYCLevel,
            StatusCode = response.ResponseCode, // Use original EasyPay response code
            StatusMessage = GetEasyPayStatusMessage(response.ResponseCode),
            TransactionID = paymentReference
        };
    }

    private EasyPayTransactionDto CreateFallbackTransactionDto(decimal amount, string accountNumber, string bankCode, string accountName, string? narration)
    {
        return new EasyPayTransactionDto
        {
            Amount = amount.ToString(CultureInfo.InvariantCulture),
            BenAccountName = accountName,
            BenAccountNumber = accountNumber,
            BenBVN = "***********",
            BenKYC = "1",
            ChannelName = "1",
            DestinationInstitution = bankCode,
            Narration = narration,
            OrigAccountName = _options.OriginatorAccountName,
            OrigAccountNumber = _options.OriginatorAccountNumber,
            Reference = GeneratePaymentReference(),
            OrigBVN = "***********",
            OrigKYC = "3",
            StatusCode = "00",
            StatusMessage = "Transfer failed",
            TransactionID = GeneratePaymentReference()
        };
    }
}
