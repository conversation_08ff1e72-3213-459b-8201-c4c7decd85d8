using Refit;
using TransferService.Application.Features.Transfers.Models;

namespace TransferService.Infrastructure.ExternalServices.EasyPay;

/// <summary>
/// Refit client interface for EasyPay API operations using XML format
/// </summary>
public interface IEasyPayApiClient
{
    /// <summary>
    /// Performs name enquiry to validate account details
    /// </summary>
    /// <param name="request">Name enquiry request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Name enquiry response</returns>
    [Post("/nameenquiry")]
    [Headers("Content-Type: application/json")]
    Task<EasyPayNameEnquiryResponse> NameEnquiryAsync([Body] EasyPayNameEnquiryRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs fund transfer operation
    /// </summary>
    /// <param name="request">Fund transfer request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Fund transfer response</returns>
    [Post("/fundstransfer")]
    [Headers("Content-Type: application/json")]
    Task<FundTransferDto> FundTransferAsync([Body] EasyPayTransferRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs balance enquiry
    /// </summary>
    /// <param name="request">Balance enquiry request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Balance enquiry response</returns>
    [Post("/balanceenquiry")]
    [Headers("Content-Type: application/json")]
    Task<EasyPayBalanceEnquiryResponse> BalanceEnquiryAsync([Body] EasyPayBalanceEnquiryRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs Transaction Status Query (TSQ)
    /// </summary>
    /// <param name="request">TSQ request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>TSQ response</returns>
    [Post("/tsq")]
    [Headers("Content-Type: application/json")]
    Task<EasyPayTSQResponse> TransactionStatusQueryAsync([Body] EasyPayTSQRequest request, CancellationToken cancellationToken = default);
}
