using Refit;
using System.Text.Json.Serialization;

namespace TransferService.Infrastructure.ExternalServices.EasyPay;

/// <summary>
/// Refit client interface for EasyPay OAuth2 token operations
/// </summary>
public interface IEasyPayTokenClient
{
    /// <summary>
    /// Obtains an OAuth2 access token using client credentials flow
    /// </summary>
    /// <param name="request">Token request with client credentials</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Token response with access token and expiration</returns>
    [Post("/reset")]
    [Headers("Content-Type: application/x-www-form-urlencoded")]
    Task<EasyPayTokenApiResponse> GetTokenAsync([Body(BodySerializationMethod.UrlEncoded)] EasyPayTokenFormRequest request, [Header("apikey")] string apiKey, CancellationToken cancellationToken = default);
}

/// <summary>
/// Token request for URL-encoded form data
/// </summary>
public class EasyPayTokenFormRequest
{
    [AliasAs("client_id")]
    public string ClientId { get; set; } = string.Empty;

    [AliasAs("scope")]
    public string Scope { get; set; } = string.Empty;

    [AliasAs("client_secret")]
    public string ClientSecret { get; set; } = string.Empty;

    [AliasAs("grant_type")]
    public string GrantType { get; set; } = "client_credentials";
}

/// <summary>
/// Token response from EasyPay OAuth2 endpoint
/// </summary>
public class EasyPayTokenApiResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; } = string.Empty;

    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }

    [JsonPropertyName("ext_expires_in")]
    public int ExtExpiresIn { get; set; }

    [JsonPropertyName("token_type")]
    public string TokenType { get; set; } = "Bearer";
}
