using Refit;

namespace TransferService.Infrastructure.ExternalServices;

public interface ITransferApiClient
{
    [Post("/api/transfers")]
    Task<TransferApiResponse> ProcessTransferAsync([Body] TransferApiRequest request, CancellationToken cancellationToken = default);

    [Get("/api/transfers/{transactionId}")]
    Task<TransferStatusResponse> GetTransferStatusAsync(string transactionId, CancellationToken cancellationToken = default);
}

public record TransferApiRequest
{
    public string FromAccount { get; init; } = string.Empty;
    public string ToAccount { get; init; } = string.Empty;
    public decimal Amount { get; init; }
    public string Currency { get; init; } = "NGN";
    public string Description { get; init; } = string.Empty;
    public string RequestId { get; init; } = string.Empty;
}

public record TransferApiResponse
{
    public string TransactionId { get; init; } = string.Empty;
    public string Status { get; init; } = string.Empty;
    public string Message { get; init; } = string.Empty;
    public DateTime ProcessedAt { get; init; }
}

public record TransferStatusResponse
{
    public string TransactionId { get; init; } = string.Empty;
    public string Status { get; init; } = string.Empty;
    public string Message { get; init; } = string.Empty;
    public DateTime? CompletedAt { get; init; }
}
