using System.Xml.Serialization;
using Refit;

namespace TransferService.Infrastructure.ExternalServices;

public interface IXmlApiClient
{
    [Post("/api/xml/transfers")]
    [Headers("Content-Type: application/xml")]
    Task<XmlTransferResponse> ProcessXmlTransferAsync([Body(BodySerializationMethod.Serialized)] XmlTransferRequest request, CancellationToken cancellationToken = default);
}

[XmlRoot("TransferRequest")]
public class XmlTransferRequest
{
    [XmlElement("FromAccount")]
    public string FromAccount { get; set; } = string.Empty;

    [XmlElement("ToAccount")]
    public string ToAccount { get; set; } = string.Empty;

    [XmlElement("Amount")]
    public decimal Amount { get; set; }

    [XmlElement("Currency")]
    public string Currency { get; set; } = "NGN";

    [XmlElement("Description")]
    public string Description { get; set; } = string.Empty;

    [XmlElement("RequestId")]
    public string RequestId { get; set; } = string.Empty;

    [XmlElement("Timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

[XmlRoot("TransferResponse")]
public class XmlTransferResponse
{
    [XmlElement("TransactionId")]
    public string TransactionId { get; set; } = string.Empty;

    [XmlElement("Status")]
    public string Status { get; set; } = string.Empty;

    [XmlElement("Message")]
    public string Message { get; set; } = string.Empty;

    [XmlElement("ProcessedAt")]
    public DateTime ProcessedAt { get; set; }

    [XmlElement("ErrorCode")]
    public string? ErrorCode { get; set; }
}
