using Microsoft.Extensions.Logging;
using TransferService.Domain.Interfaces;

namespace TransferService.Infrastructure.ExternalServices;

public class TransferApiService(
    ITransferApiClient transferApiClient,
    IXmlApiClient xmlApiClient,
    ILogger<TransferApiService> logger)
    : ITransferApiService
{
    public async Task<string> ProcessTransferAsync(string fromAccount, string toAccount, decimal amount, string currency, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Processing transfer from {FromAccount} to {ToAccount} for {Amount} {Currency}", 
                fromAccount, toAccount, amount, currency);

            var request = new TransferApiRequest
            {
                FromAccount = fromAccount,
                ToAccount = toAccount,
                Amount = amount,
                Currency = currency,
                Description = $"Transfer from {fromAccount} to {toAccount}",
                RequestId = Guid.NewGuid().ToString()
            };

            var response = await transferApiClient.ProcessTransferAsync(request, cancellationToken);

            logger.LogInformation("Transfer processed successfully. TransactionId: {TransactionId}, Status: {Status}", 
                response.TransactionId, response.Status);

            return response.TransactionId;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing transfer from {FromAccount} to {ToAccount}", fromAccount, toAccount);
            throw;
        }
    }

    public async Task<string> ProcessXmlTransferAsync(string fromAccount, string toAccount, decimal amount, string currency, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Processing XML transfer from {FromAccount} to {ToAccount} for {Amount} {Currency}", 
                fromAccount, toAccount, amount, currency);

            var request = new XmlTransferRequest
            {
                FromAccount = fromAccount,
                ToAccount = toAccount,
                Amount = amount,
                Currency = currency,
                Description = $"XML Transfer from {fromAccount} to {toAccount}",
                RequestId = Guid.NewGuid().ToString(),
                Timestamp = DateTime.UtcNow
            };

            var response = await xmlApiClient.ProcessXmlTransferAsync(request, cancellationToken);

            logger.LogInformation("XML transfer processed successfully. TransactionId: {TransactionId}, Status: {Status}", 
                response.TransactionId, response.Status);

            return response.TransactionId;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing XML transfer from {FromAccount} to {ToAccount}", fromAccount, toAccount);
            throw;
        }
    }
}
