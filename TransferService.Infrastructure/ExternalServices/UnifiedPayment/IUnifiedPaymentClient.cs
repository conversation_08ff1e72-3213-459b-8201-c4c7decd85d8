using System.Xml.Serialization;
using Refit;

namespace TransferService.Infrastructure.ExternalServices;

public interface IUnifiedPaymentClient
{
    [Get("/name-enquiry/{bankCode}/{benAccount}")]
    Task<NameEnqResponse> GetNameEnqAsync(string bankCode, string benAccount, CancellationToken cancellationToken = default);

    [Get("/name-enquiry/{bankCode}/{benAccount}")]
    [Headers("Content-Type: application/xml")]
    Task<NameEnqResponse> GetNameEnqXMLAsync(string bankCode, string benAccount, CancellationToken cancellationToken = default);

    [Get("/transfer-status/{id}")]
    Task<CreditStatusResponse> GetTSQAsync(string id, CancellationToken cancellationToken = default);

    [Get("/transfer-status/{id}")]
    [Headers("Content-Type: application/xml")]
    Task<CreditStatusResponse> GetTSQXMLAsync(string id, CancellationToken cancellationToken = default);


    [Post("/credit")]
    [Headers("Content-Type: application/xml")]
    Task<HttpContent> CreditTransferXMLAsync([Body(BodySerializationMethod.Serialized)] CreditRequest request, CancellationToken cancellationToken = default);

}

[XmlRoot("NameEnqiry")]
public record NameEnqRequest
{
    [XmlElement("AccountNumber")]
    public required string AccountNumber { get; set; }
}

[XmlRoot("NameEnquiryResponse")]
public record NameEnqResponse
{
    [XmlElement("Data")]
    public string? Data { get; set; }
}


[XmlRoot("TransferQuery")]
public record TransferQuery
{
    [XmlElement("TransactionId")]
    public required string TransactionId { get; set; }
}

[XmlRoot("TransferResponse")]
public record TransferResponse
{
    [XmlElement("TransactionId")]
    public string? TransactionId { get; set; }

    [XmlElement("ResponseCode")]
    public string? ResponseCode { get; set; }

    [XmlElement("Description")]
    public string? Description { get; set; }

    [XmlElement("RefId")]
    public string? RefId { get; set; }
}

[XmlRoot("CreditResponse")]
public record CreditStatusResponse
{
    [XmlElement("Data")]
    public string? Data { get; set; }

}
[XmlRoot("Credit")]
public class CreditTransferRequest
{
    [XmlElement("TrxId")]
    public string? TrxId { get; set; }

    [XmlElement("RefId")]
    public string? RefId { get; set; }

    [XmlElement("BeneficiaryAccountNo")]
    public string? BeneficiaryAccountNo { get; set; }

    [XmlElement("BeneficiaryName")]
    public string? BeneficiaryName { get; set; }

    [XmlElement("BeneficiaryBank")]
    public string? BeneficiaryBank { get; set; }

    [XmlElement("SenderName")]
    public string? SenderName { get; set; }

    [XmlElement("SenderAccountNo")]
    public string? SenderAccountNo { get; set; }
     [XmlElement("SenderBank")]
    public string? SenderBank { get; set; }

    [XmlElement("Fee")]
    public decimal? Fee { get; set; }

    [XmlElement("Amount")]
    public decimal? Amount { get; set; }

     [XmlElement("Narration")]
    public string? Narration { get; set; }
}

[XmlRoot("CreditTransferResponse")]
public record CreditTransferResponse
{
    [XmlElement("Data")]
    public string? Data { get; set; }
}

[XmlRoot("CreditRequest")]
public sealed record CreditRequest
{
    [XmlElement("Data")]
    public string? Data { get; set; }   
}