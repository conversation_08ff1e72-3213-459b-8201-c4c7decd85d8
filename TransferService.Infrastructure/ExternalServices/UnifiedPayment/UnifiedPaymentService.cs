using System.Text.Json;
using System.Xml.Serialization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using TransferService.Application.Common.Extensions;
using TransferService.Application.Common.Formatters;
using TransferService.Application.Features.UnifiedPayment.Models;
using TransferService.Application.Interfaces;
using TransferService.Domain.Interfaces.UnifiedPayment;

namespace TransferService.Infrastructure.ExternalServices.UnifiedPayment;

public class UnifiedPaymentService(IUnifiedPaymentClient unifiedPaymentClient, ILogger<UnifiedPaymentService> logger, IConfiguration configuration) : IUnifiedPaymentService
{
    public async Task<HttpContent> PerformNameEnqiryAsync(string bankCode, string accountNumber, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation($"Processing name enquiry from {accountNumber} with bankCode {bankCode}");

            var response = await unifiedPaymentClient.GetNameEnqXMLAsync(bankCode, accountNumber, cancellationToken);
            var content = new XmlContentSerializer().ToHttpContent(response);
            return content;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error processing name enquiry {accountNumber}");
            throw;
        }

    }

    public async Task<HttpContent> TSQEnqiryAsync(string transactionId, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation($"Processing tsq for transactionId-{transactionId}");

            var request = new TransferQuery
            {
                TransactionId = transactionId
            };

            var response = await unifiedPaymentClient.GetTSQXMLAsync(request.TransactionId, cancellationToken);
            var content = new XmlContentSerializer().ToHttpContent(response);
            return content;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error processing tsq for transactionId-{transactionId}");
            throw;
        }

    }

    public async Task<HttpContent> ProcessTransferAsync(object transferRequest, CancellationToken cancellationToken = default)
    {
        var req = (PaymentTransferDto)transferRequest;
        //PaymentTransferDto 
        try
        {
            logger.LogInformation("Processing transfer from {FromAccount} to {ToAccount} for {Amount}",
                req.OriginatorAccountNumber, req.AccountNumber, req.Amount);

            //a solution to solve encryption problem

            var composeXmlRequest =
            $"""
            <Credit>
                <TrxId>{req.SessionId}</TrxId>
                <RefId>{req.PaymentReference}</RefId>
                <BeneficiaryAccountNo>{req.AccountNumber}</BeneficiaryAccountNo>
                <BeneficiaryName>{req.AccountName}</BeneficiaryName>
                <BeneficiaryBank>{req.BankCode}</BeneficiaryBank>
                <SenderName>{req.OriginatorAccountName}</SenderName>
                <Amount>{req.Amount}</Amount>
                <SenderAccountNo>{req.OriginatorAccountNumber}</SenderAccountNo>
                <Fee></Fee>
                <Narration>{req.Narration}</Narration>
            </Credit>
            """.Trim();



            //for UP, request should be encrypted. the request body should like this:
            //<CreditRequest><Data>HEX</Data></CreditRequest>
            //var content = new XmlContentSerializer().ToHttpContent(request);
            //var bodyString = await content.ReadAsStringAsync();
            //bodyString.Replace("", "");

            //var bodyString = JsonSerializer.Serialize(request);
            string encryptedResult = composeXmlRequest.AES_Encrypt(configuration["ExternalApis:UnifiedPayment:IV"], configuration["ExternalApis:UnifiedPayment:Key"]);

            // var creditRequestEncrypted = new CreditRequest
            // {
            //     Data = encryptedResult
            // };

            //var decrypt = encryptedResult.AES_Decrypt(configuration["ExternalApis:UnifiedPayment:IV"], configuration["ExternalApis:UnifiedPayment:Key"]);

            var composeXmlWrapper = $"<CreditRequest><Data>{encryptedResult}</Data></CreditRequest>";

            var credit = XmlToObject<CreditRequest>.Convert(composeXmlWrapper);

            var response = await unifiedPaymentClient.CreditTransferXMLAsync(credit, cancellationToken);
            //var content = new XmlContentSerializer().ToHttpContent(response);
            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing transfer from {FromAccount} to {ToAccount}", req.OriginatorAccountNumber, req.AccountNumber);
            throw;
        }
    }
}