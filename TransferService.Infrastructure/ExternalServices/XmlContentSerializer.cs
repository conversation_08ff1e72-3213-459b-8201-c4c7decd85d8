using System.Reflection;
using System.Text;
using System.Xml.Serialization;
using Refit;
namespace TransferService.Infrastructure.ExternalServices;

public class XmlContentSerializer : IHttpContentSerializer
{

   //Serializes your C# object into XML and returns HttpContent of content type xml
    public HttpContent ToHttpContent<T>(T item)
    {
        var stream = new MemoryStream();
        var serializer = new XmlSerializer(item!.GetType());
        serializer.Serialize(stream, item);
        stream.Position = 0;

        var content = new StreamContent(stream);
        content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/xml");
        return content;
    }

    //Deserializes XML response into your target type.
    public async Task<T?> FromHttpContentAsync<T>(HttpContent content, CancellationToken cancellationToken = default)
    {
        var stream = await content.ReadAsStreamAsync(cancellationToken);
        var serializer = new XmlSerializer(typeof(T));
        return (T?)serializer.Deserialize(stream);
    }
    //Used for mapping field/property names — usually safe to return propertyInfo.Name
    public string? GetFieldNameForProperty(PropertyInfo propertyInfo)
    {
        return propertyInfo.Name;
    }
}

