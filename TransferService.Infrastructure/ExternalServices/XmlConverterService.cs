using System.Text;
using System.Xml;
using System.Xml.Serialization;
using Microsoft.Extensions.Logging;
using System.Web;

namespace TransferService.Infrastructure.ExternalServices;

public interface IXmlConverterService
{
    Task<T> DeserializeFromXmlAsync<T>(string xmlContent) where T : class;
    Task<string> SerializeToXmlAsync<T>(T obj) where T : class;
    Task<T> DeserializeFromStreamAsync<T>(Stream xmlStream) where T : class;
    Task WriteToStreamAsync<T>(T obj, Stream stream) where T : class;
    string EscapeXmlString(string input);
    string UnescapeXmlString(string input);
}

public class XmlConverterService(ILogger<XmlConverterService> logger) : IXmlConverterService
{
    public Task<T> DeserializeFromXmlAsync<T>(string xmlContent) where T : class
    {
        try
        {
            logger.LogDebug("Deserializing XML to {Type}", typeof(T).Name);

            if (string.IsNullOrWhiteSpace(xmlContent))
            {
                throw new ArgumentException("XML content cannot be null or empty", nameof(xmlContent));
            }

            var serializer = new XmlSerializer(typeof(T));
            
            using var stringReader = new StringReader(xmlContent);
            using var xmlReader = XmlReader.Create(stringReader, new XmlReaderSettings
            {
                IgnoreWhitespace = true,
                IgnoreComments = true
            });

            var result = (T?)serializer.Deserialize(xmlReader);
            
            if (result == null)
            {
                throw new InvalidOperationException($"Failed to deserialize XML to {typeof(T).Name}");
            }

            logger.LogDebug("Successfully deserialized XML to {Type}", typeof(T).Name);
            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error deserializing XML to {Type}", typeof(T).Name);
            throw;
        }
    }

    public Task<string> SerializeToXmlAsync<T>(T obj) where T : class
    {
        try
        {
            logger.LogDebug("Serializing {Type} to XML", typeof(T).Name);

            ArgumentNullException.ThrowIfNull(obj);

            var serializer = new XmlSerializer(typeof(T));
            
            using var stringWriter = new StringWriter();
            using var xmlWriter = XmlWriter.Create(stringWriter, new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = Encoding.UTF8,
                OmitXmlDeclaration = false
            });

            // Remove default namespaces
            var namespaces = new XmlSerializerNamespaces();
            namespaces.Add("", "");

            serializer.Serialize(xmlWriter, obj, namespaces);
            
            var result = stringWriter.ToString();
            
            logger.LogDebug("Successfully serialized {Type} to XML", typeof(T).Name);
            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error serializing {Type} to XML", typeof(T).Name);
            throw;
        }
    }

    public Task<T> DeserializeFromStreamAsync<T>(Stream xmlStream) where T : class
    {
        try
        {
            logger.LogDebug("Deserializing XML stream to {Type}", typeof(T).Name);

            ArgumentNullException.ThrowIfNull(xmlStream);

            var serializer = new XmlSerializer(typeof(T));
            
            using var xmlReader = XmlReader.Create(xmlStream, new XmlReaderSettings
            {
                IgnoreWhitespace = true,
                IgnoreComments = true
            });

            var result = (T?)serializer.Deserialize(xmlReader);
            
            if (result == null)
            {
                throw new InvalidOperationException($"Failed to deserialize XML stream to {typeof(T).Name}");
            }

            logger.LogDebug("Successfully deserialized XML stream to {Type}", typeof(T).Name);
            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error deserializing XML stream to {Type}", typeof(T).Name);
            throw;
        }
    }

    public Task WriteToStreamAsync<T>(T obj, Stream stream) where T : class
    {
        try
        {
            logger.LogDebug("Serializing {Type} to XML stream", typeof(T).Name);

            ArgumentNullException.ThrowIfNull(obj);

            ArgumentNullException.ThrowIfNull(stream);

            var serializer = new XmlSerializer(typeof(T));
            
            using var xmlWriter = XmlWriter.Create(stream, new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = Encoding.UTF8,
                OmitXmlDeclaration = false
            });

            // Remove default namespaces
            var namespaces = new XmlSerializerNamespaces();
            namespaces.Add("", "");

            serializer.Serialize(xmlWriter, obj, namespaces);
            
            logger.LogDebug("Successfully serialized {Type} to XML stream", typeof(T).Name);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error serializing {Type} to XML stream", typeof(T).Name);
            throw;
        }

        return Task.CompletedTask;
    }

    public string EscapeXmlString(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return HttpUtility.HtmlEncode(input);
    }

    public string UnescapeXmlString(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return HttpUtility.HtmlDecode(input);
    }
}
