using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using Serilog.Formatting.Json;

namespace TransferService.Infrastructure.Logging;

public static class SerilogConfiguration
{
    public static IServiceCollection AddSerilogLogging(this IServiceCollection services, IConfiguration configuration, IHostEnvironment environment)
    {
        // Create logger configuration - everything is now configured in appsettings.json
        var loggerConfiguration = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .Enrich.FromLogContext()
            .Enrich.WithProperty("Application", "TransferService")
            .Enrich.WithProperty("Environment", environment.EnvironmentName)
            .Enrich.WithMachineName()
            .Enrich.WithThreadId();

        // Create and configure the logger
        Log.Logger = loggerConfiguration.CreateLogger();

        // Add Serilog to DI container
        services.AddSerilog(Log.Logger);

        return services;
    }

    public static void ConfigureRequestLogging()
    {
        // Configure Serilog request logging
        Log.Logger = new LoggerConfiguration()
            .Enrich.FromLogContext()
            .WriteTo.Console()
            .CreateLogger();
    }
}

public static class LoggingExtensions
{
    public static Serilog.ILogger CreateLogger<T>() => Log.ForContext<T>();

    public static Serilog.ILogger CreateLogger(string sourceContext) => Log.ForContext("SourceContext", sourceContext);
}
