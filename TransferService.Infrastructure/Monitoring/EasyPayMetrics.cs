using System.Diagnostics;
using Microsoft.Extensions.Logging;

namespace TransferService.Infrastructure.Monitoring;

/// <summary>
/// Performance monitoring and metrics for EasyPay operations
/// </summary>
public interface IEasyPayMetrics
{
    /// <summary>
    /// Records the duration of a name enquiry operation
    /// </summary>
    void RecordNameEnquiryDuration(TimeSpan duration, bool success);

    /// <summary>
    /// Records the duration of a transfer operation
    /// </summary>
    void RecordTransferDuration(TimeSpan duration, bool success, decimal amount);

    /// <summary>
    /// Records the duration of a balance enquiry operation
    /// </summary>
    void RecordBalanceEnquiryDuration(TimeSpan duration, bool success);

    /// <summary>
    /// Records the duration of a TSQ operation
    /// </summary>
    void RecordTSQDuration(TimeSpan duration, bool success);

    /// <summary>
    /// Records token refresh operations
    /// </summary>
    void RecordTokenRefresh(TimeSpan duration, bool success);

    /// <summary>
    /// Increments error counter for specific error types
    /// </summary>
    void IncrementErrorCount(string errorType);
}

/// <summary>
/// Implementation of EasyPay metrics using structured logging
/// </summary>
public class EasyPayMetrics : IEasyPayMetrics
{
    private readonly ILogger<EasyPayMetrics> _logger;

    public EasyPayMetrics(ILogger<EasyPayMetrics> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public void RecordNameEnquiryDuration(TimeSpan duration, bool success)
    {
        _logger.LogInformation("EasyPay Name Enquiry completed in {Duration}ms. Success: {Success}",
            duration.TotalMilliseconds, success);

        if (duration.TotalSeconds > 10)
        {
            _logger.LogWarning("EasyPay Name Enquiry took longer than expected: {Duration}ms", 
                duration.TotalMilliseconds);
        }
    }

    public void RecordTransferDuration(TimeSpan duration, bool success, decimal amount)
    {
        _logger.LogInformation("EasyPay Transfer of {Amount} completed in {Duration}ms. Success: {Success}",
            amount, duration.TotalMilliseconds, success);

        if (duration.TotalSeconds > 30)
        {
            _logger.LogWarning("EasyPay Transfer took longer than expected: {Duration}ms for amount {Amount}", 
                duration.TotalMilliseconds, amount);
        }
    }

    public void RecordBalanceEnquiryDuration(TimeSpan duration, bool success)
    {
        _logger.LogInformation("EasyPay Balance Enquiry completed in {Duration}ms. Success: {Success}",
            duration.TotalMilliseconds, success);

        if (duration.TotalSeconds > 10)
        {
            _logger.LogWarning("EasyPay Balance Enquiry took longer than expected: {Duration}ms", 
                duration.TotalMilliseconds);
        }
    }

    public void RecordTSQDuration(TimeSpan duration, bool success)
    {
        _logger.LogInformation("EasyPay TSQ completed in {Duration}ms. Success: {Success}",
            duration.TotalMilliseconds, success);

        if (duration.TotalSeconds > 10)
        {
            _logger.LogWarning("EasyPay TSQ took longer than expected: {Duration}ms", 
                duration.TotalMilliseconds);
        }
    }

    public void RecordTokenRefresh(TimeSpan duration, bool success)
    {
        _logger.LogInformation("EasyPay Token Refresh completed in {Duration}ms. Success: {Success}",
            duration.TotalMilliseconds, success);

        if (duration.TotalSeconds > 5)
        {
            _logger.LogWarning("EasyPay Token Refresh took longer than expected: {Duration}ms", 
                duration.TotalMilliseconds);
        }
    }

    public void IncrementErrorCount(string errorType)
    {
        _logger.LogError("EasyPay Error occurred: {ErrorType}", errorType);
    }
}

/// <summary>
/// Extension methods for adding performance monitoring to EasyPay operations
/// </summary>
public static class EasyPayMetricsExtensions
{
    /// <summary>
    /// Executes an operation with performance monitoring
    /// </summary>
    public static async Task<T> ExecuteWithMetricsAsync<T>(
        this IEasyPayMetrics metrics,
        Func<Task<T>> operation,
        Action<TimeSpan, bool> recordMetrics,
        string operationName)
    {
        var stopwatch = Stopwatch.StartNew();
        var success = false;
        
        try
        {
            var result = await operation();
            success = true;
            return result;
        }
        catch (Exception)
        {
            success = false;
            throw;
        }
        finally
        {
            stopwatch.Stop();
            recordMetrics(stopwatch.Elapsed, success);
        }
    }
}
