namespace TransferService.Infrastructure.Options;

/// <summary>
/// Configuration options for caching strategies
/// </summary>
public class CacheOptions
{
    /// <summary>
    /// Default cache duration in minutes
    /// </summary>
    public int DefaultDurationMinutes { get; set; } = 30;

    /// <summary>
    /// Redis connection string for distributed caching
    /// </summary>
    public string? RedisConnectionString { get; set; }

    /// <summary>
    /// Enable or disable distributed caching
    /// </summary>
    public bool EnableDistributedCache { get; set; } = true;

    /// <summary>
    /// Memory cache size limit in MB
    /// </summary>
    public int MemoryCacheSizeLimitMB { get; set; } = 100;

    /// <summary>
    /// Cache key prefix for this application
    /// </summary>
    public string KeyPrefix { get; set; } = "TransferService";
}
