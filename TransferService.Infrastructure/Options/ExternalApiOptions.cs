namespace TransferService.Infrastructure.Options;

/// <summary>
/// Configuration options for external API integrations
/// </summary>
public class ExternalApiOptions
{
    public TransferApiOptions TransferApi { get; set; } = new();
    public XmlApiOptions XmlApi { get; set; } = new();
    public EasyPayOptions EasyPay { get; set; } = new();
}

/// <summary>
/// Configuration options for Transfer API
/// </summary>
public class TransferApiOptions
{
    public string BaseUrl { get; set; } = string.Empty;
    public string ApiKey { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 30;
}

/// <summary>
/// Configuration options for XML API
/// </summary>
public class XmlApiOptions
{
    public string BaseUrl { get; set; } = string.Empty;
    public string ApiKey { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 45;
}

/// <summary>
/// Configuration options for EasyPay NIBSS integration
/// </summary>
public class EasyPayOptions
{
    public string BaseUrl { get; set; } = string.Empty;
    public string TokenResetUrl { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
    public string ApiKey { get; set; } = string.Empty;
    public string InstitutionCode { get; set; } = string.Empty;
    public string SourceInstitutionCode { get; set; } = string.Empty;
    public string BillerId { get; set; } = string.Empty;
    public string MandateRef { get; set; } = string.Empty;
    public string OriginatorAccountName { get; set; } = string.Empty;
    public string OriginatorAccountNumber { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 30;
    public CacheStrategy CacheStrategy { get; set; } = CacheStrategy.FusionCache;
    public string? RedisConnectionString { get; set; }
}

/// <summary>
/// Cache strategy enumeration for EasyPay service
/// </summary>
public enum CacheStrategy
{
    /// <summary>
    /// FusionCache with multi-level caching (memory + Redis)
    /// </summary>
    FusionCache
}
