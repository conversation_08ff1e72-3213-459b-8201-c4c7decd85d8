namespace TransferService.Infrastructure.Options;

/// <summary>
/// Configuration options for application logging
/// </summary>
public class LoggingOptions
{
    /// <summary>
    /// File path for log files
    /// </summary>
    public string FilePath { get; set; } = "logs";

    /// <summary>
    /// Enable or disable file logging
    /// </summary>
    public bool EnableFileLogging { get; set; } = true;

    /// <summary>
    /// Enable or disable console logging
    /// </summary>
    public bool EnableConsoleLogging { get; set; } = true;

    /// <summary>
    /// Maximum file size in MB before rolling
    /// </summary>
    public int MaxFileSizeMB { get; set; } = 100;

    /// <summary>
    /// Number of log files to retain
    /// </summary>
    public int RetainedFileCount { get; set; } = 30;

    /// <summary>
    /// Minimum log level for file logging
    /// </summary>
    public string MinimumLevel { get; set; } = "Information";

    /// <summary>
    /// Enable structured logging (JSON format)
    /// </summary>
    public bool EnableStructuredLogging { get; set; } = true;
}
