using System.Xml.Serialization;
using Microsoft.Extensions.Logging;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// Service for handling encrypted XML request/response processing
/// </summary>
public class EncryptedXmlService : IEncryptedXmlService
{
    private readonly IRSAService _rsaService;
    private readonly ILogger<EncryptedXmlService> _logger;

    public EncryptedXmlService(IRSAService rsaService, ILogger<EncryptedXmlService> logger)
    {
        _rsaService = rsaService;
        _logger = logger;
    }

    public async Task<T?> DecryptAndDeserializeAsync<T>(string encryptedXmlPayload) where T : class
    {
        try
        {
            // Step 1: Decrypt the base64 encrypted XML payload
            _logger.LogDebug("Decrypting XML payload for type {Type}", typeof(T).Name);
            var decryptedXml = _rsaService.Decrypt(encryptedXmlPayload);
            
            // Step 2: Deserialize XML to object
            var result = DeserializeXml<T>(decryptedXml);
            if (result == null)
            {
                _logger.LogWarning("Failed to deserialize decrypted XML for type {Type}", typeof(T).Name);
                return null;
            }

            _logger.LogDebug("Successfully decrypted and deserialized XML for type {Type}", typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt and deserialize XML for type {Type}", typeof(T).Name);
            return null;
        }
    }

    public async Task<string> SerializeAndEncryptAsync<T>(T obj) where T : class
    {
        try
        {
            // Step 1: Serialize object to XML
            var xml = SerializeToXml(obj);
            if (string.IsNullOrEmpty(xml))
            {
                _logger.LogWarning("Failed to serialize object of type {Type} to XML", typeof(T).Name);
                return string.Empty;
            }
            
            // Step 2: Encrypt the XML
            var encryptedXml = _rsaService.Encrypt(xml);
            
            _logger.LogDebug("Successfully serialized and encrypted object of type {Type}", typeof(T).Name);
            return encryptedXml;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to serialize and encrypt object of type {Type}", typeof(T).Name);
            return string.Empty;
        }
    }

    public async Task<string> CreateEncryptedErrorResponseAsync<T>(T errorResponse) where T : class
    {
        try
        {
            return await SerializeAndEncryptAsync(errorResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create encrypted error response of type {Type}", typeof(T).Name);
            return string.Empty;
        }
    }

    /// <summary>
    /// Helper method to deserialize XML string to object
    /// </summary>
    private static T? DeserializeXml<T>(string xml) where T : class
    {
        try
        {
            var serializer = new XmlSerializer(typeof(T));
            using var reader = new StringReader(xml);
            return serializer.Deserialize(reader) as T;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Helper method to serialize object to XML string
    /// </summary>
    private static string SerializeToXml<T>(T obj) where T : class
    {
        try
        {
            var serializer = new XmlSerializer(typeof(T));
            using var writer = new StringWriter();
            serializer.Serialize(writer, obj);
            return writer.ToString();
        }
        catch
        {
            return string.Empty;
        }
    }
}
