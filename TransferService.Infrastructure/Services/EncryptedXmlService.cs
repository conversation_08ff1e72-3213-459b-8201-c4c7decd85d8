using System.Xml.Serialization;
using Microsoft.Extensions.Logging;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// Service for handling encrypted XML request/response processing
/// </summary>
public class EncryptedXmlService(IPGPService pgpService, ILogger<EncryptedXmlService> logger)
    : IEncryptedXmlService
{
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
    public async Task<T?> DecryptAndDeserializeAsync<T>(string encryptedXmlPayload) where T : class
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
    {
        try
        {
            logger.LogDebug("Decrypting XML payload for type {Type}", typeof(T).Name);
            var decryptedXml = pgpService.Decrypt(encryptedXmlPayload);
            
            var result = DeserializeXml<T>(decryptedXml);
            if (result == null)
            {
                logger.LogWarning("Failed to deserialize decrypted XML for type {Type}", typeof(T).Name);
                return null;
            }

            logger.LogDebug("Successfully decrypted and deserialized XML for type {Type}", typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to decrypt and deserialize XML for type {Type}", typeof(T).Name);
            return null;
        }
    }

    public Task<string> SerializeAndEncryptAsync<T>(T obj) where T : class
    {
        try
        {
            var xml = SerializeToXml(obj);
            if (string.IsNullOrEmpty(xml))
            {
                logger.LogWarning("Failed to serialize object of type {Type} to XML", typeof(T).Name);
                return Task.FromResult(string.Empty);
            }
            
            var encryptedXml = pgpService.Encrypt(xml);
            
            logger.LogDebug("Successfully serialized and encrypted object of type {Type}", typeof(T).Name);
            return Task.FromResult(encryptedXml);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to serialize and encrypt object of type {Type}", typeof(T).Name);
            return Task.FromResult(string.Empty);
        }
    }

    public async Task<string> CreateEncryptedErrorResponseAsync<T>(T errorResponse) where T : class
    {
        try
        {
            return await SerializeAndEncryptAsync(errorResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create encrypted error response of type {Type}", typeof(T).Name);
            return string.Empty;
        }
    }

    /// <summary>
    /// Helper method to deserialize XML string to object
    /// </summary>
    private static T? DeserializeXml<T>(string xml) where T : class
    {
        try
        {
            var serializer = new XmlSerializer(typeof(T));
            using var reader = new StringReader(xml);
            return serializer.Deserialize(reader) as T;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Helper method to serialize object to XML string
    /// </summary>
    private static string SerializeToXml<T>(T obj) where T : class
    {
        try
        {
            var serializer = new XmlSerializer(typeof(T));
            using var writer = new StringWriter();
            serializer.Serialize(writer, obj);
            return writer.ToString();
        }
        catch
        {
            return string.Empty;
        }
    }
}
