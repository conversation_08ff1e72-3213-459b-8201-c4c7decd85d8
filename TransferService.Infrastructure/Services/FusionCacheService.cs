using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TransferService.Application.Interfaces;
using ZiggyCreatures.Caching.Fusion;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// FusionCache implementation of the centralized cache service
/// </summary>
public class FusionCacheService : ICacheService
{
    private readonly IFusionCache _fusionCache;
    private readonly ILogger<FusionCacheService> _logger;

    public FusionCacheService(IFusionCache fusionCache, ILogger<FusionCacheService> logger)
    {
        _fusionCache = fusionCache ?? throw new ArgumentNullException(nameof(fusionCache));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _fusionCache.TryGetAsync<T>(key, token: cancellationToken);
            return result.HasValue ? result.Value : default;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache value for key: {Key}", key);
            return default;
        }
    }

    public async Task<CacheResult<T>> TryGetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _fusionCache.TryGetAsync<T>(key, token: cancellationToken);
            
            if (result.HasValue)
            {
                return CacheResult<T>.Hit(result.Value, false, DateTime.UtcNow);
            }
            
            return CacheResult<T>.Miss();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error trying to get cache value for key: {Key}", key);
            return CacheResult<T>.Miss();
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan duration, CancellationToken cancellationToken = default)
    {
        try
        {
            await _fusionCache.SetAsync(key, value, duration, token: cancellationToken);
            _logger.LogDebug("Set cache value for key: {Key} with duration: {Duration}", key, duration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache value for key: {Key}", key);
            throw;
        }
    }

    public async Task SetAsync<T>(string key, T value, CacheEntryOptions options, CancellationToken cancellationToken = default)
    {
        try
        {
            var fusionOptions = ConvertToFusionCacheOptions(options);
            await _fusionCache.SetAsync(key, value, fusionOptions, token: cancellationToken);
            _logger.LogDebug("Set cache value for key: {Key} with advanced options", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache value with options for key: {Key}", key);
            throw;
        }
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan duration, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _fusionCache.GetOrSetAsync(key, async _ => await factory(), duration, token: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetOrSet for key: {Key}", key);
            throw;
        }
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, CacheEntryOptions options, CancellationToken cancellationToken = default)
    {
        try
        {
            var fusionOptions = ConvertToFusionCacheOptions(options);
            return await _fusionCache.GetOrSetAsync(key, async _ => await factory(), fusionOptions, token: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetOrSet with options for key: {Key}", key);
            throw;
        }
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            await _fusionCache.RemoveAsync(key, token: cancellationToken);
            _logger.LogDebug("Removed cache value for key: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache value for key: {Key}", key);
            throw;
        }
    }

    public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        try
        {
            // FusionCache doesn't have built-in pattern removal, so we'll implement a basic version
            // In a real implementation, you might want to use Redis SCAN or maintain a key registry
            _logger.LogWarning("RemoveByPattern is not fully implemented for pattern: {Pattern}. Consider using tags instead.", pattern);
            
            // For now, we'll just log the request
            // TODO: Implement pattern-based removal if needed
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache values by pattern: {Pattern}", pattern);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _fusionCache.TryGetAsync<object>(key, token: cancellationToken);
            return result.HasValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache existence for key: {Key}", key);
            return false;
        }
    }

    public async Task RefreshAsync<T>(string key, Func<Task<T>> factory, CancellationToken cancellationToken = default)
    {
        try
        {
            // Remove the existing entry and set a new one
            await _fusionCache.RemoveAsync(key, token: cancellationToken);
            var newValue = await factory();
            await _fusionCache.SetAsync(key, newValue, token: cancellationToken);
            _logger.LogDebug("Refreshed cache value for key: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing cache value for key: {Key}", key);
            throw;
        }
    }

    /// <summary>
    /// Converts application cache options to FusionCache options
    /// </summary>
    private static FusionCacheEntryOptions ConvertToFusionCacheOptions(CacheEntryOptions options)
    {
        var fusionOptions = new FusionCacheEntryOptions(options.Duration)
        {
            Priority = ConvertPriority(options.Priority),
            Size = options.Size,
            IsFailSafeEnabled = options.EnableFailSafe,
            FailSafeMaxDuration = options.FailSafeMaxDuration,
            EagerRefreshThreshold = options.EnableEagerRefresh ? options.EagerRefreshThreshold : 0f,
            AllowBackgroundDistributedCacheOperations = true
        };

        // Add tags if supported (FusionCache supports tagging)
        if (options.Tags?.Length > 0)
        {
            // Note: FusionCache tagging might require additional configuration
            // This is a placeholder for tag implementation
        }

        return fusionOptions;
    }

    /// <summary>
    /// Converts application cache priority to FusionCache priority
    /// </summary>
    private static CacheItemPriority ConvertPriority(CachePriority priority)
    {
        return priority switch
        {
            CachePriority.Low => CacheItemPriority.Low,
            CachePriority.Normal => CacheItemPriority.Normal,
            CachePriority.High => CacheItemPriority.High,
            CachePriority.Critical => CacheItemPriority.NeverRemove,
            _ => CacheItemPriority.Normal
        };
    }
}
