using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Bcpg;
using Org.BouncyCastle.Bcpg.OpenPgp;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Math;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// PGP encryption service using BouncyCastle
/// Uses proper PGP message format with automatic hybrid encryption
/// </summary>
public class PGPService : IRSAService
{
    private readonly ILogger<PGPService> _logger;
    private readonly PgpPublicKey _publicKey;
    private readonly PgpPrivateKey _privateKey;

    public PGPService(ILogger<PGPService> logger, IConfiguration configuration)
    {
        _logger = logger;

        try
        {
            // Generate PGP keys directly
            _logger.LogInformation("Generating PGP keys...");

            // Generate RSA key pair first
            var rsaGenerator = new RsaKeyPairGenerator();
            rsaGenerator.Init(new RsaKeyGenerationParameters(
                BigInteger.ValueOf(65537), // e
                new SecureRandom(),
                4096, // 4096-bit keys
                25));

            var rsaKeyPair = rsaGenerator.GenerateKeyPair();

            // Create PGP key pair from RSA keys
            var pgpKeyPair = new PgpKeyPair(
                PublicKeyAlgorithmTag.RsaGeneral,
                rsaKeyPair,
                DateTime.UtcNow);

            _publicKey = pgpKeyPair.PublicKey;
            _privateKey = pgpKeyPair.PrivateKey;

            _logger.LogInformation("PGP keys generated successfully. Key ID: {KeyId}",
                _publicKey.KeyId.ToString("X"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate PGP keys");
            throw;
        }
    }

    public string Encrypt(string plainText)
    {
        try
        {
            _logger.LogDebug("Encrypting data with PGP. Length: {Length} characters", plainText.Length);

            var plainTextBytes = Encoding.UTF8.GetBytes(plainText);

            using var outputStream = new MemoryStream();

            // Create PGP encrypted data generator
            var encryptedDataGenerator = new PgpEncryptedDataGenerator(
                SymmetricKeyAlgorithmTag.Aes256,
                true,
                new SecureRandom());

            // Add recipient public key
            encryptedDataGenerator.AddMethod(_publicKey);

            using var encryptedStream = encryptedDataGenerator.Open(outputStream, plainTextBytes.Length);

            // Create compressed data generator
            var compressedDataGenerator = new PgpCompressedDataGenerator(CompressionAlgorithmTag.Zip);
            using var compressedStream = compressedDataGenerator.Open(encryptedStream);

            // Create literal data generator
            var literalDataGenerator = new PgpLiteralDataGenerator();
            using var literalStream = literalDataGenerator.Open(
                compressedStream,
                PgpLiteralData.Binary,
                "data",
                plainTextBytes.Length,
                DateTime.UtcNow);

            literalStream.Write(plainTextBytes, 0, plainTextBytes.Length);

            var encryptedData = Convert.ToBase64String(outputStream.ToArray());
            _logger.LogDebug("PGP encryption completed. Encrypted length: {Length} characters", encryptedData.Length);

            return encryptedData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt data with PGP");
            throw;
        }
    }



    public string Decrypt(string encryptedData)
    {
        try
        {
            _logger.LogDebug("Decrypting PGP data. Length: {Length} characters", encryptedData.Length);

            var encryptedBytes = Convert.FromBase64String(encryptedData);

            using var inputStream = new MemoryStream(encryptedBytes);
            var pgpFactory = new PgpObjectFactory(inputStream);
            var encryptedDataList = (PgpEncryptedDataList)pgpFactory.NextPgpObject();

            PgpPublicKeyEncryptedData? encryptedDataObj = null;
            foreach (PgpPublicKeyEncryptedData pked in encryptedDataList.GetEncryptedDataObjects())
            {
                if (_privateKey.KeyId == pked.KeyId)
                {
                    encryptedDataObj = pked;
                    break;
                }
            }

            if (encryptedDataObj == null)
                throw new InvalidOperationException("No matching private key found for PGP decryption");

            using var decryptedStream = encryptedDataObj.GetDataStream(_privateKey);
            var decryptedFactory = new PgpObjectFactory(decryptedStream);

            var compressedData = (PgpCompressedData)decryptedFactory.NextPgpObject();
            using var compressedStream = compressedData.GetDataStream();

            var literalFactory = new PgpObjectFactory(compressedStream);
            var literalData = (PgpLiteralData)literalFactory.NextPgpObject();

            using var literalStream = literalData.GetInputStream();
            using var outputStream = new MemoryStream();
            literalStream.CopyTo(outputStream);

            var decryptedText = Encoding.UTF8.GetString(outputStream.ToArray());
            _logger.LogDebug("PGP decryption completed. Decrypted length: {Length} characters", decryptedText.Length);

            return decryptedText;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt PGP data");
            throw;
        }
    }



    public string Sign(string data)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);

            using var outputStream = new MemoryStream();

            var signatureGenerator = new PgpSignatureGenerator(
                _publicKey.Algorithm,
                HashAlgorithmTag.Sha256);

            signatureGenerator.InitSign(PgpSignature.BinaryDocument, _privateKey);
            signatureGenerator.Update(dataBytes, 0, dataBytes.Length);

            var signature = signatureGenerator.Generate();
            signature.Encode(outputStream);

            return Convert.ToBase64String(outputStream.ToArray());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sign data with PGP");
            throw;
        }
    }

    public bool VerifySignature(string data, string signature)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signatureBytes = Convert.FromBase64String(signature);

            using var signatureStream = new MemoryStream(signatureBytes);
            var pgpFactory = new PgpObjectFactory(signatureStream);
            var signatureList = (PgpSignatureList)pgpFactory.NextPgpObject();
            var pgpSignature = signatureList[0];

            pgpSignature.InitVerify(_publicKey);
            pgpSignature.Update(dataBytes, 0, dataBytes.Length);

            return pgpSignature.Verify();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify PGP signature");
            return false;
        }
    }
}
