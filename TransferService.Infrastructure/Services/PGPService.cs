using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PgpCore;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// PGP encryption service using PgpCore library
/// Follows standard PGP practices with .asc key files
/// </summary>
public class PGPService : IPGPService
{
    private readonly ILogger<PGPService> _logger;
    private readonly EncryptionKeys _encryptionKeys;

    public PGPService(ILogger<PGPService> logger, IConfiguration configuration)
    {
        _logger = logger;

        try
        {
            // Load PGP keys from .asc files (standard PGP format)
            var publicKeyPath = configuration["PGP:PublicKeyPath"] ?? "../keys/public.asc";
            var privateKeyPath = configuration["PGP:PrivateKeyPath"] ?? "../keys/private.asc";
            var passphrase = configuration["PGP:Passphrase"] ?? "";

            _logger.LogInformation("Loading PGP keys from .asc files using PgpCore...");

            // Load keys using PgpCore EncryptionKeys
            var publicKeyInfo = new FileInfo(publicKeyPath);
            var privateKeyInfo = new FileInfo(privateKeyPath);

            _encryptionKeys = new EncryptionKeys(publicKeyInfo, privateKeyInfo, passphrase);

            _logger.LogInformation("PGP keys loaded successfully using PgpCore");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load PGP keys from .asc files");
            throw;
        }
    }

    public string Encrypt(string plainText)
    {
        try
        {
            _logger.LogDebug("Encrypting data with PGP using PgpCore. Length: {Length} characters", plainText.Length);

            // Use PgpCore to encrypt the string
            using var pgp = new PGP(_encryptionKeys);
            var encryptedData = pgp.EncryptAsync(plainText).Result;

            _logger.LogDebug("PGP encryption completed using PgpCore. Encrypted length: {Length} characters", encryptedData.Length);

            return encryptedData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt data with PGP using PgpCore");
            throw;
        }
    }



    public string Decrypt(string encryptedData)
    {
        try
        {
            _logger.LogDebug("Decrypting PGP data using PgpCore. Length: {Length} characters", encryptedData.Length);

            // Use PgpCore to decrypt the string
            using var pgp = new PGP(_encryptionKeys);
            var decryptedData = pgp.DecryptAsync(encryptedData).Result;

            _logger.LogDebug("PGP decryption completed using PgpCore. Decrypted length: {Length} characters", decryptedData.Length);

            return decryptedData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt PGP data using PgpCore");
            throw;
        }
    }



    public string Sign(string data)
    {
        try
        {
            _logger.LogDebug("Signing data with PGP using PgpCore. Length: {Length} characters", data.Length);

            // Use PgpCore to sign the string
            using var pgp = new PGP(_encryptionKeys);
            var signature = pgp.SignAsync(data).Result;

            _logger.LogDebug("PGP signing completed using PgpCore");

            return signature;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sign data with PGP using PgpCore");
            throw;
        }
    }

    public bool VerifySignature(string data, string signature)
    {
        try
        {
            _logger.LogDebug("Verifying PGP signature using PgpCore");

            // Use PgpCore to verify the signature
            using var pgp = new PGP(_encryptionKeys);
            var isValid = pgp.VerifyAsync(signature).Result;

            _logger.LogDebug("PGP signature verification completed using PgpCore. Valid: {IsValid}", isValid);

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify PGP signature using PgpCore");
            return false;
        }
    }
}
