using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Bcpg;
using Org.BouncyCastle.Bcpg.OpenPgp;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Security;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// PGP encryption service implementation using BouncyCastle
/// Supports large payloads with hybrid encryption (RSA + AES)
/// </summary>
public class PGPService : IRSAService
{
    private readonly ILogger<PGPService> _logger;
    private readonly PgpPublicKey _recipientPublicKey;
    private readonly PgpPrivateKey _senderPrivateKey;
    private readonly PgpPublicKey _senderPublicKey;

    public PGPService(ILogger<PGPService> logger, IConfiguration configuration)
    {
        _logger = logger;

        try
        {
            // Load PGP keys
            var publicKeyPath = configuration["PGP:PublicKeyPath"] ?? "Keys/public.asc";
            var privateKeyPath = configuration["PGP:PrivateKeyPath"] ?? "Keys/private.asc";
            var passphrase = configuration["PGP:Passphrase"] ?? "";

            // Load public key for encryption
            _recipientPublicKey = LoadPublicKey(publicKeyPath);
            
            // Load private key for decryption
            var privateKeyRing = LoadPrivateKey(privateKeyPath);
            _senderPrivateKey = ExtractPrivateKey(privateKeyRing, passphrase);
            _senderPublicKey = privateKeyRing.GetPublicKey();

            _logger.LogInformation("PGP keys loaded successfully. Public key ID: {PublicKeyId}, Private key ID: {PrivateKeyId}", 
                _recipientPublicKey.KeyId.ToString("X"), _senderPrivateKey.KeyId.ToString("X"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load PGP keys");
            throw;
        }
    }

    public string Encrypt(string plainText)
    {
        try
        {
            _logger.LogDebug("Encrypting data with PGP. Length: {Length} characters", plainText.Length);
            
            var plainTextBytes = Encoding.UTF8.GetBytes(plainText);
            
            using var outputStream = new MemoryStream();
            using var armoredStream = new ArmoredOutputStream(outputStream);
            
            // Create encrypted data generator
            var encryptedDataGenerator = new PgpEncryptedDataGenerator(
                SymmetricKeyAlgorithmTag.Aes256, 
                true, 
                new SecureRandom());
            
            encryptedDataGenerator.AddMethod(_recipientPublicKey);
            
            using var encryptedStream = encryptedDataGenerator.Open(armoredStream, plainTextBytes.Length);
            
            // Create compressed data generator
            var compressedDataGenerator = new PgpCompressedDataGenerator(CompressionAlgorithmTag.Zip);
            using var compressedStream = compressedDataGenerator.Open(encryptedStream);
            
            // Create literal data generator
            var literalDataGenerator = new PgpLiteralDataGenerator();
            using var literalStream = literalDataGenerator.Open(
                compressedStream, 
                PgpLiteralData.Binary, 
                "data", 
                plainTextBytes.Length, 
                DateTime.UtcNow);
            
            literalStream.Write(plainTextBytes, 0, plainTextBytes.Length);
            literalStream.Close();
            compressedStream.Close();
            encryptedStream.Close();
            armoredStream.Close();
            
            var encryptedData = Convert.ToBase64String(outputStream.ToArray());
            _logger.LogDebug("PGP encryption completed. Encrypted length: {Length} characters", encryptedData.Length);
            
            return encryptedData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt data with PGP");
            throw;
        }
    }

    public string Decrypt(string encryptedData)
    {
        try
        {
            _logger.LogDebug("Decrypting PGP data. Length: {Length} characters", encryptedData.Length);
            
            var encryptedBytes = Convert.FromBase64String(encryptedData);
            
            using var inputStream = new MemoryStream(encryptedBytes);
            using var armoredStream = new ArmoredInputStream(inputStream);
            
            var pgpFactory = new PgpObjectFactory(armoredStream);
            var encryptedDataList = (PgpEncryptedDataList)pgpFactory.NextPgpObject();
            
            PgpPublicKeyEncryptedData? encryptedData_obj = null;
            foreach (PgpPublicKeyEncryptedData pked in encryptedDataList.GetEncryptedDataObjects())
            {
                if (_senderPrivateKey.KeyId == pked.KeyId)
                {
                    encryptedData_obj = pked;
                    break;
                }
            }
            
            if (encryptedData_obj == null)
                throw new InvalidOperationException("No matching private key found for decryption");
            
            using var decryptedStream = encryptedData_obj.GetDataStream(_senderPrivateKey);
            var decryptedFactory = new PgpObjectFactory(decryptedStream);
            
            var compressedData = (PgpCompressedData)decryptedFactory.NextPgpObject();
            using var compressedStream = compressedData.GetDataStream();
            
            var literalFactory = new PgpObjectFactory(compressedStream);
            var literalData = (PgpLiteralData)literalFactory.NextPgpObject();
            
            using var literalStream = literalData.GetInputStream();
            using var outputStream = new MemoryStream();
            literalStream.CopyTo(outputStream);
            
            var decryptedText = Encoding.UTF8.GetString(outputStream.ToArray());
            _logger.LogDebug("PGP decryption completed. Decrypted length: {Length} characters", decryptedText.Length);
            
            return decryptedText;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt PGP data");
            throw;
        }
    }

    public string Sign(string data)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);
            
            using var outputStream = new MemoryStream();
            using var armoredStream = new ArmoredOutputStream(outputStream);
            
            var signatureGenerator = new PgpSignatureGenerator(
                _senderPublicKey.Algorithm, 
                HashAlgorithmTag.Sha256);
            
            signatureGenerator.InitSign(PgpSignature.BinaryDocument, _senderPrivateKey);
            signatureGenerator.Update(dataBytes, 0, dataBytes.Length);
            
            var signature = signatureGenerator.Generate();
            signature.Encode(armoredStream);
            armoredStream.Close();
            
            return Convert.ToBase64String(outputStream.ToArray());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sign data with PGP");
            throw;
        }
    }

    public bool VerifySignature(string data, string signature)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signatureBytes = Convert.FromBase64String(signature);
            
            using var signatureStream = new MemoryStream(signatureBytes);
            using var armoredStream = new ArmoredInputStream(signatureStream);
            
            var pgpFactory = new PgpObjectFactory(armoredStream);
            var signatureList = (PgpSignatureList)pgpFactory.NextPgpObject();
            var pgpSignature = signatureList[0];
            
            pgpSignature.InitVerify(_recipientPublicKey);
            pgpSignature.Update(dataBytes, 0, dataBytes.Length);
            
            return pgpSignature.Verify();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify PGP signature");
            return false;
        }
    }

    private PgpPublicKey LoadPublicKey(string publicKeyPath)
    {
        using var keyStream = File.OpenRead(publicKeyPath);
        using var armoredStream = new ArmoredInputStream(keyStream);
        
        var pgpFactory = new PgpObjectFactory(armoredStream);
        var publicKeyRing = (PgpPublicKeyRing)pgpFactory.NextPgpObject();
        
        return publicKeyRing.GetPublicKey();
    }

    private PgpSecretKeyRing LoadPrivateKey(string privateKeyPath)
    {
        using var keyStream = File.OpenRead(privateKeyPath);
        using var armoredStream = new ArmoredInputStream(keyStream);
        
        var pgpFactory = new PgpObjectFactory(armoredStream);
        return (PgpSecretKeyRing)pgpFactory.NextPgpObject();
    }

    private PgpPrivateKey ExtractPrivateKey(PgpSecretKeyRing secretKeyRing, string passphrase)
    {
        var secretKey = secretKeyRing.GetSecretKey();
        return secretKey.ExtractPrivateKey(passphrase.ToCharArray());
    }
}
