using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Asn1.Pkcs;
using Org.BouncyCastle.Bcpg;
using Org.BouncyCastle.Bcpg.OpenPgp;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Math;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Pkcs;
using Org.BouncyCastle.Security;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// Proper PGP encryption service using BouncyCastle
/// Uses standard PGP key rings and proper PGP message format
/// </summary>
public class PGPService : IRSAService
{
    private readonly ILogger<PGPService> _logger;
    private readonly PgpPublicKey _encryptionKey;
    private readonly PgpPrivateKey _decryptionKey;

    public PGPService(ILogger<PGPService> logger, IConfiguration configuration)
    {
        _logger = logger;

        try
        {
            // Load keys from .key files (PEM format) and convert to PGP
            var publicKeyPath = configuration["PGP:PublicKeyPath"] ?? "Keys/public.key";
            var privateKeyPath = configuration["PGP:PrivateKeyPath"] ?? "Keys/private.key";
            var thirdPartyPublicKeyPath = configuration["PGP:ThirdPartyPublicKeyPath"] ?? "Keys/third-party-public.key";

            _logger.LogInformation("Loading PEM keys from .key files and converting to PGP format...");

            // Load RSA keys from PEM files
            var ourPrivateKey = LoadRsaPrivateKey(privateKeyPath);
            var thirdPartyPublicKey = LoadRsaPublicKey(thirdPartyPublicKeyPath);

            // Convert to PGP keys
            _decryptionKey = CreatePgpPrivateKey(ourPrivateKey);
            _encryptionKey = CreatePgpPublicKey(thirdPartyPublicKey);

            _logger.LogInformation("PGP keys created from PEM successfully. Encryption Key ID: {EncryptionKeyId}, Decryption Key ID: {DecryptionKeyId}",
                _encryptionKey.KeyId.ToString("X"), _decryptionKey.KeyId.ToString("X"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load PGP keys from .asc files");
            throw;
        }
    }

    public string Encrypt(string plainText)
    {
        try
        {
            _logger.LogDebug("Encrypting data with PGP. Length: {Length} characters", plainText.Length);

            var plainTextBytes = Encoding.UTF8.GetBytes(plainText);

            using var outputStream = new MemoryStream();

            // Compress the data first
            using var compressedStream = new MemoryStream();
            var compressedDataGenerator = new PgpCompressedDataGenerator(CompressionAlgorithmTag.Zip);
            using (var compressedOut = compressedDataGenerator.Open(compressedStream))
            {
                var literalDataGenerator = new PgpLiteralDataGenerator();
                using var literalOut = literalDataGenerator.Open(
                    compressedOut,
                    PgpLiteralData.Binary,
                    "data",
                    plainTextBytes.Length,
                    DateTime.UtcNow);

                literalOut.Write(plainTextBytes, 0, plainTextBytes.Length);
            }

            var compressedBytes = compressedStream.ToArray();

            // Create PGP encrypted data generator
            var encryptedDataGenerator = new PgpEncryptedDataGenerator(
                SymmetricKeyAlgorithmTag.Aes256,
                true,
                new SecureRandom());

            // Add recipient public key
            encryptedDataGenerator.AddMethod(_encryptionKey);

            using var encryptedStream = encryptedDataGenerator.Open(outputStream, compressedBytes.Length);
            encryptedStream.Write(compressedBytes, 0, compressedBytes.Length);

            var encryptedData = Convert.ToBase64String(outputStream.ToArray());
            _logger.LogDebug("PGP encryption completed. Encrypted length: {Length} characters", encryptedData.Length);

            return encryptedData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt data with PGP");
            throw;
        }
    }



    public string Decrypt(string encryptedData)
    {
        try
        {
            _logger.LogDebug("Decrypting PGP data. Length: {Length} characters", encryptedData.Length);

            var encryptedBytes = Convert.FromBase64String(encryptedData);

            using var inputStream = new MemoryStream(encryptedBytes);
            var pgpFactory = new PgpObjectFactory(inputStream);
            var encryptedDataList = (PgpEncryptedDataList)pgpFactory.NextPgpObject();

            PgpPublicKeyEncryptedData? encryptedDataObj = null;
            foreach (PgpPublicKeyEncryptedData pked in encryptedDataList.GetEncryptedDataObjects())
            {
                if (_decryptionKey.KeyId == pked.KeyId)
                {
                    encryptedDataObj = pked;
                    break;
                }
            }

            if (encryptedDataObj == null)
                throw new InvalidOperationException("No matching private key found for PGP decryption");

            using var decryptedStream = encryptedDataObj.GetDataStream(_decryptionKey);
            var decryptedFactory = new PgpObjectFactory(decryptedStream);

            var compressedData = (PgpCompressedData)decryptedFactory.NextPgpObject();
            using var compressedStream = compressedData.GetDataStream();

            var literalFactory = new PgpObjectFactory(compressedStream);
            var literalData = (PgpLiteralData)literalFactory.NextPgpObject();

            using var literalStream = literalData.GetInputStream();
            using var outputStream = new MemoryStream();
            literalStream.CopyTo(outputStream);

            var decryptedText = Encoding.UTF8.GetString(outputStream.ToArray());
            _logger.LogDebug("PGP decryption completed. Decrypted length: {Length} characters", decryptedText.Length);

            return decryptedText;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt PGP data");
            throw;
        }
    }



    public string Sign(string data)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);

            using var outputStream = new MemoryStream();

            var signatureGenerator = new PgpSignatureGenerator(
                _encryptionKey.Algorithm,
                HashAlgorithmTag.Sha256);

            signatureGenerator.InitSign(PgpSignature.BinaryDocument, _decryptionKey);
            signatureGenerator.Update(dataBytes, 0, dataBytes.Length);

            var signature = signatureGenerator.Generate();
            signature.Encode(outputStream);

            return Convert.ToBase64String(outputStream.ToArray());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sign data with PGP");
            throw;
        }
    }

    public bool VerifySignature(string data, string signature)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signatureBytes = Convert.FromBase64String(signature);

            using var signatureStream = new MemoryStream(signatureBytes);
            var pgpFactory = new PgpObjectFactory(signatureStream);
            var signatureList = (PgpSignatureList)pgpFactory.NextPgpObject();
            var pgpSignature = signatureList[0];

            pgpSignature.InitVerify(_encryptionKey);
            pgpSignature.Update(dataBytes, 0, dataBytes.Length);

            return pgpSignature.Verify();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify PGP signature");
            return false;
        }
    }

    private RsaKeyParameters LoadRsaPublicKey(string publicKeyPath)
    {
        var keyContent = File.ReadAllText(publicKeyPath);
        using var reader = new StringReader(keyContent);
        var pemReader = new PemReader(reader);
        var keyObject = pemReader.ReadObject();

        if (keyObject is RsaKeyParameters rsaKey)
        {
            return rsaKey;
        }
        else if (keyObject is AsymmetricCipherKeyPair keyPair)
        {
            return (RsaKeyParameters)keyPair.Public;
        }

        throw new InvalidOperationException("Invalid RSA public key format in .key file");
    }

    private RsaPrivateCrtKeyParameters LoadRsaPrivateKey(string privateKeyPath)
    {
        var keyContent = File.ReadAllText(privateKeyPath);
        _logger.LogDebug("Key content preview: {Preview}", keyContent.Substring(0, Math.Min(100, keyContent.Length)));

        using var reader = new StringReader(keyContent);
        var pemReader = new PemReader(reader);
        var keyObject = pemReader.ReadObject();

        _logger.LogDebug("PEM object type: {Type}", keyObject?.GetType().Name ?? "null");

        if (keyObject is RsaPrivateCrtKeyParameters rsaPrivateKey)
        {
            return rsaPrivateKey;
        }
        else if (keyObject is AsymmetricCipherKeyPair keyPair)
        {
            return (RsaPrivateCrtKeyParameters)keyPair.Private;
        }
        else if (keyObject is Pkcs8EncryptedPrivateKeyInfo encryptedInfo)
        {
            // Handle PKCS#8 encrypted private key
            var decryptedInfo = encryptedInfo.DecryptPrivateKeyInfo(null); // No password
            return (RsaPrivateCrtKeyParameters)PrivateKeyFactory.CreateKey(decryptedInfo);
        }
        else if (keyObject is PrivateKeyInfo privateKeyInfo)
        {
            // Handle PKCS#8 unencrypted private key
            return (RsaPrivateCrtKeyParameters)PrivateKeyFactory.CreateKey(privateKeyInfo);
        }

        throw new InvalidOperationException($"Invalid RSA private key format in .key file. Got type: {keyObject?.GetType().Name ?? "null"}");
    }

    private PgpPublicKey CreatePgpPublicKey(RsaKeyParameters rsaPublicKey)
    {
        // Create a PGP key pair from RSA public key
        var keyPair = new PgpKeyPair(
            PublicKeyAlgorithmTag.RsaGeneral,
            new AsymmetricCipherKeyPair(rsaPublicKey, rsaPublicKey), // Use same key for both (we only need public)
            DateTime.UtcNow);

        return keyPair.PublicKey;
    }

    private PgpPrivateKey CreatePgpPrivateKey(RsaPrivateCrtKeyParameters rsaPrivateKey)
    {
        // Extract public key from private key
        var rsaPublicKey = new RsaKeyParameters(false, rsaPrivateKey.Modulus, rsaPrivateKey.PublicExponent);

        // Create a PGP key pair from RSA keys
        var keyPair = new PgpKeyPair(
            PublicKeyAlgorithmTag.RsaGeneral,
            new AsymmetricCipherKeyPair(rsaPublicKey, rsaPrivateKey),
            DateTime.UtcNow);

        return keyPair.PrivateKey;
    }
}
