using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Bcpg;
using Org.BouncyCastle.Bcpg.OpenPgp;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// PGP-style encryption service using <PERSON>uncyCast<PERSON> with RSA PEM keys
/// Supports large payloads with hybrid encryption (RSA + AES)
/// </summary>
public class PGPService : IRSAService
{
    private readonly ILogger<PGPService> _logger;
    private readonly RsaKeyParameters _publicKey;
    private readonly RsaPrivateCrtKeyParameters _privateKey;

    public PGPService(ILogger<PGPService> logger, IConfiguration configuration)
    {
        _logger = logger;

        try
        {
            // Load RSA keys from PEM files
            var publicKeyPath = configuration["PGP:PublicKeyPath"] ?? "Keys/public.key";
            var privateKeyPath = configuration["PGP:PrivateKeyPath"] ?? "Keys/private.key";

            // Load public key for encryption
            _publicKey = LoadRsaPublicKey(publicKeyPath);

            // Load private key for decryption
            _privateKey = LoadRsaPrivateKey(privateKeyPath);

            _logger.LogInformation("PGP-style RSA keys loaded successfully. Key size: {KeySize} bits",
                _publicKey.Modulus.BitLength);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load RSA keys for PGP-style encryption");
            throw;
        }
    }

    public string Encrypt(string plainText)
    {
        try
        {
            _logger.LogDebug("Encrypting data with hybrid encryption. Length: {Length} characters", plainText.Length);

            var plainTextBytes = Encoding.UTF8.GetBytes(plainText);

            // For small data (< 500 bytes), use RSA directly
            if (plainTextBytes.Length < 500)
            {
                return EncryptWithRsa(plainTextBytes);
            }

            // For large data, use hybrid encryption (AES + RSA)
            return EncryptWithHybrid(plainTextBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt data");
            throw;
        }
    }

    private string EncryptWithRsa(byte[] data)
    {
        var cipher = CipherUtilities.GetCipher("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
        cipher.Init(true, _publicKey);
        var encryptedData = cipher.DoFinal(data);

        // Prefix with "RSA:" to indicate encryption method
        var result = "RSA:" + Convert.ToBase64String(encryptedData);
        _logger.LogDebug("RSA encryption completed. Encrypted length: {Length} characters", result.Length);
        return result;
    }

    private string EncryptWithHybrid(byte[] data)
    {
        // Generate random AES key and IV
        var aesKey = new byte[32]; // 256-bit key
        var iv = new byte[12]; // 96-bit IV for GCM
        var random = new SecureRandom();
        random.NextBytes(aesKey);
        random.NextBytes(iv);

        // Encrypt data with AES
        var aesEngine = CipherUtilities.GetCipher("AES/GCM/NoPadding");
        var aesKeyParam = ParameterUtilities.CreateKeyParameter("AES", aesKey);
        var aesParams = new ParametersWithIV(aesKeyParam, iv);
        aesEngine.Init(true, aesParams);
        var encryptedData = aesEngine.DoFinal(data);

        // Encrypt AES key with RSA
        var rsaCipher = CipherUtilities.GetCipher("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
        rsaCipher.Init(true, _publicKey);
        var encryptedAesKey = rsaCipher.DoFinal(aesKey);

        // Combine encrypted AES key + IV + encrypted data
        using var outputStream = new MemoryStream();
        using var writer = new BinaryWriter(outputStream);

        writer.Write(encryptedAesKey.Length);
        writer.Write(encryptedAesKey);
        writer.Write(iv.Length);
        writer.Write(iv);
        writer.Write(encryptedData);

        // Prefix with "HYBRID:" to indicate encryption method
        var result = "HYBRID:" + Convert.ToBase64String(outputStream.ToArray());
        _logger.LogDebug("Hybrid encryption completed. Encrypted length: {Length} characters", result.Length);
        return result;
    }

    public string Decrypt(string encryptedData)
    {
        try
        {
            _logger.LogDebug("Decrypting data. Length: {Length} characters", encryptedData.Length);

            // Determine encryption method by prefix
            if (encryptedData.StartsWith("RSA:"))
            {
                var base64Data = encryptedData.Substring(4);
                return DecryptWithRsa(Convert.FromBase64String(base64Data));
            }
            else if (encryptedData.StartsWith("HYBRID:"))
            {
                var base64Data = encryptedData.Substring(7);
                return DecryptWithHybrid(Convert.FromBase64String(base64Data));
            }
            else
            {
                // Legacy support - assume RSA
                return DecryptWithRsa(Convert.FromBase64String(encryptedData));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt data");
            throw;
        }
    }

    private string DecryptWithRsa(byte[] encryptedData)
    {
        var cipher = CipherUtilities.GetCipher("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
        cipher.Init(false, _privateKey);
        var decryptedData = cipher.DoFinal(encryptedData);

        var result = Encoding.UTF8.GetString(decryptedData);
        _logger.LogDebug("RSA decryption completed. Decrypted length: {Length} characters", result.Length);
        return result;
    }

    private string DecryptWithHybrid(byte[] encryptedData)
    {
        using var inputStream = new MemoryStream(encryptedData);
        using var reader = new BinaryReader(inputStream);

        // Read encrypted AES key
        var aesKeyLength = reader.ReadInt32();
        var encryptedAesKey = reader.ReadBytes(aesKeyLength);

        // Read IV
        var ivLength = reader.ReadInt32();
        var iv = reader.ReadBytes(ivLength);

        // Read encrypted data
        var encryptedPayload = reader.ReadBytes((int)(inputStream.Length - inputStream.Position));

        // Decrypt AES key with RSA
        var rsaCipher = CipherUtilities.GetCipher("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
        rsaCipher.Init(false, _privateKey);
        var aesKey = rsaCipher.DoFinal(encryptedAesKey);

        // Decrypt data with AES
        var aesEngine = CipherUtilities.GetCipher("AES/GCM/NoPadding");
        var aesKeyParam = ParameterUtilities.CreateKeyParameter("AES", aesKey);
        var aesParams = new ParametersWithIV(aesKeyParam, iv);
        aesEngine.Init(false, aesParams);
        var decryptedData = aesEngine.DoFinal(encryptedPayload);

        var result = Encoding.UTF8.GetString(decryptedData);
        _logger.LogDebug("Hybrid decryption completed. Decrypted length: {Length} characters", result.Length);
        return result;
    }

    public string Sign(string data)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);

            // Use RSA-PSS for signing
            var signer = SignerUtilities.GetSigner("SHA256withRSA/PSS");
            signer.Init(true, _privateKey);
            signer.BlockUpdate(dataBytes, 0, dataBytes.Length);
            var signature = signer.GenerateSignature();

            return Convert.ToBase64String(signature);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sign data");
            throw;
        }
    }

    public bool VerifySignature(string data, string signature)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signatureBytes = Convert.FromBase64String(signature);

            var verifier = SignerUtilities.GetSigner("SHA256withRSA/PSS");
            verifier.Init(false, _publicKey);
            verifier.BlockUpdate(dataBytes, 0, dataBytes.Length);

            return verifier.VerifySignature(signatureBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify signature");
            return false;
        }
    }

    private RsaKeyParameters LoadRsaPublicKey(string publicKeyPath)
    {
        var keyContent = File.ReadAllText(publicKeyPath);
        using var reader = new StringReader(keyContent);
        var pemReader = new PemReader(reader);
        var keyObject = pemReader.ReadObject();

        if (keyObject is RsaKeyParameters rsaKey)
        {
            return rsaKey;
        }
        else if (keyObject is AsymmetricCipherKeyPair keyPair)
        {
            return (RsaKeyParameters)keyPair.Public;
        }

        throw new InvalidOperationException("Invalid RSA public key format");
    }

    private RsaPrivateCrtKeyParameters LoadRsaPrivateKey(string privateKeyPath)
    {
        var keyContent = File.ReadAllText(privateKeyPath);
        using var reader = new StringReader(keyContent);
        var pemReader = new PemReader(reader);
        var keyObject = pemReader.ReadObject();

        if (keyObject is RsaPrivateCrtKeyParameters rsaPrivateKey)
        {
            return rsaPrivateKey;
        }
        else if (keyObject is AsymmetricCipherKeyPair keyPair)
        {
            return (RsaPrivateCrtKeyParameters)keyPair.Private;
        }

        throw new InvalidOperationException("Invalid RSA private key format");
    }
}
