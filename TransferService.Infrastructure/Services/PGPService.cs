using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Bcpg;
using Org.BouncyCastle.Bcpg.OpenPgp;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Math;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// Proper PGP encryption service using BouncyCastle
/// Uses standard PGP key rings and proper PGP message format
/// </summary>
public class PGPService : IRSAService
{
    private readonly ILogger<PGPService> _logger;
    private readonly PgpPublicKey _encryptionKey;
    private readonly PgpPrivateKey _decryptionKey;

    public PGPService(ILogger<PGPService> logger, IConfiguration configuration)
    {
        _logger = logger;

        try
        {
            // Load PGP keys from .asc files (proper PGP format)
            var publicKeyPath = configuration["PGP:PublicKeyPath"] ?? "Keys/public.asc";
            var privateKeyPath = configuration["PGP:PrivateKeyPath"] ?? "Keys/private.asc";
            var passphrase = configuration["PGP:Passphrase"] ?? "";

            _logger.LogInformation("Loading PGP keys from .asc files...");

            // Load public key for encryption
            _encryptionKey = LoadPgpPublicKey(publicKeyPath);

            // Load private key for decryption
            _decryptionKey = LoadPgpPrivateKey(privateKeyPath, passphrase.ToCharArray());

            _logger.LogInformation("PGP keys loaded successfully. Encryption Key ID: {EncryptionKeyId}, Decryption Key ID: {DecryptionKeyId}",
                _encryptionKey.KeyId.ToString("X"), _decryptionKey.KeyId.ToString("X"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load PGP keys from .asc files");
            throw;
        }
    }

    public string Encrypt(string plainText)
    {
        try
        {
            _logger.LogDebug("Encrypting data with PGP. Length: {Length} characters", plainText.Length);

            var plainTextBytes = Encoding.UTF8.GetBytes(plainText);

            using var outputStream = new MemoryStream();

            // Compress the data first
            using var compressedStream = new MemoryStream();
            var compressedDataGenerator = new PgpCompressedDataGenerator(CompressionAlgorithmTag.Zip);
            using (var compressedOut = compressedDataGenerator.Open(compressedStream))
            {
                var literalDataGenerator = new PgpLiteralDataGenerator();
                using var literalOut = literalDataGenerator.Open(
                    compressedOut,
                    PgpLiteralData.Binary,
                    "data",
                    plainTextBytes.Length,
                    DateTime.UtcNow);

                literalOut.Write(plainTextBytes, 0, plainTextBytes.Length);
            }

            var compressedBytes = compressedStream.ToArray();

            // Create PGP encrypted data generator
            var encryptedDataGenerator = new PgpEncryptedDataGenerator(
                SymmetricKeyAlgorithmTag.Aes256,
                true,
                new SecureRandom());

            // Add recipient public key
            encryptedDataGenerator.AddMethod(_encryptionKey);

            using var encryptedStream = encryptedDataGenerator.Open(outputStream, compressedBytes.Length);
            encryptedStream.Write(compressedBytes, 0, compressedBytes.Length);

            var encryptedData = Convert.ToBase64String(outputStream.ToArray());
            _logger.LogDebug("PGP encryption completed. Encrypted length: {Length} characters", encryptedData.Length);

            return encryptedData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt data with PGP");
            throw;
        }
    }



    public string Decrypt(string encryptedData)
    {
        try
        {
            _logger.LogDebug("Decrypting PGP data. Length: {Length} characters", encryptedData.Length);

            var encryptedBytes = Convert.FromBase64String(encryptedData);

            using var inputStream = new MemoryStream(encryptedBytes);
            var pgpFactory = new PgpObjectFactory(inputStream);
            var encryptedDataList = (PgpEncryptedDataList)pgpFactory.NextPgpObject();

            PgpPublicKeyEncryptedData? encryptedDataObj = null;
            foreach (PgpPublicKeyEncryptedData pked in encryptedDataList.GetEncryptedDataObjects())
            {
                if (_decryptionKey.KeyId == pked.KeyId)
                {
                    encryptedDataObj = pked;
                    break;
                }
            }

            if (encryptedDataObj == null)
                throw new InvalidOperationException("No matching private key found for PGP decryption");

            using var decryptedStream = encryptedDataObj.GetDataStream(_decryptionKey);
            var decryptedFactory = new PgpObjectFactory(decryptedStream);

            var compressedData = (PgpCompressedData)decryptedFactory.NextPgpObject();
            using var compressedStream = compressedData.GetDataStream();

            var literalFactory = new PgpObjectFactory(compressedStream);
            var literalData = (PgpLiteralData)literalFactory.NextPgpObject();

            using var literalStream = literalData.GetInputStream();
            using var outputStream = new MemoryStream();
            literalStream.CopyTo(outputStream);

            var decryptedText = Encoding.UTF8.GetString(outputStream.ToArray());
            _logger.LogDebug("PGP decryption completed. Decrypted length: {Length} characters", decryptedText.Length);

            return decryptedText;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt PGP data");
            throw;
        }
    }



    public string Sign(string data)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);

            using var outputStream = new MemoryStream();

            var signatureGenerator = new PgpSignatureGenerator(
                _publicKey.Algorithm,
                HashAlgorithmTag.Sha256);

            signatureGenerator.InitSign(PgpSignature.BinaryDocument, _privateKey);
            signatureGenerator.Update(dataBytes, 0, dataBytes.Length);

            var signature = signatureGenerator.Generate();
            signature.Encode(outputStream);

            return Convert.ToBase64String(outputStream.ToArray());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sign data with PGP");
            throw;
        }
    }

    public bool VerifySignature(string data, string signature)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signatureBytes = Convert.FromBase64String(signature);

            using var signatureStream = new MemoryStream(signatureBytes);
            var pgpFactory = new PgpObjectFactory(signatureStream);
            var signatureList = (PgpSignatureList)pgpFactory.NextPgpObject();
            var pgpSignature = signatureList[0];

            pgpSignature.InitVerify(_publicKey);
            pgpSignature.Update(dataBytes, 0, dataBytes.Length);

            return pgpSignature.Verify();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify PGP signature");
            return false;
        }
    }

    private PgpPublicKey LoadPgpPublicKey(string publicKeyPath)
    {
        using var keyStream = File.OpenRead(publicKeyPath);
        using var decoderStream = PgpUtilities.GetDecoderStream(keyStream);

        var pgpPublicKeyRingCollection = new PgpPublicKeyRingCollection(decoderStream);

        // Find the first encryption key
        foreach (PgpPublicKeyRing keyRing in pgpPublicKeyRingCollection.GetKeyRings())
        {
            foreach (PgpPublicKey key in keyRing.GetPublicKeys())
            {
                if (key.IsEncryptionKey)
                {
                    return key;
                }
            }
        }

        throw new InvalidOperationException("No encryption key found in PGP public key file");
    }

    private PgpPrivateKey LoadPgpPrivateKey(string privateKeyPath, char[] passphrase)
    {
        using var keyStream = File.OpenRead(privateKeyPath);
        using var decoderStream = PgpUtilities.GetDecoderStream(keyStream);

        var pgpSecretKeyRingCollection = new PgpSecretKeyRingCollection(decoderStream);

        // Find the first secret key
        foreach (PgpSecretKeyRing keyRing in pgpSecretKeyRingCollection.GetKeyRings())
        {
            foreach (PgpSecretKey secretKey in keyRing.GetSecretKeys())
            {
                if (secretKey.IsSigningKey)
                {
                    return secretKey.ExtractPrivateKey(passphrase);
                }
            }
        }

        throw new InvalidOperationException("No signing key found in PGP private key file");
    }
}
