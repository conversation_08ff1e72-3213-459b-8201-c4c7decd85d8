using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Security.Cryptography;
using TransferService.Application.Common.Models;
using TransferService.Application.Configuration;
using TransferService.Application.Services;
using TransferService.Infrastructure.Services;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// Implementation of RSA key loading service
/// </summary>
public class RSAKeyLoadingService : IRSAKeyLoadingService
{
    private readonly IOptions<RSAOptions> _rsaOptions;
    private readonly IRSACryptographyService _rsaService;
    private readonly ILogger<RSAKeyLoadingService> _logger;

    public RSAKeyLoadingService(
        IOptions<RSAOptions> rsaOptions,
        IRSACryptographyService rsaService,
        ILogger<RSAKeyLoadingService> logger)
    {
        _rsaOptions = rsaOptions;
        _rsaService = rsaService;
        _logger = logger;
    }

    public async Task<RSAOperationResult<List<RSAKeyPairInfo>>> LoadAllConfiguredKeysAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var loadedKeys = new List<RSAKeyPairInfo>();
            var errors = new List<string>();

            foreach (var keyConfig in _rsaOptions.Value.Keys)
            {
                if (!keyConfig.Value.Enabled)
                {
                    _logger.LogDebug("Skipping disabled key configuration: {KeyIdentifier}", keyConfig.Key);
                    continue;
                }

                var result = await LoadKeyPairAsync(keyConfig.Key, cancellationToken);
                if (result.IsSuccess && result.Data != null)
                {
                    loadedKeys.Add(result.Data);
                    _logger.LogInformation("Successfully loaded RSA key pair: {KeyIdentifier}", keyConfig.Key);
                }
                else
                {
                    errors.Add($"Failed to load key {keyConfig.Key}: {result.ErrorMessage}");
                    _logger.LogError("Failed to load RSA key pair {KeyIdentifier}: {Error}", keyConfig.Key, result.ErrorMessage);
                }
            }

            if (loadedKeys.Any())
            {
                _logger.LogInformation("Successfully loaded {Count} RSA key pairs", loadedKeys.Count);
                return RSAOperationResult<List<RSAKeyPairInfo>>.Success(loadedKeys);
            }
            else
            {
                return RSAOperationResult<List<RSAKeyPairInfo>>.Failure(
                    $"No RSA keys were loaded successfully. Errors: {string.Join("; ", errors)}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while loading RSA keys");
            return RSAOperationResult<List<RSAKeyPairInfo>>.Failure("Unexpected error while loading RSA keys", exception: ex);
        }
    }

    public async Task<RSAOperationResult<RSAKeyPairInfo>> LoadKeyPairAsync(string keyIdentifier, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!_rsaOptions.Value.Keys.TryGetValue(keyIdentifier, out var keyConfig))
            {
                return RSAOperationResult<RSAKeyPairInfo>.Failure($"Key configuration not found for identifier: {keyIdentifier}");
            }

            if (!keyConfig.Enabled)
            {
                return RSAOperationResult<RSAKeyPairInfo>.Failure($"Key configuration is disabled for identifier: {keyIdentifier}");
            }

            // Read public key file
            var publicKeyPath = GetFullPath(keyConfig.PublicKeyPath);
            if (!File.Exists(publicKeyPath))
            {
                return RSAOperationResult<RSAKeyPairInfo>.Failure($"Public key file not found: {publicKeyPath}");
            }

            var publicKeyPem = await File.ReadAllTextAsync(publicKeyPath, cancellationToken);
            if (string.IsNullOrWhiteSpace(publicKeyPem))
            {
                return RSAOperationResult<RSAKeyPairInfo>.Failure($"Public key file is empty: {publicKeyPath}");
            }

            // Read private key file
            var privateKeyPath = GetFullPath(keyConfig.PrivateKeyPath);
            if (!File.Exists(privateKeyPath))
            {
                return RSAOperationResult<RSAKeyPairInfo>.Failure($"Private key file not found: {privateKeyPath}");
            }

            var privateKeyPem = await File.ReadAllTextAsync(privateKeyPath, cancellationToken);
            if (string.IsNullOrWhiteSpace(privateKeyPem))
            {
                return RSAOperationResult<RSAKeyPairInfo>.Failure($"Private key file is empty: {privateKeyPath}");
            }

            // Validate key formats
            if (!IsValidPemKey(publicKeyPem, false))
            {
                return RSAOperationResult<RSAKeyPairInfo>.Failure($"Invalid public key format in file: {publicKeyPath}");
            }

            if (!IsValidPemKey(privateKeyPem, true))
            {
                return RSAOperationResult<RSAKeyPairInfo>.Failure($"Invalid private key format in file: {privateKeyPath}");
            }

            // Load the key pair into the RSA service
            var loadResult = await _rsaService.LoadExistingKeyPairAsync(
                publicKeyPem, 
                privateKeyPem, 
                keyConfig.KeyIdentifier, 
                cancellationToken);

            if (!loadResult.IsSuccess)
            {
                return RSAOperationResult<RSAKeyPairInfo>.Failure(
                    $"Failed to load key pair into RSA service: {loadResult.ErrorMessage}");
            }

            // Create key pair info
            var keyPairInfo = new RSAKeyPairInfo
            {
                KeyIdentifier = keyConfig.KeyIdentifier,
                PublicKeyPem = publicKeyPem,
                PrivateKeyPem = privateKeyPem,
                KeySize = keyConfig.KeySize,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            return RSAOperationResult<RSAKeyPairInfo>.Success(keyPairInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading RSA key pair for identifier: {KeyIdentifier}", keyIdentifier);
            return RSAOperationResult<RSAKeyPairInfo>.Failure(
                $"Error loading RSA key pair: {ex.Message}", 
                exception: ex);
        }
    }

    public async Task<RSAOperationResult<List<string>>> ValidateKeyFilesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var validKeys = new List<string>();
            var errors = new List<string>();

            foreach (var keyConfig in _rsaOptions.Value.Keys)
            {
                if (!keyConfig.Value.Enabled)
                {
                    continue;
                }

                var publicKeyPath = GetFullPath(keyConfig.Value.PublicKeyPath);
                var privateKeyPath = GetFullPath(keyConfig.Value.PrivateKeyPath);

                if (!File.Exists(publicKeyPath))
                {
                    errors.Add($"Public key file not found: {publicKeyPath}");
                    continue;
                }

                if (!File.Exists(privateKeyPath))
                {
                    errors.Add($"Private key file not found: {privateKeyPath}");
                    continue;
                }

                // Try to read and validate key content
                try
                {
                    var publicKeyPem = await File.ReadAllTextAsync(publicKeyPath, cancellationToken);
                    var privateKeyPem = await File.ReadAllTextAsync(privateKeyPath, cancellationToken);

                    if (string.IsNullOrWhiteSpace(publicKeyPem))
                    {
                        errors.Add($"Public key file is empty: {publicKeyPath}");
                        continue;
                    }

                    if (string.IsNullOrWhiteSpace(privateKeyPem))
                    {
                        errors.Add($"Private key file is empty: {privateKeyPath}");
                        continue;
                    }

                    if (!IsValidPemKey(publicKeyPem, false))
                    {
                        errors.Add($"Invalid public key format: {publicKeyPath}");
                        continue;
                    }

                    if (!IsValidPemKey(privateKeyPem, true))
                    {
                        errors.Add($"Invalid private key format: {privateKeyPath}");
                        continue;
                    }

                    validKeys.Add(keyConfig.Key);
                }
                catch (Exception ex)
                {
                    errors.Add($"Error reading key files for {keyConfig.Key}: {ex.Message}");
                }
            }

            if (validKeys.Any())
            {
                return RSAOperationResult<List<string>>.Success(validKeys);
            }
            else
            {
                return RSAOperationResult<List<string>>.Failure(
                    $"No valid key files found. Errors: {string.Join("; ", errors)}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating RSA key files");
            return RSAOperationResult<List<string>>.Failure("Error validating RSA key files", exception: ex);
        }
    }

    public IEnumerable<string> GetConfiguredKeyIdentifiers()
    {
        return _rsaOptions.Value.Keys
            .Where(k => k.Value.Enabled)
            .Select(k => k.Key)
            .ToList();
    }

    private static string GetFullPath(string relativePath)
    {
        // If it's already an absolute path, return as is
        if (Path.IsPathRooted(relativePath))
        {
            return relativePath;
        }

        // Otherwise, make it relative to the application base directory
        return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, relativePath);
    }

    private static bool IsValidPemKey(string pemContent, bool isPrivateKey)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pemContent))
                return false;

            // Check for proper PEM headers
            if (isPrivateKey)
            {
                if (!pemContent.Contains("-----BEGIN PRIVATE KEY-----") && 
                    !pemContent.Contains("-----BEGIN RSA PRIVATE KEY-----"))
                    return false;
            }
            else
            {
                if (!pemContent.Contains("-----BEGIN PUBLIC KEY-----") && 
                    !pemContent.Contains("-----BEGIN RSA PUBLIC KEY-----"))
                    return false;
            }

            // Try to import the key to validate format
            using var rsa = RSA.Create();
            if (isPrivateKey)
            {
                rsa.ImportFromPem(pemContent);
            }
            else
            {
                rsa.ImportFromPem(pemContent);
            }

            return true;
        }
        catch
        {
            return false;
        }
    }
} 