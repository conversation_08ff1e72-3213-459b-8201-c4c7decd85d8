using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Xml.Serialization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TransferService.Application.Common.Models;
using TransferService.Application.Interfaces;
using System.Diagnostics;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// RSA cryptography service implementation providing encryption and decryption operations
/// </summary>
public class RSAService : IRSACryptographyService, IDisposable
{
    private readonly ILogger<RSAService> _logger;
    private readonly RSAEncryptionOptions _options;
    private readonly Dictionary<string, RSA> _keyPairs;
    private readonly object _lockObject = new();
    private bool _disposed = false;

    public RSAService(ILogger<RSAService> logger, IOptions<RSAEncryptionOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _keyPairs = new Dictionary<string, RSA>();
        
        // Initialize default key pair if specified
        if (!string.IsNullOrEmpty(_options.DefaultKeyIdentifier))
        {
            _ = GenerateKeyPairAsync(_options.DefaultKeySize, _options.DefaultKeyIdentifier);
        }
    }

    /// <summary>
    /// Load an existing RSA key pair from PEM strings
    /// </summary>
    /// <param name="publicKeyPem">Public key in PEM format</param>
    /// <param name="privateKeyPem">Private key in PEM format (optional for encryption-only scenarios)</param>
    /// <param name="keyIdentifier">Identifier for the key pair</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Key pair information</returns>
    public async Task<RSAOperationResult<RSAKeyPairInfo>> LoadExistingKeyPairAsync(string publicKeyPem, string? privateKeyPem = null, string? keyIdentifier = null, CancellationToken cancellationToken = default)
    {
        var effectiveKeyId = keyIdentifier ?? _options.DefaultKeyIdentifier ?? "default";

        try
        {
            // Load public key
            var publicKeyRsa = RSA.Create();
            publicKeyRsa.ImportFromPem(publicKeyPem);

            // Load private key if provided
            RSA? privateKeyRsa = null;
            if (!string.IsNullOrEmpty(privateKeyPem))
            {
                privateKeyRsa = RSA.Create();
                privateKeyRsa.ImportFromPem(privateKeyPem);
            }

            lock (_lockObject)
            {
                // Dispose existing key if it exists
                if (_keyPairs.TryGetValue(effectiveKeyId, out var existingRsa))
                {
                    existingRsa.Dispose();
                }

                // Store the key pair (use private key if available, otherwise public key)
                _keyPairs[effectiveKeyId] = privateKeyRsa ?? publicKeyRsa;
            }

            var keyPairInfo = new RSAKeyPairInfo
            {
                KeyIdentifier = effectiveKeyId,
                PublicKeyPem = publicKeyPem,
                PrivateKeyPem = privateKeyPem ?? string.Empty,
                KeySize = publicKeyRsa.KeySize,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            _logger.LogInformation("RSA key pair loaded successfully for identifier {KeyId} with size {KeySize} bits",
                effectiveKeyId, publicKeyRsa.KeySize);

            return RSAOperationResult<RSAKeyPairInfo>.Success(keyPairInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load RSA key pair for identifier {KeyId}", effectiveKeyId);
            return RSAOperationResult<RSAKeyPairInfo>.Failure($"Key loading failed: {ex.Message}", "KEY_LOADING_ERROR", ex);
        }
    }

    /// <summary>
    /// Load a public key for encryption-only operations
    /// </summary>
    /// <param name="publicKeyPem">Public key in PEM format</param>
    /// <param name="keyIdentifier">Identifier for the key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Key information</returns>
    public async Task<RSAOperationResult<RSAKeyPairInfo>> LoadPublicKeyAsync(string publicKeyPem, string? keyIdentifier = null, CancellationToken cancellationToken = default)
    {
        return await LoadExistingKeyPairAsync(publicKeyPem, null, keyIdentifier, cancellationToken);
    }

    public async Task<RSAOperationResult<string>> EncryptAsync(string plainText, string? keyIdentifier = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(plainText))
        {
            return RSAOperationResult<string>.Failure("Plain text cannot be null or empty", "INVALID_INPUT");
        }

        var stopwatch = Stopwatch.StartNew();
        var effectiveKeyId = keyIdentifier ?? _options.DefaultKeyIdentifier ?? "default";

        try
        {
            var rsa = await GetOrCreateKeyPairAsync(effectiveKeyId, cancellationToken);
            if (rsa == null)
            {
                return RSAOperationResult<string>.Failure($"Failed to get RSA key pair for identifier: {effectiveKeyId}", "KEY_NOT_FOUND");
            }

            var dataBytes = Encoding.UTF8.GetBytes(plainText);
            
            // Check data size limit
            var maxSize = GetMaxDataSize(effectiveKeyId);
            if (dataBytes.Length > maxSize)
            {
                return RSAOperationResult<string>.Failure($"Data size ({dataBytes.Length} bytes) exceeds maximum allowed size ({maxSize} bytes)", "DATA_TOO_LARGE");
            }

            var encryptedBytes = rsa.Encrypt(dataBytes, RSAEncryptionPadding.OaepSHA256);
            var encryptedBase64 = Convert.ToBase64String(encryptedBytes);

            stopwatch.Stop();
            
            if (_options.EnablePerformanceLogging)
            {
                _logger.LogInformation("RSA encryption completed in {ElapsedMs}ms for key {KeyId}, data size: {DataSize} bytes", 
                    stopwatch.ElapsedMilliseconds, effectiveKeyId, dataBytes.Length);
            }

            return RSAOperationResult<string>.Success(encryptedBase64);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            if (_options.EnableDetailedErrorLogging)
            {
                _logger.LogError(ex, "RSA encryption failed for key {KeyId} after {ElapsedMs}ms", effectiveKeyId, stopwatch.ElapsedMilliseconds);
            }
            
            return RSAOperationResult<string>.Failure($"Encryption failed: {ex.Message}", "ENCRYPTION_ERROR", ex);
        }
    }

    public async Task<RSAOperationResult<string>> DecryptAsync(string encryptedText, string? keyIdentifier = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(encryptedText))
        {
            return RSAOperationResult<string>.Failure("Encrypted text cannot be null or empty", "INVALID_INPUT");
        }

        var stopwatch = Stopwatch.StartNew();
        var effectiveKeyId = keyIdentifier ?? _options.DefaultKeyIdentifier ?? "default";

        try
        {
            var rsa = await GetOrCreateKeyPairAsync(effectiveKeyId, cancellationToken);
            if (rsa == null)
            {
                return RSAOperationResult<string>.Failure($"Failed to get RSA key pair for identifier: {effectiveKeyId}", "KEY_NOT_FOUND");
            }

            var encryptedBytes = Convert.FromBase64String(encryptedText);
            var decryptedBytes = rsa.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA256);
            var decryptedText = Encoding.UTF8.GetString(decryptedBytes);

            stopwatch.Stop();
            
            if (_options.EnablePerformanceLogging)
            {
                _logger.LogInformation("RSA decryption completed in {ElapsedMs}ms for key {KeyId}, data size: {DataSize} bytes", 
                    stopwatch.ElapsedMilliseconds, effectiveKeyId, decryptedBytes.Length);
            }

            return RSAOperationResult<string>.Success(decryptedText);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            if (_options.EnableDetailedErrorLogging)
            {
                _logger.LogError(ex, "RSA decryption failed for key {KeyId} after {ElapsedMs}ms", effectiveKeyId, stopwatch.ElapsedMilliseconds);
            }
            
            return RSAOperationResult<string>.Failure($"Decryption failed: {ex.Message}", "DECRYPTION_ERROR", ex);
        }
    }

    public async Task<RSAOperationResult<string>> EncryptObjectAsync<T>(T obj, SerializationFormat serializationFormat = SerializationFormat.Json, string? keyIdentifier = null, CancellationToken cancellationToken = default) where T : class
    {
        if (obj == null)
        {
            return RSAOperationResult<string>.Failure("Object cannot be null", "INVALID_INPUT");
        }

        try
        {
            string serializedData;
            
            switch (serializationFormat)
            {
                case SerializationFormat.Json:
                    serializedData = JsonSerializer.Serialize(obj, new JsonSerializerOptions 
                    { 
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        WriteIndented = false
                    });
                    break;
                    
                case SerializationFormat.Xml:
                    var xmlSerializer = new XmlSerializer(typeof(T));
                    using var stringWriter = new StringWriter();
                    xmlSerializer.Serialize(stringWriter, obj);
                    serializedData = stringWriter.ToString();
                    break;
                    
                default:
                    return RSAOperationResult<string>.Failure($"Unsupported serialization format: {serializationFormat}", "UNSUPPORTED_FORMAT");
            }

            return await EncryptAsync(serializedData, keyIdentifier, cancellationToken);
        }
        catch (Exception ex)
        {
            if (_options.EnableDetailedErrorLogging)
            {
                _logger.LogError(ex, "Object serialization failed for type {ObjectType} with format {Format}", typeof(T).Name, serializationFormat);
            }
            
            return RSAOperationResult<string>.Failure($"Object encryption failed: {ex.Message}", "OBJECT_ENCRYPTION_ERROR", ex);
        }
    }

    public async Task<RSAOperationResult<T>> DecryptObjectAsync<T>(string encryptedData, SerializationFormat serializationFormat = SerializationFormat.Json, string? keyIdentifier = null, CancellationToken cancellationToken = default) where T : class
    {
        var decryptResult = await DecryptAsync(encryptedData, keyIdentifier, cancellationToken);
        
        if (!decryptResult.IsSuccess)
        {
            return RSAOperationResult<T>.Failure(decryptResult.ErrorMessage!, decryptResult.ErrorCode, decryptResult.Exception);
        }

        try
        {
            T deserializedObject;
            
            switch (serializationFormat)
            {
                case SerializationFormat.Json:
                    deserializedObject = JsonSerializer.Deserialize<T>(decryptResult.Data!, new JsonSerializerOptions 
                    { 
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        PropertyNameCaseInsensitive = true
                    })!;
                    break;
                    
                case SerializationFormat.Xml:
                    var xmlSerializer = new XmlSerializer(typeof(T));
                    using var stringReader = new StringReader(decryptResult.Data!);
                    deserializedObject = (T)xmlSerializer.Deserialize(stringReader)!;
                    break;
                    
                default:
                    return RSAOperationResult<T>.Failure($"Unsupported serialization format: {serializationFormat}", "UNSUPPORTED_FORMAT");
            }

            return RSAOperationResult<T>.Success(deserializedObject);
        }
        catch (Exception ex)
        {
            if (_options.EnableDetailedErrorLogging)
            {
                _logger.LogError(ex, "Object deserialization failed for type {ObjectType} with format {Format}", typeof(T).Name, serializationFormat);
            }
            
            return RSAOperationResult<T>.Failure($"Object decryption failed: {ex.Message}", "OBJECT_DECRYPTION_ERROR", ex);
        }
    }

    public async Task<RSAOperationResult<RSAKeyPairInfo>> GenerateKeyPairAsync(int keySize = 2048, string? keyIdentifier = null, CancellationToken cancellationToken = default)
    {
        var effectiveKeyId = keyIdentifier ?? _options.DefaultKeyIdentifier ?? "default";

        if (keySize != 2048 && keySize != 3072 && keySize != 4096)
        {
            return RSAOperationResult<RSAKeyPairInfo>.Failure($"Invalid key size: {keySize}. Supported sizes are 2048, 3072, and 4096 bits.", "INVALID_KEY_SIZE");
        }

        var stopwatch = Stopwatch.StartNew();

        try
        {
            var rsa = RSA.Create(keySize);

            lock (_lockObject)
            {
                // Dispose existing key if it exists
                if (_keyPairs.TryGetValue(effectiveKeyId, out var existingRsa))
                {
                    existingRsa.Dispose();
                }

                _keyPairs[effectiveKeyId] = rsa;
            }

            var publicKeyPem = rsa.ExportRSAPublicKeyPem();
            var privateKeyPem = rsa.ExportRSAPrivateKeyPem();

            var keyPairInfo = new RSAKeyPairInfo
            {
                KeyIdentifier = effectiveKeyId,
                PublicKeyPem = publicKeyPem,
                PrivateKeyPem = privateKeyPem,
                KeySize = keySize,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            stopwatch.Stop();

            _logger.LogInformation("RSA key pair generated successfully for identifier {KeyId} with size {KeySize} bits in {ElapsedMs}ms",
                effectiveKeyId, keySize, stopwatch.ElapsedMilliseconds);

            return RSAOperationResult<RSAKeyPairInfo>.Success(keyPairInfo);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();

            _logger.LogError(ex, "Failed to generate RSA key pair for identifier {KeyId} with size {KeySize} bits after {ElapsedMs}ms",
                effectiveKeyId, keySize, stopwatch.ElapsedMilliseconds);

            return RSAOperationResult<RSAKeyPairInfo>.Failure($"Key generation failed: {ex.Message}", "KEY_GENERATION_ERROR", ex);
        }
    }

    public async Task<RSAOperationResult<string>> GetPublicKeyAsync(string? keyIdentifier = null, CancellationToken cancellationToken = default)
    {
        var effectiveKeyId = keyIdentifier ?? _options.DefaultKeyIdentifier ?? "default";

        try
        {
            var rsa = await GetOrCreateKeyPairAsync(effectiveKeyId, cancellationToken);
            if (rsa == null)
            {
                return RSAOperationResult<string>.Failure($"Failed to get RSA key pair for identifier: {effectiveKeyId}", "KEY_NOT_FOUND");
            }

            var publicKeyPem = rsa.ExportRSAPublicKeyPem();
            return RSAOperationResult<string>.Success(publicKeyPem);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get public key for identifier {KeyId}", effectiveKeyId);
            return RSAOperationResult<string>.Failure($"Failed to get public key: {ex.Message}", "PUBLIC_KEY_ERROR", ex);
        }
    }

    public async Task<RSAOperationResult<RSAHealthStatus>> ValidateServiceHealthAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var details = new Dictionary<string, object>();

        try
        {
            // Test encryption/decryption with a small test string
            const string testData = "RSA Health Check Test";
            var testKeyId = "health-check-test";

            var encryptResult = await EncryptAsync(testData, testKeyId, cancellationToken);
            if (!encryptResult.IsSuccess)
            {
                details["encryption_test"] = "FAILED";
                details["encryption_error"] = encryptResult.ErrorMessage ?? "Unknown error";

                return RSAOperationResult<RSAHealthStatus>.Success(new RSAHealthStatus
                {
                    IsHealthy = false,
                    Status = "UNHEALTHY",
                    Details = details,
                    CheckedAt = DateTime.UtcNow,
                    ResponseTime = stopwatch.Elapsed
                });
            }

            var decryptResult = await DecryptAsync(encryptResult.Data!, testKeyId, cancellationToken);
            if (!decryptResult.IsSuccess || decryptResult.Data != testData)
            {
                details["decryption_test"] = "FAILED";
                details["decryption_error"] = decryptResult.ErrorMessage ?? "Data mismatch";

                return RSAOperationResult<RSAHealthStatus>.Success(new RSAHealthStatus
                {
                    IsHealthy = false,
                    Status = "UNHEALTHY",
                    Details = details,
                    CheckedAt = DateTime.UtcNow,
                    ResponseTime = stopwatch.Elapsed
                });
            }

            // Clean up test key
            lock (_lockObject)
            {
                if (_keyPairs.TryGetValue(testKeyId, out var testRsa))
                {
                    testRsa.Dispose();
                    _keyPairs.Remove(testKeyId);
                }
            }

            stopwatch.Stop();

            details["encryption_test"] = "PASSED";
            details["decryption_test"] = "PASSED";
            details["active_keys"] = _keyPairs.Count;
            details["test_data_size"] = testData.Length;

            return RSAOperationResult<RSAHealthStatus>.Success(new RSAHealthStatus
            {
                IsHealthy = true,
                Status = "HEALTHY",
                Details = details,
                CheckedAt = DateTime.UtcNow,
                ResponseTime = stopwatch.Elapsed
            });
        }
        catch (Exception ex)
        {
            stopwatch.Stop();

            details["health_check_error"] = ex.Message;

            _logger.LogError(ex, "RSA service health check failed after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

            return RSAOperationResult<RSAHealthStatus>.Success(new RSAHealthStatus
            {
                IsHealthy = false,
                Status = "UNHEALTHY",
                Details = details,
                CheckedAt = DateTime.UtcNow,
                ResponseTime = stopwatch.Elapsed
            });
        }
    }

    public int GetMaxDataSize(string? keyIdentifier = null)
    {
        var effectiveKeyId = keyIdentifier ?? _options.DefaultKeyIdentifier ?? "default";

        lock (_lockObject)
        {
            if (_keyPairs.TryGetValue(effectiveKeyId, out var rsa))
            {
                // For OAEP SHA-256 padding, the maximum data size is:
                // key_size_in_bytes - 2 * hash_length - 2
                // For SHA-256: hash_length = 32 bytes
                var keySize = rsa.KeySize;
                return (keySize / 8) - (2 * 32) - 2;
            }
        }

        // Default for 2048-bit key
        return _options.MaxDataSizeBytes;
    }

    private async Task<RSA?> GetOrCreateKeyPairAsync(string keyIdentifier, CancellationToken cancellationToken = default)
    {
        lock (_lockObject)
        {
            if (_keyPairs.TryGetValue(keyIdentifier, out var existingRsa))
            {
                return existingRsa;
            }
        }

        // Generate new key pair if not found
        var keyPairResult = await GenerateKeyPairAsync(_options.DefaultKeySize, keyIdentifier, cancellationToken);

        if (!keyPairResult.IsSuccess)
        {
            _logger.LogError("Failed to generate key pair for identifier {KeyId}: {Error}", keyIdentifier, keyPairResult.ErrorMessage);
            return null;
        }

        lock (_lockObject)
        {
            return _keyPairs.TryGetValue(keyIdentifier, out var rsa) ? rsa : null;
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        lock (_lockObject)
        {
            foreach (var keyPair in _keyPairs.Values)
            {
                keyPair?.Dispose();
            }
            _keyPairs.Clear();
        }

        _disposed = true;
        GC.SuppressFinalize(this);
    }
}
