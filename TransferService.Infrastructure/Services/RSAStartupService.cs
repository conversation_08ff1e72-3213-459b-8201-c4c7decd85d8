using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TransferService.Application.Configuration;
using TransferService.Application.Services;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// Startup service to automatically load RSA keys from configuration
/// </summary>
public class RSAStartupService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RSAStartupService> _logger;
    private readonly IOptions<RSAOptions> _rsaOptions;

    public RSAStartupService(
        IServiceProvider serviceProvider,
        ILogger<RSAStartupService> logger,
        IOptions<RSAOptions> rsaOptions)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _rsaOptions = rsaOptions;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Check if auto-loading is enabled
            if (!_rsaOptions.Value.Encryption.AutoLoadKeys)
            {
                _logger.LogInformation("RSA key auto-loading is disabled in configuration");
                return;
            }

            _logger.LogInformation("Starting RSA key auto-loading process...");

            // Create a scope to resolve services
            using var scope = _serviceProvider.CreateScope();
            var keyLoadingService = scope.ServiceProvider.GetRequiredService<IRSAKeyLoadingService>();

            // Validate key files first
            var validationResult = await keyLoadingService.ValidateKeyFilesAsync(cancellationToken);
            if (!validationResult.IsSuccess)
            {
                _logger.LogWarning("RSA key file validation failed: {Error}", validationResult.ErrorMessage);
                return;
            }

            _logger.LogInformation("RSA key files validation successful. Found {Count} valid key configurations", 
                validationResult.Data?.Count ?? 0);

            // Load all configured keys
            var loadResult = await keyLoadingService.LoadAllConfiguredKeysAsync(cancellationToken);
            if (loadResult.IsSuccess && loadResult.Data != null)
            {
                _logger.LogInformation("Successfully loaded {Count} RSA key pairs during startup", loadResult.Data.Count);
                
                foreach (var keyInfo in loadResult.Data)
                {
                    _logger.LogDebug("Loaded RSA key: {KeyIdentifier} (Size: {KeySize} bits)", 
                        keyInfo.KeyIdentifier, keyInfo.KeySize);
                }
            }
            else
            {
                _logger.LogError("Failed to load RSA keys during startup: {Error}", loadResult.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during RSA key auto-loading startup");
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("RSA startup service stopping...");
        return Task.CompletedTask;
    }
} 