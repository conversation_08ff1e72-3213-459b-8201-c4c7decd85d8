using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using TransferService.Application.Interfaces;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// Simple RSA service implementation for encryption/decryption operations
/// </summary>
public class SimpleRSAService : IRSAService
{
    private readonly ILogger<SimpleRSAService> _logger;
    private readonly RSA _yourPrivateKey;
    private readonly RSA _yourPublicKey;
    private readonly RSA _thirdPartyPublicKey;

    public SimpleRSAService(ILogger<SimpleRSAService> logger, IConfiguration configuration)
    {
        _logger = logger;

        try
        {
            // Load your private key
            var yourPrivateKeyPath = configuration["RSA:YourPrivateKeyPath"] ?? "keys/your-private.key";
            var yourPrivateKeyContent = File.ReadAllText(yourPrivateKeyPath);
            _yourPrivateKey = RSA.Create();
            _yourPrivateKey.ImportFromPem(yourPrivateKeyContent);

            // Load your public key (extract from private key)
            _yourPublicKey = RSA.Create();
            _yourPublicKey.ImportFromPem(yourPrivateKeyContent);

            // Load third-party public key
            var thirdPartyPublicKeyPath = configuration["RSA:ThirdPartyPublicKeyPath"] ?? "keys/third-party-public.key";
            var thirdPartyPublicKeyContent = File.ReadAllText(thirdPartyPublicKeyPath);
            _thirdPartyPublicKey = RSA.Create();
            _thirdPartyPublicKey.ImportFromPem(thirdPartyPublicKeyContent);

            _logger.LogInformation("RSA keys loaded successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load RSA keys");
            throw;
        }
    }

    public string Encrypt(string plainText)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(plainText);
            var encryptedBytes = _thirdPartyPublicKey.Encrypt(dataBytes, RSAEncryptionPadding.OaepSHA1);
            return Convert.ToBase64String(encryptedBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt data");
            throw;
        }
    }

    public string Decrypt(string encryptedData)
    {
        try
        {
            var encryptedBytes = Convert.FromBase64String(encryptedData);
            var decryptedBytes = _yourPrivateKey.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA256);
            return Encoding.UTF8.GetString(decryptedBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt data");
            throw;
        }
    }

    public string Sign(string data)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signatureBytes = _yourPrivateKey.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            return Convert.ToBase64String(signatureBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sign data");
            throw;
        }
    }

    public bool VerifySignature(string data, string signature)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signatureBytes = Convert.FromBase64String(signature);
            return _thirdPartyPublicKey.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify signature");
            return false;
        }
    }


}
