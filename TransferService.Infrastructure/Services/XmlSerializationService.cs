using System.Text;
using System.Xml;
using System.Xml.Serialization;
using Microsoft.Extensions.Logging;

namespace TransferService.Infrastructure.Services;

/// <summary>
/// Service for XML serialization and deserialization operations
/// </summary>
public interface IXmlSerializationService
{
    /// <summary>
    /// Serializes an object to XML string
    /// </summary>
    /// <typeparam name="T">Type to serialize</typeparam>
    /// <param name="obj">Object to serialize</param>
    /// <returns>XML string</returns>
    string SerializeToXml<T>(T obj) where T : class;
    
    /// <summary>
    /// Deserializes XML string to object
    /// </summary>
    /// <typeparam name="T">Type to deserialize to</typeparam>
    /// <param name="xml">XML string</param>
    /// <returns>Deserialized object</returns>
    T DeserializeFromXml<T>(string xml) where T : class;
}

/// <summary>
/// Implementation of XML serialization service
/// </summary>
public class XmlSerializationService : IXmlSerializationService
{
    private readonly ILogger<XmlSerializationService> _logger;

    public XmlSerializationService(ILogger<XmlSerializationService> logger)
    {
        _logger = logger;
    }

    public string SerializeToXml<T>(T obj) where T : class
    {
        if (obj == null)
        {
            throw new ArgumentNullException(nameof(obj));
        }

        try
        {
            var serializer = new XmlSerializer(typeof(T));
            
            // Configure XML writer settings for clean output
            var settings = new XmlWriterSettings
            {
                Encoding = Encoding.UTF8,
                Indent = true,
                IndentChars = "    ",
                OmitXmlDeclaration = false,
                NewLineChars = "\r\n"
            };

            using var stringWriter = new StringWriter();
            using var xmlWriter = XmlWriter.Create(stringWriter, settings);
            
            // Remove default namespaces for cleaner XML
            var namespaces = new XmlSerializerNamespaces();
            namespaces.Add("", "");
            
            serializer.Serialize(xmlWriter, obj, namespaces);
            
            var result = stringWriter.ToString();
            _logger.LogDebug("Serialized {Type} to XML: {Xml}", typeof(T).Name, result);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to serialize {Type} to XML", typeof(T).Name);
            throw new InvalidOperationException($"Failed to serialize {typeof(T).Name} to XML", ex);
        }
    }

    public T DeserializeFromXml<T>(string xml) where T : class
    {
        if (string.IsNullOrWhiteSpace(xml))
        {
            throw new ArgumentException("XML string cannot be null or empty", nameof(xml));
        }

        try
        {
            var serializer = new XmlSerializer(typeof(T));
            
            using var stringReader = new StringReader(xml);
            using var xmlReader = XmlReader.Create(stringReader);
            
            var result = (T)serializer.Deserialize(xmlReader)!;
            
            _logger.LogDebug("Deserialized XML to {Type}: {Xml}", typeof(T).Name, xml);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to deserialize XML to {Type}: {Xml}", typeof(T).Name, xml);
            throw new InvalidOperationException($"Failed to deserialize XML to {typeof(T).Name}", ex);
        }
    }
}
