<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\TransferService.Domain\TransferService.Domain.csproj" />
        <ProjectReference Include="..\TransferService.Application\TransferService.Application.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="AspNetCore.HealthChecks.Uris" Version="9.0.0" />
      <PackageReference Include="PgpCore" Version="6.5.2" />
      <PackageReference Include="ZiggyCreatures.FusionCache" Version="2.3.0" />
      <PackageReference Include="ZiggyCreatures.FusionCache.Serialization.SystemTextJson" Version="2.3.0" />
      <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.0" />
      <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.6" />
      <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
      <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
      <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
      <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
      <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
      <PackageReference Include="System.ServiceModel.Http" Version="8.1.2" />
    </ItemGroup>

</Project>
