using Microsoft.Extensions.Logging;
using Moq;
using TransferService.Application.Features.EasyPay.Commands;
using TransferService.Application.Interfaces;
using TransferService.Application.Features.Transfers.Models;
using Xunit;

namespace TransferService.Tests.Application.EasyPay;

public class EasyPayCommandHandlerTests
{
    private readonly Mock<IEasyPayTransferService> _mockEasyPayService;
    private readonly Mock<ILogger<EasyPayNameEnquiryCommandHandler>> _mockNameEnquiryLogger;
    private readonly Mock<ILogger<EasyPayTransferCommandHandler>> _mockTransferLogger;

    public EasyPayCommandHandlerTests()
    {
        _mockEasyPayService = new Mock<IEasyPayTransferService>();
        _mockNameEnquiryLogger = new Mock<ILogger<EasyPayNameEnquiryCommandHandler>>();
        _mockTransferLogger = new Mock<ILogger<EasyPayTransferCommandHandler>>();
    }

    [Fact]
    public async Task EasyPayNameEnquiryCommandHandler_WhenCalled_ReturnsResult()
    {
        // Arrange
        var command = new EasyPayNameEnquiryCommand
        {
            AccountNumber = "**********",
            BankCode = "058"
        };

        var expectedResult = new EasyPayNameEnquiryResultDto
        {
            AccountName = "John Doe",
            AccountNumber = "**********",
            IsSuccessful = true,
            ErrorMessage = ""
        };

        _mockEasyPayService.Setup(x => x.NameEnquiryAsync(command.AccountNumber, command.BankCode, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        var handler = new EasyPayNameEnquiryCommandHandler(_mockEasyPayService.Object, _mockNameEnquiryLogger.Object);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.Equal(expectedResult, result);
        _mockEasyPayService.Verify(x => x.NameEnquiryAsync(command.AccountNumber, command.BankCode, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task EasyPayTransferCommandHandler_WhenCalled_ReturnsResult()
    {
        // Arrange
        var command = new EasyPayTransferCommand
        {
            Amount = 1000m,
            AccountNumber = "**********",
            BankCode = "058",
            AccountName = "John Doe",
            Narration = "Test transfer"
        };

        var expectedResult = new EasyPayTransferResultDto
        {
            TransactionId = "TXN123456",
            Status = "000",
            IsSuccessful = true,
            RequiresProcessing = true,
            ErrorMessage = ""
        };

        _mockEasyPayService.Setup(x => x.TransferAsync(
            It.IsAny<EasyPayTransferCommand>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        var handler = new EasyPayTransferCommandHandler(_mockEasyPayService.Object, _mockTransferLogger.Object);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.Equal(expectedResult, result);
        _mockEasyPayService.Verify(x => x.TransferAsync(
            It.IsAny<EasyPayTransferCommand>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory]
    [InlineData("")]
    [InlineData("*********")] // Too short
    [InlineData("**********1")] // Too long
    [InlineData("*********a")] // Contains letter
    public void EasyPayNameEnquiryCommandValidator_WithInvalidAccountNumber_FailsValidation(string accountNumber)
    {
        // Arrange
        var command = new EasyPayNameEnquiryCommand
        {
            AccountNumber = accountNumber,
            BankCode = "058"
        };

        var validator = new EasyPayNameEnquiryCommandValidator();

        // Act
        var result = validator.Validate(command);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == nameof(command.AccountNumber));
    }

    [Theory]
    [InlineData("")]
    [InlineData("12")] // Too short
    [InlineData("1234567")] // Too long
    [InlineData("05a")] // Contains letter
    public void EasyPayNameEnquiryCommandValidator_WithInvalidBankCode_FailsValidation(string bankCode)
    {
        // Arrange
        var command = new EasyPayNameEnquiryCommand
        {
            AccountNumber = "**********",
            BankCode = bankCode
        };

        var validator = new EasyPayNameEnquiryCommandValidator();

        // Act
        var result = validator.Validate(command);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == nameof(command.BankCode));
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-100)]
    [InlineData(1000001)] // Too large
    public void EasyPayTransferCommandValidator_WithInvalidAmount_FailsValidation(decimal amount)
    {
        // Arrange
        var command = new EasyPayTransferCommand
        {
            Amount = amount,
            AccountNumber = "**********",
            BankCode = "058",
            AccountName = "John Doe",
            Narration = "Test transfer"
        };

        var validator = new EasyPayTransferCommandValidator();

        // Act
        var result = validator.Validate(command);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == nameof(command.Amount));
    }

    [Fact]
    public void EasyPayTransferCommandValidator_WithValidCommand_PassesValidation()
    {
        // Arrange
        var command = new EasyPayTransferCommand
        {
            Amount = 1000m,
            AccountNumber = "**********",
            BankCode = "058",
            AccountName = "John Doe",
            Narration = "Test transfer"
        };

        var validator = new EasyPayTransferCommandValidator();

        // Act
        var result = validator.Validate(command);

        // Assert
        Assert.True(result.IsValid);
    }
}
