using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using TransferService.Application.Features.EasyPay.Commands;
using TransferService.Application.Features.EasyPay.Queries;
using TransferService.Application.Interfaces;
using TransferService.Controllers;
using TransferService.Application.Features.Transfers.Models;
using Xunit;

namespace TransferService.Tests.Controllers;

public class EasyPayControllerTests
{
    private readonly Mock<IMediator> _mockMediator;
    private readonly Mock<IRSAService> _mockRsaService;
    private readonly Mock<ILogger<EasyPayController>> _mockLogger;
    private readonly EasyPayController _controller;

    public EasyPayControllerTests()
    {
        _mockMediator = new Mock<IMediator>();
        _mockRsaService = new Mock<IRSAService>();
        _mockLogger = new Mock<ILogger<EasyPayController>>();
        _controller = new EasyPayController(_mockMediator.Object, _mockRsaService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task NameEnquiry_WhenSuccessful_ReturnsOkResult()
    {
        // Arrange
        var request = new EasyPayNameEnquiryDto
        {
            AccountNumber = "**********",
            BankCode = "058"
        };

        var expectedResult = new EasyPayNameEnquiryResultDto
        {
            AccountName = "John Doe",
            AccountNumber = "**********",
            IsSuccessful = true,
            ErrorMessage = ""
        };

        _mockMediator.Setup(x => x.Send(It.IsAny<EasyPayNameEnquiryCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.NameEnquiry(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnValue = Assert.IsType<EasyPayNameEnquiryResultDto>(okResult.Value);
        Assert.Equal(expectedResult.AccountName, returnValue.AccountName);
        Assert.True(returnValue.IsSuccessful);
    }

    [Fact]
    public async Task Transfer_WhenSuccessful_ReturnsOkResult()
    {
        // Arrange
        var request = new EasyPayTransferDto
        {
            Amount = 1000m,
            AccountNumber = "**********",
            BankCode = "058",
            AccountName = "John Doe",
            Narration = "Test transfer"
        };

        var expectedResult = new EasyPayTransferResultDto
        {
            TransactionId = "TXN123456",
            Status = "000",
            IsSuccessful = true,
            RequiresProcessing = true,
            ErrorMessage = ""
        };

        _mockMediator.Setup(x => x.Send(It.IsAny<EasyPayTransferCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.Transfer(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnValue = Assert.IsType<EasyPayTransferResultDto>(okResult.Value);
        Assert.Equal(expectedResult.TransactionId, returnValue.TransactionId);
        Assert.True(returnValue.IsSuccessful);
    }

    [Fact]
    public async Task GetWalletBalance_WhenSuccessful_ReturnsOkResult()
    {
        // Arrange
        var expectedResult = new EasyPayBalanceDto
        {
            Balance = 50000m,
            Status = "00",
            IsSuccessful = true,
            ErrorMessage = ""
        };

        _mockMediator.Setup(x => x.Send(It.IsAny<EasyPayBalanceQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetWalletBalance();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnValue = Assert.IsType<EasyPayBalanceDto>(okResult.Value);
        Assert.Equal(expectedResult.Balance, returnValue.Balance);
        Assert.True(returnValue.IsSuccessful);
    }

    [Fact]
    public async Task CreditWallet_WhenSuccessful_ReturnsOkResult()
    {
        // Arrange
        var request = new EasyPayCreditWalletDto
        {
            Amount = 10000m
        };

        var expectedResult = new EasyPayCreditWalletResultDto
        {
            Status = "00",
            IsSuccessful = true,
            ErrorMessage = ""
        };

        _mockMediator.Setup(x => x.Send(It.IsAny<EasyPayCreditWalletCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.CreditWallet(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnValue = Assert.IsType<EasyPayCreditWalletResultDto>(okResult.Value);
        Assert.Equal(expectedResult.Status, returnValue.Status);
        Assert.True(returnValue.IsSuccessful);
    }

    [Fact]
    public async Task TransactionStatusQuery_WhenSuccessful_ReturnsOkResult()
    {
        // Arrange
        var request = new EasyPayTSQDto
        {
            SessionId = "SESSION123"
        };

        var expectedResult = new EasyPayTSQResultDto
        {
            Status = "00",
            IsSuccessful = true,
            ErrorMessage = ""
        };

        _mockMediator.Setup(x => x.Send(It.IsAny<EasyPayTSQQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.TransactionStatusQuery(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnValue = Assert.IsType<EasyPayTSQResultDto>(okResult.Value);
        Assert.Equal(expectedResult.Status, returnValue.Status);
        Assert.True(returnValue.IsSuccessful);
    }

    [Fact]
    public async Task NameEnquiry_WhenFailed_ReturnsOkWithErrorDetails()
    {
        // Arrange
        var request = new EasyPayNameEnquiryDto
        {
            AccountNumber = "**********",
            BankCode = "058"
        };

        var expectedResult = new EasyPayNameEnquiryResultDto
        {
            AccountName = "",
            AccountNumber = "**********",
            IsSuccessful = false,
            ErrorMessage = "Account not found"
        };

        _mockMediator.Setup(x => x.Send(It.IsAny<EasyPayNameEnquiryCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.NameEnquiry(request);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
        var returnValue = Assert.IsType<EasyPayNameEnquiryResultDto>(badRequestResult.Value);
        Assert.False(returnValue.IsSuccessful);
        Assert.Equal("Account not found", returnValue.ErrorMessage);
    }
}
