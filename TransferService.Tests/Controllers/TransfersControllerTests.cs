using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using TransferService.Application.Features.EasyPay.Commands;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;
using TransferService.Controllers;
using Xunit;

namespace TransferService.Tests.Controllers;

public class TransfersControllerTests
{
    private readonly Mock<IPaymentGatewayService> _mockPaymentGateway;
    private readonly Mock<IXmlValidationService> _mockValidationService;
    private readonly Mock<IXmlResponseFactory> _mockResponseFactory;
    private readonly Mock<IEncryptedXmlService> _mockEncryptedXmlService;
    private readonly Mock<ILogger<BelemaEasyTransferController>> _mockLogger;
    private readonly BelemaEasyTransferController _controller;

    public TransfersControllerTests()
    {
        _mockPaymentGateway = new Mock<IPaymentGatewayService>();
        _mockValidationService = new Mock<IXmlValidationService>();
        _mockResponseFactory = new Mock<IXmlResponseFactory>();
        _mockEncryptedXmlService = new Mock<IEncryptedXmlService>();
        _mockLogger = new Mock<ILogger<BelemaEasyTransferController>>();

        _controller = new BelemaEasyTransferController(
            _mockPaymentGateway.Object,
            _mockValidationService.Object,
            _mockResponseFactory.Object,
            _mockEncryptedXmlService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task NameEnquiry_WithValidEncryptedXmlRequest_ReturnsEncryptedXmlResponse()
    {
        // Arrange
        var encryptedPayload = "encrypted-base64-payload";
        var xmlRequest = new NameEnquiryXmlRequest
        {
            SessionId = "SESSION*********",
            DestinationInstitutionCode = "011",
            ChannelCode = "BELEMA_VA",
            AccountNumber = "**********"
        };

        var expectedResult = new PaymentNameEnquiryResultDto
        {
            AccountName = "JOHN DOE SMITH",
            IsSuccessful = true,
            ErrorMessage = "",
            Provider = "EasyPay"
        };

        var expectedXmlResponse = new NameEnquiryXmlResponse
        {
            SessionId = xmlRequest.SessionId,
            DestinationInstitutionCode = xmlRequest.DestinationInstitutionCode,
            ChannelCode = xmlRequest.ChannelCode,
            AccountNumber = xmlRequest.AccountNumber,
            AccountName = expectedResult.AccountName,
            BankVerificationNumber = "***********",
            KycLevel = "3",
            ResponseCode = "00"
        };

        var encryptedResponse = "encrypted-response-payload";

        // Setup encrypted XML service
        _mockEncryptedXmlService.Setup(x => x.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedPayload))
            .ReturnsAsync(xmlRequest);
        _mockEncryptedXmlService.Setup(x => x.SerializeAndEncryptAsync(expectedXmlResponse))
            .ReturnsAsync(encryptedResponse);

        // Setup validation to pass
        _mockValidationService.Setup(x => x.ValidateAsync(xmlRequest))
            .ReturnsAsync(new ValidationResult { IsValid = true, Errors = Enumerable.Empty<string>() });

        // Setup payment gateway
        _mockPaymentGateway.Setup(x => x.ProcessNameEnquiryAsync(It.IsAny<PaymentNameEnquiryCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Setup response factory
        _mockResponseFactory.Setup(x => x.CreateNameEnquirySuccessResponse(xmlRequest, expectedResult))
            .Returns(expectedXmlResponse);

        // Act
        var result = await _controller.NameEnquiry(encryptedPayload, CancellationToken.None);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var encryptedResponseResult = Assert.IsType<string>(okResult.Value);
        Assert.Equal(encryptedResponse, encryptedResponseResult);

        // Verify all services were called
        _mockEncryptedXmlService.Verify(x => x.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedPayload), Times.Once);
        _mockEncryptedXmlService.Verify(x => x.SerializeAndEncryptAsync(expectedXmlResponse), Times.Once);
        _mockValidationService.Verify(x => x.ValidateAsync(xmlRequest), Times.Once);
        _mockPaymentGateway.Verify(x => x.ProcessNameEnquiryAsync(It.IsAny<PaymentNameEnquiryCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockResponseFactory.Verify(x => x.CreateNameEnquirySuccessResponse(xmlRequest, expectedResult), Times.Once);
    }

    [Fact]
    public async Task NameEnquiry_WithInvalidXmlRequest_ReturnsValidationErrorResponse()
    {
        // Arrange
        var encryptedPayload = "encrypted-invalid-payload";
        var xmlRequest = new NameEnquiryXmlRequest
        {
            SessionId = "SESSION*********",
            DestinationInstitutionCode = "011",
            ChannelCode = "BELEMA_VA",
            AccountNumber = "*********" // Invalid: only 9 digits
        };

        var validationErrors = new[] { "AccountNumber must be exactly 10 digits", "AccountNumber must contain only digits" };
        var validationResult = new ValidationResult { IsValid = false, Errors = validationErrors };

        var expectedErrorResponse = new NameEnquiryXmlResponse
        {
            SessionId = xmlRequest.SessionId,
            DestinationInstitutionCode = xmlRequest.DestinationInstitutionCode,
            ChannelCode = xmlRequest.ChannelCode,
            AccountNumber = xmlRequest.AccountNumber,
            AccountName = "",
            BankVerificationNumber = "",
            KycLevel = "",
            ResponseCode = "01"
        };

        var encryptedErrorResponse = "encrypted-error-response";

        // Setup encrypted XML service
        _mockEncryptedXmlService.Setup(x => x.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedPayload))
            .ReturnsAsync(xmlRequest);
        _mockEncryptedXmlService.Setup(x => x.CreateEncryptedErrorResponseAsync(expectedErrorResponse))
            .ReturnsAsync(encryptedErrorResponse);

        // Setup validation to fail
        _mockValidationService.Setup(x => x.ValidateAsync(xmlRequest))
            .ReturnsAsync(validationResult);

        // Setup response factory for validation error
        _mockResponseFactory.Setup(x => x.CreateNameEnquiryValidationErrorResponse(xmlRequest))
            .Returns(expectedErrorResponse);

        // Act
        var result = await _controller.NameEnquiry(encryptedPayload, CancellationToken.None);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        var encryptedResponseResult = Assert.IsType<string>(badRequestResult.Value);
        Assert.Equal(encryptedErrorResponse, encryptedResponseResult);

        // Verify all services were called correctly
        _mockEncryptedXmlService.Verify(x => x.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedPayload), Times.Once);
        _mockEncryptedXmlService.Verify(x => x.CreateEncryptedErrorResponseAsync(expectedErrorResponse), Times.Once);
        _mockValidationService.Verify(x => x.ValidateAsync(xmlRequest), Times.Once);
        _mockPaymentGateway.Verify(x => x.ProcessNameEnquiryAsync(It.IsAny<PaymentNameEnquiryCommand>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockResponseFactory.Verify(x => x.CreateNameEnquiryValidationErrorResponse(xmlRequest), Times.Once);
    }

    [Fact]
    public async Task NameEnquiry_WhenBusinessLogicFailed_ReturnsBadRequestWithErrorXmlResponse()
    {
        // Arrange
        var encryptedPayload = "encrypted-business-logic-fail-payload";
        var xmlRequest = new NameEnquiryXmlRequest
        {
            SessionId = "SESSION*********",
            DestinationInstitutionCode = "011",
            ChannelCode = "BELEMA_VA",
            AccountNumber = "**********"
        };

        var expectedResult = new PaymentNameEnquiryResultDto
        {
            AccountName = "",
            IsSuccessful = false,
            ErrorMessage = "Account not found",
            Provider = "EasyPay"
        };

        var expectedErrorResponse = new NameEnquiryXmlResponse
        {
            SessionId = xmlRequest.SessionId,
            DestinationInstitutionCode = xmlRequest.DestinationInstitutionCode,
            ChannelCode = xmlRequest.ChannelCode,
            AccountNumber = xmlRequest.AccountNumber,
            AccountName = "",
            BankVerificationNumber = "***********",
            KycLevel = "3",
            ResponseCode = "01"
        };

        var encryptedErrorResponse = "encrypted-business-logic-error-response";

        // Setup encrypted XML service
        _mockEncryptedXmlService.Setup(x => x.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedPayload))
            .ReturnsAsync(xmlRequest);
        _mockEncryptedXmlService.Setup(x => x.SerializeAndEncryptAsync(expectedErrorResponse))
            .ReturnsAsync(encryptedErrorResponse);

        // Setup validation to pass
        _mockValidationService.Setup(x => x.ValidateAsync(xmlRequest))
            .ReturnsAsync(new ValidationResult { IsValid = true, Errors = Enumerable.Empty<string>() });

        // Setup payment gateway to return failure
        _mockPaymentGateway.Setup(x => x.ProcessNameEnquiryAsync(It.IsAny<PaymentNameEnquiryCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Setup response factory
        _mockResponseFactory.Setup(x => x.CreateNameEnquirySuccessResponse(xmlRequest, expectedResult))
            .Returns(expectedErrorResponse);

        // Act
        var result = await _controller.NameEnquiry(encryptedPayload, CancellationToken.None);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        var encryptedResponseResult = Assert.IsType<string>(badRequestResult.Value);
        Assert.Equal(encryptedErrorResponse, encryptedResponseResult);

        // Verify all services were called
        _mockEncryptedXmlService.Verify(x => x.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedPayload), Times.Once);
        _mockEncryptedXmlService.Verify(x => x.SerializeAndEncryptAsync(expectedErrorResponse), Times.Once);
        _mockValidationService.Verify(x => x.ValidateAsync(xmlRequest), Times.Once);
        _mockPaymentGateway.Verify(x => x.ProcessNameEnquiryAsync(It.IsAny<PaymentNameEnquiryCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockResponseFactory.Verify(x => x.CreateNameEnquirySuccessResponse(xmlRequest, expectedResult), Times.Once);
    }

    [Fact]
    public async Task Transfer_WithValidEncryptedXmlRequest_ReturnsEncryptedXmlResponse()
    {
        // Arrange
        var encryptedPayload = "encrypted-transfer-payload";
        var xmlRequest = new TransferXmlRequest
        {
            SessionId = "SESSION*********",
            NameEnquiryRef = "NE*********",
            DestinationInstitutionCode = "011",
            ChannelCode = 1,
            BeneficiaryAccountName = "JANE DOE SMITH",
            BeneficiaryAccountNumber = "**********",
            BeneficiaryBankVerificationNumber = "***********",
            BeneficiaryKycLevel = "3",
            OriginatorAccountName = "JOHN DOE SMITH",
            OriginatorAccountNumber = "*********0",
            OriginatorBankVerificationNumber = "***********",
            OriginatorKycLevel = "3",
            TransactionLocation = "LAGOS",
            Narration = "Payment for services",
            PaymentReference = "TRF-REF-*********",
            Amount = "5000.00"
        };

        var expectedResult = new PaymentTransferResultDto
        {
            IsSuccessful = true,
            TransactionId = "TXN-*********",
            SessionId = "SESSION*********",
            ErrorMessage = "",
            Provider = "EasyPay"
        };

        var expectedXmlResponse = new TransferXmlResponse
        {
            SessionId = xmlRequest.SessionId,
            NameEnquiryRef = xmlRequest.NameEnquiryRef,
            DestinationInstitutionCode = xmlRequest.DestinationInstitutionCode,
            ChannelCode = xmlRequest.ChannelCode,
            BeneficiaryAccountName = xmlRequest.BeneficiaryAccountName,
            BeneficiaryAccountNumber = xmlRequest.BeneficiaryAccountNumber,
            Amount = xmlRequest.Amount,
            ResponseCode = "00",
            ResponseMessage = "SUCCESS",
            TransactionReference = expectedResult.TransactionId
        };

        var encryptedResponse = "encrypted-transfer-response";

        // Setup encrypted XML service
        _mockEncryptedXmlService.Setup(x => x.DecryptAndDeserializeAsync<TransferXmlRequest>(encryptedPayload))
            .ReturnsAsync(xmlRequest);
        _mockEncryptedXmlService.Setup(x => x.SerializeAndEncryptAsync(expectedXmlResponse))
            .ReturnsAsync(encryptedResponse);

        // Setup validation to pass
        _mockValidationService.Setup(x => x.ValidateAsync(xmlRequest))
            .ReturnsAsync(new ValidationResult { IsValid = true, Errors = Enumerable.Empty<string>() });

        // Setup payment gateway
        _mockPaymentGateway.Setup(x => x.ProcessTransferAsync(It.IsAny<PaymentTransferDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Setup response factory
        _mockResponseFactory.Setup(x => x.CreateTransferSuccessResponse(xmlRequest, expectedResult))
            .Returns(expectedXmlResponse);

        // Act
        var result = await _controller.Transfer(encryptedPayload, CancellationToken.None);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var encryptedResponseResult = Assert.IsType<string>(okResult.Value);
        Assert.Equal(encryptedResponse, encryptedResponseResult);

        // Verify all services were called
        _mockEncryptedXmlService.Verify(x => x.DecryptAndDeserializeAsync<TransferXmlRequest>(encryptedPayload), Times.Once);
        _mockEncryptedXmlService.Verify(x => x.SerializeAndEncryptAsync(expectedXmlResponse), Times.Once);
        _mockValidationService.Verify(x => x.ValidateAsync(xmlRequest), Times.Once);
        _mockPaymentGateway.Verify(x => x.ProcessTransferAsync(It.IsAny<PaymentTransferDto>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockResponseFactory.Verify(x => x.CreateTransferSuccessResponse(xmlRequest, expectedResult), Times.Once);
    }

    [Fact]
    public async Task Transfer_WithInvalidXmlRequest_ReturnsValidationErrorResponse()
    {
        // Arrange
        var encryptedPayload = "encrypted-invalid-transfer-payload";
        var xmlRequest = new TransferXmlRequest
        {
            SessionId = "SESSION*********",
            Amount = "invalid_amount", // Invalid amount format
            BeneficiaryAccountNumber = "123" // Invalid: too short
        };

        var validationErrors = new[] { "Amount must be a valid decimal number", "BeneficiaryAccountNumber must be exactly 10 digits" };
        var validationResult = new ValidationResult { IsValid = false, Errors = validationErrors };

        var expectedErrorResponse = new TransferXmlResponse
        {
            SessionId = xmlRequest.SessionId,
            Amount = xmlRequest.Amount,
            BeneficiaryAccountNumber = xmlRequest.BeneficiaryAccountNumber,
            ResponseCode = "01",
            ResponseMessage = "VALIDATION_FAILED",
            TransactionReference = ""
        };

        var encryptedErrorResponse = "encrypted-validation-error-response";

        // Setup encrypted XML service
        _mockEncryptedXmlService.Setup(x => x.DecryptAndDeserializeAsync<TransferXmlRequest>(encryptedPayload))
            .ReturnsAsync(xmlRequest);
        _mockEncryptedXmlService.Setup(x => x.CreateEncryptedErrorResponseAsync(expectedErrorResponse))
            .ReturnsAsync(encryptedErrorResponse);

        // Setup validation to fail
        _mockValidationService.Setup(x => x.ValidateAsync(xmlRequest))
            .ReturnsAsync(validationResult);

        // Setup response factory for validation error
        _mockResponseFactory.Setup(x => x.CreateTransferValidationErrorResponse(xmlRequest))
            .Returns(expectedErrorResponse);

        // Act
        var result = await _controller.Transfer(encryptedPayload, CancellationToken.None);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        var encryptedResponse = Assert.IsType<string>(badRequestResult.Value);
        Assert.Equal(encryptedErrorResponse, encryptedResponse);

        // Verify validation was called but payment gateway was not
        _mockValidationService.Verify(x => x.ValidateAsync(xmlRequest), Times.Once);
        _mockPaymentGateway.Verify(x => x.ProcessTransferAsync(It.IsAny<PaymentTransferDto>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockResponseFactory.Verify(x => x.CreateTransferValidationErrorResponse(xmlRequest), Times.Once);
    }

    [Fact]
    public async Task Transfer_WhenBusinessLogicFailed_ReturnsBadRequestWithErrorXmlResponse()
    {
        // Arrange
        var encryptedPayload = "encrypted-business-logic-fail-transfer-payload";
        var xmlRequest = new TransferXmlRequest
        {
            SessionId = "SESSION*********",
            NameEnquiryRef = "NE*********",
            DestinationInstitutionCode = "011",
            ChannelCode = 1,
            BeneficiaryAccountName = "JANE DOE SMITH",
            BeneficiaryAccountNumber = "**********",
            BeneficiaryBankVerificationNumber = "***********",
            BeneficiaryKycLevel = "3",
            OriginatorAccountName = "JOHN DOE SMITH",
            OriginatorAccountNumber = "*********0",
            OriginatorBankVerificationNumber = "***********",
            OriginatorKycLevel = "3",
            TransactionLocation = "LAGOS",
            Narration = "Payment for services",
            PaymentReference = "TRF-REF-*********",
            Amount = "5000.00"
        };

        var expectedResult = new PaymentTransferResultDto
        {
            IsSuccessful = false,
            TransactionId = "",
            SessionId = "",
            ErrorMessage = "Transfer failed",
            Provider = "EasyPay"
        };

        var expectedErrorResponse = new TransferXmlResponse
        {
            SessionId = xmlRequest.SessionId,
            NameEnquiryRef = xmlRequest.NameEnquiryRef,
            Amount = xmlRequest.Amount,
            ResponseCode = "01",
            ResponseMessage = "FAILED",
            TransactionReference = ""
        };

        var encryptedErrorResponse = "encrypted-business-logic-error-response";

        // Setup encrypted XML service
        _mockEncryptedXmlService.Setup(x => x.DecryptAndDeserializeAsync<TransferXmlRequest>(encryptedPayload))
            .ReturnsAsync(xmlRequest);
        _mockEncryptedXmlService.Setup(x => x.SerializeAndEncryptAsync(expectedErrorResponse))
            .ReturnsAsync(encryptedErrorResponse);

        // Setup validation to pass
        _mockValidationService.Setup(x => x.ValidateAsync(xmlRequest))
            .ReturnsAsync(new ValidationResult { IsValid = true, Errors = Enumerable.Empty<string>() });

        // Setup payment gateway to return failure
        _mockPaymentGateway.Setup(x => x.ProcessTransferAsync(It.IsAny<PaymentTransferDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Setup response factory
        _mockResponseFactory.Setup(x => x.CreateTransferSuccessResponse(xmlRequest, expectedResult))
            .Returns(expectedErrorResponse);

        // Act
        var result = await _controller.Transfer(encryptedPayload, CancellationToken.None);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        var encryptedResponse = Assert.IsType<string>(badRequestResult.Value);
        Assert.Equal(encryptedErrorResponse, encryptedResponse);

        // Verify all services were called
        _mockValidationService.Verify(x => x.ValidateAsync(xmlRequest), Times.Once);
        _mockPaymentGateway.Verify(x => x.ProcessTransferAsync(It.IsAny<PaymentTransferDto>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockResponseFactory.Verify(x => x.CreateTransferSuccessResponse(xmlRequest, expectedResult), Times.Once);
    }

    [Fact]
    public async Task NameEnquiry_WhenExceptionThrown_ReturnsBadRequestWithErrorXml()
    {
        // Arrange
        var encryptedPayload = "encrypted-exception-name-enquiry-payload";
        var xmlRequest = new NameEnquiryXmlRequest
        {
            SessionId = "SESSION*********",
            DestinationInstitutionCode = "011",
            ChannelCode = "BELEMA_VA",
            AccountNumber = "**********"
        };

        var expectedErrorResponse = new NameEnquiryXmlResponse
        {
            SessionId = xmlRequest.SessionId,
            ResponseCode = "99"
        };

        // Setup encrypted XML service
        _mockEncryptedXmlService.Setup(x => x.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedPayload))
            .ReturnsAsync(xmlRequest);

        // Setup validation to pass
        _mockValidationService.Setup(x => x.ValidateAsync(xmlRequest))
            .ReturnsAsync(new ValidationResult { IsValid = true, Errors = Enumerable.Empty<string>() });

        // Setup payment gateway to throw exception
        _mockPaymentGateway.Setup(x => x.ProcessNameEnquiryAsync(It.IsAny<PaymentNameEnquiryCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Internal error"));

        // Act
        var result = await _controller.NameEnquiry(encryptedPayload, CancellationToken.None);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        var errorMessage = Assert.IsType<string>(badRequestResult.Value);
        Assert.Equal("Name enquiry processing failed", errorMessage);

        // Verify services were called appropriately
        _mockEncryptedXmlService.Verify(x => x.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedPayload), Times.Once);
        _mockValidationService.Verify(x => x.ValidateAsync(xmlRequest), Times.Once);
        _mockPaymentGateway.Verify(x => x.ProcessNameEnquiryAsync(It.IsAny<PaymentNameEnquiryCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        // Note: Response factory is not called when exception is thrown - controller returns simple error message
    }

    [Fact]
    public async Task Transfer_WhenExceptionThrown_ReturnsBadRequestWithErrorXml()
    {
        // Arrange
        var encryptedPayload = "encrypted-exception-transfer-payload";
        var xmlRequest = new TransferXmlRequest
        {
            SessionId = "SESSION*********",
            NameEnquiryRef = "NE*********",
            DestinationInstitutionCode = "011",
            ChannelCode = 1,
            BeneficiaryAccountName = "JANE DOE SMITH",
            BeneficiaryAccountNumber = "**********",
            BeneficiaryBankVerificationNumber = "***********",
            BeneficiaryKycLevel = "3",
            OriginatorAccountName = "JOHN DOE SMITH",
            OriginatorAccountNumber = "*********0",
            OriginatorBankVerificationNumber = "***********",
            OriginatorKycLevel = "3",
            TransactionLocation = "LAGOS",
            Narration = "Payment for services",
            PaymentReference = "TRF-REF-*********",
            Amount = "5000.00"
        };

        var expectedErrorResponse = new TransferXmlResponse
        {
            SessionId = xmlRequest.SessionId,
            ResponseCode = "99",
            ResponseMessage = "Internal server error"
        };

        // Setup encrypted XML service
        _mockEncryptedXmlService.Setup(x => x.DecryptAndDeserializeAsync<TransferXmlRequest>(encryptedPayload))
            .ReturnsAsync(xmlRequest);

        // Setup validation to pass
        _mockValidationService.Setup(x => x.ValidateAsync(xmlRequest))
            .ReturnsAsync(new ValidationResult { IsValid = true, Errors = Enumerable.Empty<string>() });

        // Setup payment gateway to throw exception
        _mockPaymentGateway.Setup(x => x.ProcessTransferAsync(It.IsAny<PaymentTransferDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Internal error"));

        // Act
        var result = await _controller.Transfer(encryptedPayload, CancellationToken.None);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        var errorMessage = Assert.IsType<string>(badRequestResult.Value);
        Assert.Equal("Transfer processing failed", errorMessage);

        // Verify services were called appropriately
        _mockEncryptedXmlService.Verify(x => x.DecryptAndDeserializeAsync<TransferXmlRequest>(encryptedPayload), Times.Once);
        _mockValidationService.Verify(x => x.ValidateAsync(xmlRequest), Times.Once);
        _mockPaymentGateway.Verify(x => x.ProcessTransferAsync(It.IsAny<PaymentTransferDto>(), It.IsAny<CancellationToken>()), Times.Once);
        // Note: Response factory is not called when exception is thrown - controller returns simple error message
    }
}
