using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using TransferService.Domain.Exceptions;
using TransferService.Infrastructure.ExternalServices.EasyPay;
using TransferService.Infrastructure;
using TransferService.Infrastructure.Options;
using Xunit;

namespace TransferService.Tests.Infrastructure.EasyPay;

public class EasyPayTokenServiceTests
{
    private readonly Mock<IEasyPayTokenStore> _mockTokenStore;
    private readonly Mock<IEasyPayTokenClient> _mockTokenClient;
    private readonly Mock<ILogger<EasyPayTokenService>> _mockLogger;
    private readonly IOptions<ExternalApiOptions> _options;
    private readonly EasyPayTokenService _tokenService;

    public EasyPayTokenServiceTests()
    {
        _mockTokenStore = new Mock<IEasyPayTokenStore>();
        _mockTokenClient = new Mock<IEasyPayTokenClient>();
        _mockLogger = new Mock<ILogger<EasyPayTokenService>>();

        var easyPayOptions = new EasyPayOptions
        {
            ClientId = "test-client-id",
            ClientSecret = "test-client-secret",
            ApiKey = "test-api-key",
            TokenResetUrl = "https://test-token-url.com/oauth2/token",
            TimeoutSeconds = 30
        };

        var externalApiOptions = new ExternalApiOptions
        {
            EasyPay = easyPayOptions
        };

        _options = Options.Create(externalApiOptions);
        _tokenService = new EasyPayTokenService(_mockTokenStore.Object, _mockTokenClient.Object, _options, _mockLogger.Object);
    }

    [Fact]
    public async Task GetAccessTokenAsync_WhenCachedTokenExists_ReturnsCachedToken()
    {
        // Arrange
        var cachedToken = "cached-access-token";
        _mockTokenStore.Setup(x => x.GetToken()).Returns(cachedToken);

        // Act
        var result = await _tokenService.GetAccessTokenAsync();

        // Assert
        Assert.Equal(cachedToken, result);
        _mockTokenClient.Verify(x => x.GetTokenAsync(It.IsAny<EasyPayTokenFormRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetAccessTokenAsync_WhenNoCachedToken_RefreshesToken()
    {
        // Arrange
        var newToken = "new-access-token";
        _mockTokenStore.Setup(x => x.GetToken()).Returns((string?)null);
        
        var tokenResponse = new EasyPayTokenApiResponse
        {
            AccessToken = newToken,
            ExpiresIn = 3600,
            TokenType = "Bearer"
        };

        _mockTokenClient.Setup(x => x.GetTokenAsync(It.IsAny<EasyPayTokenFormRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(tokenResponse);

        // Act
        var result = await _tokenService.GetAccessTokenAsync();

        // Assert
        Assert.Equal(newToken, result);
        _mockTokenStore.Verify(x => x.SetToken(newToken, 3600), Times.Once);
    }

    [Fact]
    public async Task RefreshTokenAsync_WhenApiCallSucceeds_ReturnsNewToken()
    {
        // Arrange
        var newToken = "refreshed-access-token";
        var tokenResponse = new EasyPayTokenApiResponse
        {
            AccessToken = newToken,
            ExpiresIn = 3600,
            TokenType = "Bearer"
        };

        _mockTokenClient.Setup(x => x.GetTokenAsync(It.IsAny<EasyPayTokenFormRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(tokenResponse);

        // Act
        var result = await _tokenService.RefreshTokenAsync();

        // Assert
        Assert.Equal(newToken, result);
        _mockTokenStore.Verify(x => x.SetToken(newToken, 3600), Times.Once);
    }

    [Fact]
    public async Task RefreshTokenAsync_WhenApiCallFails_ThrowsEasyPayAuthenticationException()
    {
        // Arrange
        _mockTokenClient.Setup(x => x.GetTokenAsync(It.IsAny<EasyPayTokenFormRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new HttpRequestException("Unauthorized"));

        // Act & Assert
        await Assert.ThrowsAsync<EasyPayAuthenticationException>(() => _tokenService.RefreshTokenAsync());
    }

    [Fact]
    public void ClearToken_CallsTokenStoreClearToken()
    {
        // Act
        _tokenService.ClearToken();

        // Assert
        _mockTokenStore.Verify(x => x.ClearToken(), Times.Once);
    }

    [Fact]
    public void Constructor_WithInvalidConfiguration_ThrowsEasyPayConfigurationException()
    {
        // Arrange
        var invalidOptions = Options.Create(new ExternalApiOptions
        {
            EasyPay = new EasyPayOptions() // Missing required fields
        });

        // Act & Assert
        Assert.Throws<EasyPayConfigurationException>(() => 
            new EasyPayTokenService(_mockTokenStore.Object, _mockTokenClient.Object, invalidOptions, _mockLogger.Object));
    }
}
