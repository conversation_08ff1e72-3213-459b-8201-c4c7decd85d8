using System.Globalization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Features.EasyPay.Commands;
using TransferService.Application.Interfaces;
using TransferService.Domain.Exceptions;
using TransferService.Infrastructure.ExternalServices.EasyPay;
using TransferService.Infrastructure;
using TransferService.Infrastructure.Options;
using Xunit;

namespace TransferService.Tests.Infrastructure.EasyPay;

public class EasyPayTransferServiceTests
{
    private readonly Mock<IEasyPayAuthenticatedClient> _mockApiClient;
    private readonly Mock<ILogger<EasyPayTransferService>> _mockLogger;
    private readonly IOptions<ExternalApiOptions> _options;
    private readonly EasyPayTransferService _transferService;

    public EasyPayTransferServiceTests()
    {
        _mockApiClient = new Mock<IEasyPayAuthenticatedClient>();
        _mockLogger = new Mock<ILogger<EasyPayTransferService>>();

        var easyPayOptions = new EasyPayOptions
        {
            InstitutionCode = "TEST001",
            OriginatorAccountName = "Test Originator",
            OriginatorAccountNumber = "**********",
            BillerId = "TEST-BILLER",
            MandateRef = "TEST-MANDATE",
            SourceInstitutionCode = "888564"
        };

        var externalApiOptions = new ExternalApiOptions
        {
            EasyPay = easyPayOptions
        };

        _options = Options.Create(externalApiOptions);
        _transferService = new EasyPayTransferService(_mockApiClient.Object, _options, _mockLogger.Object);
    }

    [Fact]
    public async Task NameEnquiryAsync_WhenSuccessful_ReturnsSuccessResult()
    {
        // Arrange
        var accountNumber = "**********";
        var bankCode = "058";
        var expectedAccountName = "John Doe";

        var apiResponse = new EasyPayNameEnquiryResponse
        {
            AccountName = expectedAccountName,
            AccountNumber = accountNumber,
            ResponseCode = "00",
            ResponseMessage = "Success"
        };

        _mockApiClient.Setup(x => x.NameEnquiryAsync(It.IsAny<EasyPayNameEnquiryRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        // Act
        var result = await _transferService.NameEnquiryAsync(accountNumber, bankCode);

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.Equal(expectedAccountName, result.AccountName);
        Assert.Equal(accountNumber, result.AccountNumber);
        Assert.Empty(result.ErrorMessage);
    }

    [Fact]
    public async Task NameEnquiryAsync_WhenFailed_ReturnsFailureResult()
    {
        // Arrange
        var accountNumber = "**********";
        var bankCode = "058";

        var apiResponse = new EasyPayNameEnquiryResponse
        {
            AccountName = "",
            AccountNumber = accountNumber,
            ResponseCode = "01",
            ResponseMessage = "Account not found"
        };

        _mockApiClient.Setup(x => x.NameEnquiryAsync(It.IsAny<EasyPayNameEnquiryRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        // Act
        var result = await _transferService.NameEnquiryAsync(accountNumber, bankCode);

        // Assert
        Assert.False(result.IsSuccessful);
        Assert.Empty(result.AccountName);
        Assert.Equal("Account not found", result.ErrorMessage);
    }

    [Fact]
    public async Task TransferAsync_WhenSuccessful_ReturnsSuccessResult()
    {
        // Arrange
        var amount = 1000m;
        var accountNumber = "**********";
        var bankCode = "058";
        var accountName = "John Doe";
        var narration = "Test transfer";

        var apiResponse = new FundTransferDto
        {
            ResponseCode = "00",
            // ResponseMessage removed as it's not in the updated FundTransferDto
            TransactionId = "TXN123456",
            PaymentReference = "PAY123456",
            Amount = amount,
            BeneficiaryAccountName = accountName,
            BeneficiaryAccountNumber = accountNumber,
            DestinationInstitutionCode = bankCode
        };

        _mockApiClient.Setup(x => x.FundTransferAsync(It.IsAny<EasyPayTransferRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        // Act
        var command = new EasyPayTransferCommand
        {
            Amount = amount,
            AccountNumber = accountNumber,
            BankCode = bankCode,
            AccountName = accountName,
            Narration = narration
        };
        var result = await _transferService.TransferAsync(command);

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.Equal("00", result.Status);
        Assert.True(result.RequiresProcessing);
        Assert.NotNull(result.FullResult);
        Assert.Empty(result.ErrorMessage);
    }

    [Fact]
    public async Task TransferAsync_WhenApiThrowsException_ReturnsFailureResult()
    {
        // Arrange
        var amount = 1000m;
        var accountNumber = "**********";
        var bankCode = "058";
        var accountName = "John Doe";
        var narration = "Test transfer";

        _mockApiClient.Setup(x => x.FundTransferAsync(It.IsAny<EasyPayTransferRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new EasyPayApiException("API Error", "01", "Transfer failed"));

        // Act
        var command = new EasyPayTransferCommand
        {
            Amount = amount,
            AccountNumber = accountNumber,
            BankCode = bankCode,
            AccountName = accountName,
            Narration = narration
        };
        var result = await _transferService.TransferAsync(command);

        // Assert
        Assert.False(result.IsSuccessful);
        Assert.Equal("00", result.Status);
        Assert.Equal("Transfer failed", result.ErrorMessage);
        Assert.NotNull(result.FullResult);
    }

    [Fact]
    public async Task GetWalletBalanceAsync_WhenSuccessful_ReturnsBalance()
    {
        // Arrange
        var expectedBalance = 50000m;
        var apiResponse = new EasyPayBalanceEnquiryResponse
        {
            StatusCode = "00",
            Balance = expectedBalance,
            ResponseMessage = "Success"
        };

        _mockApiClient.Setup(x => x.BalanceEnquiryAsync(It.IsAny<EasyPayBalanceEnquiryRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        // Act
        var result = await _transferService.GetWalletBalanceAsync();

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.Equal(expectedBalance, result.Balance);
        Assert.Equal("00", result.Status);
        Assert.Empty(result.ErrorMessage);
    }

    [Fact]
    public async Task TransactionStatusQueryAsync_WhenSuccessful_ReturnsStatus()
    {
        // Arrange
        var sessionId = "SESSION123";
        var apiResponse = new EasyPayTSQResponse
        {
            StatusCode = "00",
            ResponseMessage = "Transaction successful"
        };

        _mockApiClient.Setup(x => x.TransactionStatusQueryAsync(It.IsAny<EasyPayTSQRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        // Act
        var result = await _transferService.TransactionStatusQueryAsync(sessionId);

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.Equal("00", result.Status);
        Assert.Empty(result.ErrorMessage);
    }

    [Fact]
    public async Task CreditWalletAsync_AlwaysReturnsSuccess()
    {
        // Arrange
        var amount = 10000m;

        // Act
        var result = await _transferService.CreditWalletAsync(amount);

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.Equal("00", result.Status);
        Assert.Empty(result.ErrorMessage);
    }
}
