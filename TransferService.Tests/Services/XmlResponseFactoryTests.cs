using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;
using TransferService.Application.Services;
using Xunit;

namespace TransferService.Tests.Services;

public class XmlResponseFactoryTests
{
    private readonly XmlResponseFactory _responseFactory = new XmlResponseFactory("989898");

    [Fact]
    public void CreateNameEnquiryValidationErrorResponse_ReturnsCorrectErrorResponse()
    {
        // Arrange
        var request = new NameEnquiryXmlRequest
        {
            SessionId = "SESSION*********",
            DestinationInstitutionCode = "011",
            ChannelCode = "BELEMA_VA",
            AccountNumber = "*********" // Invalid
        };

        // Act
        var response = _responseFactory.CreateNameEnquiryValidationErrorResponse(request);

        // Assert
        Assert.Equal(request.SessionId, response.SessionId);
        Assert.Equal(request.DestinationInstitutionCode, response.DestinationInstitutionCode);
        Assert.Equal(request.ChannelCode, response.ChannelCode);
        Assert.Equal(request.AccountNumber, response.AccountNumber);
        Assert.Equal("", response.AccountName);
        Assert.Equal("", response.BankVerificationNumber);
        Assert.Equal("", response.KycLevel);
        Assert.Equal("01", response.ResponseCode);
    }

    [Fact]
    public void CreateNameEnquirySuccessResponse_ReturnsCorrectSuccessResponse()
    {
        // Arrange
        var request = new NameEnquiryXmlRequest
        {
            SessionId = "SESSION*********",
            DestinationInstitutionCode = "011",
            ChannelCode = "BELEMA_VA",
            AccountNumber = "**********"
        };

        var result = new PaymentNameEnquiryResultDto
        {
            AccountName = "JOHN DOE SMITH & COMPANY",
            IsSuccessful = true,
            ErrorMessage = "",
            Provider = "EasyPay"
        };

        // Act
        var response = _responseFactory.CreateNameEnquirySuccessResponse(request, result);

        // Assert
        Assert.Equal(request.SessionId, response.SessionId);
        Assert.Equal(request.DestinationInstitutionCode, response.DestinationInstitutionCode);
        Assert.Equal(request.ChannelCode, response.ChannelCode);
        Assert.Equal(request.AccountNumber, response.AccountNumber);
        Assert.Equal("JOHN DOE SMITH &amp; COMPANY", response.AccountName); // Should be XML escaped
        Assert.Equal("*********01", response.BankVerificationNumber);
        Assert.Equal("3", response.KycLevel);
        Assert.Equal("00", response.ResponseCode);
    }

    [Fact]
    public void CreateNameEnquirySuccessResponse_WhenFailed_ReturnsErrorResponse()
    {
        // Arrange
        var request = new NameEnquiryXmlRequest
        {
            SessionId = "SESSION*********",
            DestinationInstitutionCode = "011",
            ChannelCode = "BELEMA_VA",
            AccountNumber = "**********"
        };

        var result = new PaymentNameEnquiryResultDto
        {
            AccountName = "",
            IsSuccessful = false,
            ErrorMessage = "Account not found",
            Provider = "EasyPay"
        };

        // Act
        var response = _responseFactory.CreateNameEnquirySuccessResponse(request, result);

        // Assert
        Assert.Equal(request.SessionId, response.SessionId);
        Assert.Equal(request.DestinationInstitutionCode, response.DestinationInstitutionCode);
        Assert.Equal(request.ChannelCode, response.ChannelCode);
        Assert.Equal(request.AccountNumber, response.AccountNumber);
        Assert.Equal("", response.AccountName);
        Assert.Equal("*********01", response.BankVerificationNumber);
        Assert.Equal("3", response.KycLevel);
        Assert.Equal("01", response.ResponseCode);
    }

    [Fact]
    public void CreateTransferValidationErrorResponse_ReturnsCorrectErrorResponse()
    {
        // Arrange
        var request = new TransferXmlRequest
        {
            SessionId = "SESSION*********",
            NameEnquiryRef = "NE*********",
            DestinationInstitutionCode = "011",
            ChannelCode = 1,
            BeneficiaryAccountNumber = "123", // Invalid
            Amount = "invalid_amount" // Invalid
        };

        // Act
        var response = _responseFactory.CreateTransferValidationErrorResponse(request);

        // Assert
        Assert.Equal(request.SessionId, response.SessionId);
        Assert.Equal(request.NameEnquiryRef, response.NameEnquiryRef);
        Assert.Equal(request.DestinationInstitutionCode, response.DestinationInstitutionCode);
        Assert.Equal(request.ChannelCode, response.ChannelCode);
        Assert.Equal(request.BeneficiaryAccountNumber, response.BeneficiaryAccountNumber);
        Assert.Equal(request.Amount, response.Amount);
        Assert.Equal("01", response.ResponseCode);
        Assert.Equal("VALIDATION_FAILED", response.ResponseMessage);
        Assert.Equal("", response.TransactionReference);
    }

    [Fact]
    public void CreateTransferSuccessResponse_ReturnsCorrectSuccessResponse()
    {
        // Arrange
        var request = new TransferXmlRequest
        {
            SessionId = "SESSION*********",
            NameEnquiryRef = "NE*********",
            DestinationInstitutionCode = "011",
            ChannelCode = 1,
            BeneficiaryAccountName = "JANE DOE SMITH & COMPANY",
            BeneficiaryAccountNumber = "**********",
            BeneficiaryBankVerificationNumber = "***********",
            BeneficiaryKycLevel = "3",
            OriginatorAccountName = "JOHN DOE SMITH & ASSOCIATES",
            OriginatorAccountNumber = "*********0",
            OriginatorBankVerificationNumber = "*********01",
            OriginatorKycLevel = "3",
            TransactionLocation = "LAGOS",
            Narration = "Payment for services & goods",
            PaymentReference = "TRF-REF-*********",
            Amount = "5000.00"
        };

        var result = new PaymentTransferResultDto
        {
            IsSuccessful = true,
            TransactionId = "TXN-*********",
            SessionId = "SESSION*********",
            ErrorMessage = "",
            Provider = "EasyPay"
        };

        // Act
        var response = _responseFactory.CreateTransferSuccessResponse(request, result);

        // Assert
        Assert.Equal(request.SessionId, response.SessionId);
        Assert.Equal(request.NameEnquiryRef, response.NameEnquiryRef);
        Assert.Equal(request.DestinationInstitutionCode, response.DestinationInstitutionCode);
        Assert.Equal(request.ChannelCode, response.ChannelCode);
        Assert.Equal("JANE DOE SMITH &amp; COMPANY", response.BeneficiaryAccountName); // Should be XML escaped
        Assert.Equal(request.BeneficiaryAccountNumber, response.BeneficiaryAccountNumber);
        Assert.Equal("JOHN DOE SMITH &amp; ASSOCIATES", response.OriginatorAccountName); // Should be XML escaped
        Assert.Equal(request.OriginatorAccountNumber, response.OriginatorAccountNumber);
        Assert.Equal(request.TransactionLocation, response.TransactionLocation);
        Assert.Equal(request.PaymentReference, response.PaymentReference);
        Assert.Equal(request.Amount, response.Amount);
        Assert.Equal("00", response.ResponseCode);
        Assert.Equal("SUCCESS", response.ResponseMessage);
        Assert.Equal(result.TransactionId, response.TransactionReference);
    }

    [Fact]
    public void CreateTransferSuccessResponse_WhenFailed_ReturnsErrorResponse()
    {
        // Arrange
        var request = new TransferXmlRequest
        {
            SessionId = "SESSION*********",
            NameEnquiryRef = "NE*********",
            Amount = "5000.00"
        };

        var result = new PaymentTransferResultDto
        {
            IsSuccessful = false,
            TransactionId = "",
            SessionId = "",
            ErrorMessage = "Transfer failed",
            Provider = "EasyPay"
        };

        // Act
        var response = _responseFactory.CreateTransferSuccessResponse(request, result);

        // Assert
        Assert.Equal(request.SessionId, response.SessionId);
        Assert.Equal(request.NameEnquiryRef, response.NameEnquiryRef);
        Assert.Equal(request.Amount, response.Amount);
        Assert.Equal("01", response.ResponseCode);
        Assert.Equal("Transfer failed", response.ResponseMessage);
        Assert.Equal("", response.TransactionReference);
    }

    [Fact]
    public void CreateNameEnquiryErrorResponse_ReturnsCorrectErrorResponse()
    {
        // Arrange
        var request = new NameEnquiryXmlRequest
        {
            SessionId = "SESSION*********"
        };

        // Act
        var response = _responseFactory.CreateNameEnquiryErrorResponse(request);

        // Assert
        Assert.Equal(request.SessionId, response.SessionId);
        Assert.Equal("99", response.ResponseCode);
    }

    [Fact]
    public void CreateTransferErrorResponse_ReturnsCorrectErrorResponse()
    {
        // Arrange
        var request = new TransferXmlRequest
        {
            SessionId = "SESSION*********"
        };

        // Act
        var response = _responseFactory.CreateTransferErrorResponse(request);

        // Assert
        Assert.Equal(request.SessionId, response.SessionId);
        Assert.Equal("99", response.ResponseCode);
        Assert.Equal("Internal server error", response.ResponseMessage);
    }
}
