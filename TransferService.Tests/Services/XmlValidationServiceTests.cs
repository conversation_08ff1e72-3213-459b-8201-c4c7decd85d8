using FluentValidation;
using Moq;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Services;
using TransferService.Application.Services.Interfaces;
using Xunit;

namespace TransferService.Tests.Services;

public class XmlValidationServiceTests
{
    private readonly Mock<IValidator<NameEnquiryXmlRequest>> _mockNameEnquiryValidator;
    private readonly Mock<IValidator<TransferXmlRequest>> _mockTransferValidator;
    private readonly XmlValidationService _validationService;

    public XmlValidationServiceTests()
    {
        _mockNameEnquiryValidator = new Mock<IValidator<NameEnquiryXmlRequest>>();
        _mockTransferValidator = new Mock<IValidator<TransferXmlRequest>>();
        _validationService = new XmlValidationService(_mockNameEnquiryValidator.Object, _mockTransferValidator.Object);
    }

    [Fact]
    public async Task ValidateAsync_NameEnquiryRequest_WhenValid_ReturnsValidResult()
    {
        // Arrange
        var request = new NameEnquiryXmlRequest
        {
            SessionId = "SESSION*********",
            DestinationInstitutionCode = "011",
            ChannelCode = "BELEMA_VA",
            AccountNumber = "0*********"
        };

        var fluentValidationResult = new FluentValidation.Results.ValidationResult();

        _mockNameEnquiryValidator.Setup(x => x.ValidateAsync(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(fluentValidationResult);

        // Act
        var result = await _validationService.ValidateAsync(request);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
        _mockNameEnquiryValidator.Verify(x => x.ValidateAsync(request, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ValidateAsync_NameEnquiryRequest_WhenInvalid_ReturnsInvalidResult()
    {
        // Arrange
        var request = new NameEnquiryXmlRequest
        {
            SessionId = "",
            DestinationInstitutionCode = "011",
            ChannelCode = "BELEMA_VA",
            AccountNumber = "*********" // Invalid: 9 digits
        };

        var fluentValidationResult = new FluentValidation.Results.ValidationResult();
        fluentValidationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("SessionID", "SessionID is required"));
        fluentValidationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("AccountNumber", "AccountNumber must be exactly 10 digits"));

        _mockNameEnquiryValidator.Setup(x => x.ValidateAsync(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(fluentValidationResult);

        // Act
        var result = await _validationService.ValidateAsync(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Equal(2, result.Errors.Count());
        Assert.Contains("SessionID is required", result.Errors);
        Assert.Contains("AccountNumber must be exactly 10 digits", result.Errors);
        _mockNameEnquiryValidator.Verify(x => x.ValidateAsync(request, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ValidateAsync_TransferRequest_WhenValid_ReturnsValidResult()
    {
        // Arrange
        var request = new TransferXmlRequest
        {
            SessionId = "SESSION*********",
            NameEnquiryRef = "NE*********",
            DestinationInstitutionCode = "011",
            ChannelCode = 1,
            BeneficiaryAccountName = "JANE DOE SMITH",
            BeneficiaryAccountNumber = "**********",
            BeneficiaryKycLevel = "3",
            OriginatorAccountName = "JOHN DOE SMITH",
            OriginatorAccountNumber = "*********0",
            OriginatorKycLevel = "3",
            TransactionLocation = "LAGOS",
            Narration = "Payment for services",
            PaymentReference = "TRF-REF-*********",
            Amount = "5000.00"
        };

        var fluentValidationResult = new FluentValidation.Results.ValidationResult();

        _mockTransferValidator.Setup(x => x.ValidateAsync(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(fluentValidationResult);

        // Act
        var result = await _validationService.ValidateAsync(request);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
        _mockTransferValidator.Verify(x => x.ValidateAsync(request, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ValidateAsync_TransferRequest_WhenInvalid_ReturnsInvalidResult()
    {
        // Arrange
        var request = new TransferXmlRequest
        {
            SessionId = "",
            Amount = "invalid_amount",
            BeneficiaryAccountNumber = "123" // Invalid: too short
        };

        var fluentValidationResult = new FluentValidation.Results.ValidationResult();
        fluentValidationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("SessionID", "SessionID is required"));
        fluentValidationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Amount", "Amount must be a valid decimal number"));
        fluentValidationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("BeneficiaryAccountNumber", "BeneficiaryAccountNumber must be exactly 10 digits"));

        _mockTransferValidator.Setup(x => x.ValidateAsync(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(fluentValidationResult);

        // Act
        var result = await _validationService.ValidateAsync(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Equal(3, result.Errors.Count());
        Assert.Contains("SessionID is required", result.Errors);
        Assert.Contains("Amount must be a valid decimal number", result.Errors);
        Assert.Contains("BeneficiaryAccountNumber must be exactly 10 digits", result.Errors);
        _mockTransferValidator.Verify(x => x.ValidateAsync(request, It.IsAny<CancellationToken>()), Times.Once);
    }
}
