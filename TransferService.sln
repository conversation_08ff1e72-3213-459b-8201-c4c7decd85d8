﻿
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TransferService", "TransferService\TransferService.csproj", "{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TransferService.Domain", "TransferService.Domain\TransferService.Domain.csproj", "{D1234567-1234-1234-1234-123456789012}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TransferService.Application", "TransferService.Application\TransferService.Application.csproj", "{D2234567-1234-1234-1234-123456789012}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TransferService.Infrastructure", "TransferService.Infrastructure\TransferService.Infrastructure.csproj", "{D3234567-1234-1234-1234-123456789012}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TransferService.Tests", "TransferService.Tests\TransferService.Tests.csproj", "{18D142BD-97DE-415E-8891-901EAC3C7DBC}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Debug|x64.Build.0 = Debug|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Debug|x86.Build.0 = Debug|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Release|x64.ActiveCfg = Release|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Release|x64.Build.0 = Release|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Release|x86.ActiveCfg = Release|Any CPU
		{C4FBED9A-C40A-48C8-803F-FAEBDF27C2AF}.Release|x86.Build.0 = Release|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Debug|x64.Build.0 = Debug|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Debug|x86.Build.0 = Debug|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Release|x64.ActiveCfg = Release|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Release|x64.Build.0 = Release|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Release|x86.ActiveCfg = Release|Any CPU
		{D1234567-1234-1234-1234-123456789012}.Release|x86.Build.0 = Release|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Debug|x64.Build.0 = Debug|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Debug|x86.Build.0 = Debug|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Release|x64.ActiveCfg = Release|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Release|x64.Build.0 = Release|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Release|x86.ActiveCfg = Release|Any CPU
		{D2234567-1234-1234-1234-123456789012}.Release|x86.Build.0 = Release|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Debug|x64.Build.0 = Debug|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Debug|x86.Build.0 = Debug|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Release|x64.ActiveCfg = Release|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Release|x64.Build.0 = Release|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Release|x86.ActiveCfg = Release|Any CPU
		{D3234567-1234-1234-1234-123456789012}.Release|x86.Build.0 = Release|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Debug|x64.Build.0 = Debug|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Debug|x86.Build.0 = Debug|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Release|Any CPU.Build.0 = Release|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Release|x64.ActiveCfg = Release|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Release|x64.Build.0 = Release|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Release|x86.ActiveCfg = Release|Any CPU
		{18D142BD-97DE-415E-8891-901EAC3C7DBC}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
