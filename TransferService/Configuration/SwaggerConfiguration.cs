using Microsoft.OpenApi.Models;
using System.Reflection;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace TransferService.Configuration;

public static class SwaggerConfiguration
{
    public static IServiceCollection AddSwaggerDocumentation(this IServiceCollection services)
    {
        services.AddSwaggerGen(options =>
        {
            // API Information
            options.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "Transfer Service API",
                Version = "v1.0",
                Description = "Enterprise Transfer Service API for processing financial transfers with external API integrations",
                Contact = new OpenApiContact
                {
                    Name = "Transfer Service Team",
                    Email = "<EMAIL>",
                    Url = new Uri("https://transferservice.com/support")
                },
                License = new OpenApiLicense
                {
                    Name = "MIT License",
                    Url = new Uri("https://opensource.org/licenses/MIT")
                }
            });

            // Security Definition
            options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });

            options.AddSecurityDefinition("ApiKey", new OpenApiSecurityScheme
            {
                Description = "API Key needed to access the endpoints. X-API-Key: {key}",
                In = ParameterLocation.Header,
                Name = "X-API-Key",
                Type = SecuritySchemeType.ApiKey
            });

            // Security Requirements
            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    []
                }
            });

            // XML Documentation
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                options.IncludeXmlComments(xmlPath);
            }

            // Include XML comments from other projects
            var applicationXmlFile = "TransferService.Application.xml";
            var applicationXmlPath = Path.Combine(AppContext.BaseDirectory, applicationXmlFile);
            if (File.Exists(applicationXmlPath))
            {
                options.IncludeXmlComments(applicationXmlPath);
            }

            var domainXmlFile = "TransferService.Domain.xml";
            var domainXmlPath = Path.Combine(AppContext.BaseDirectory, domainXmlFile);
            if (File.Exists(domainXmlPath))
            {
                options.IncludeXmlComments(domainXmlPath);
            }

            // Custom Schema IDs
            options.CustomSchemaIds(type => type.FullName?.Replace("+", "."));

            // Operation Filters
            options.OperationFilter<SwaggerDefaultValues>();
            options.OperationFilter<XmlRequestExampleFilter>();
            options.OperationFilter<XmlTransferExampleFilter>();
            options.OperationFilter<PgpTextPlainFilter>();
            options.DocumentFilter<SwaggerDocumentFilter>();
            options.SchemaFilter<XmlSchemaFilter>();

            // Enable annotations (commented out as it requires additional package)
            // options.EnableAnnotations();

            // Configure for different environments
            options.DocInclusionPredicate((docName, description) => true);

            // Custom ordering
            options.OrderActionsBy(apiDesc => $"{apiDesc.ActionDescriptor.RouteValues["controller"]}_{apiDesc.HttpMethod}");
        });

        return services;
    }

    public static IApplicationBuilder UseSwaggerDocumentation(this IApplicationBuilder app, IWebHostEnvironment env)
    {
        app.UseSwagger(options =>
        {
            options.RouteTemplate = "api-docs/{documentName}/swagger.json";
        });

        app.UseSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/api-docs/v1/swagger.json", "Transfer Service API v1.0");
            options.RoutePrefix = "api-docs";
            
            // UI Customization
            options.DocumentTitle = "Transfer Service API Documentation";
            options.DefaultModelsExpandDepth(2);
            options.DefaultModelExpandDepth(2);
            options.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
            options.EnableDeepLinking();
            options.DisplayOperationId();
            options.DisplayRequestDuration();
            
            // Custom CSS removed as requested
            
            // Enable OAuth2 if needed
            options.OAuthClientId("transfer-service-swagger");
            options.OAuthAppName("Transfer Service API");
            options.OAuthUsePkce();

            // Only show in development
            if (!env.IsDevelopment()) return;
            options.EnableValidator();
            options.EnableFilter();
            options.ShowExtensions();
        });

        return app;
    }
}

public class SwaggerDefaultValues : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var apiDescription = context.ApiDescription;

        // operation.Deprecated |= apiDescription.IsDeprecated();

        foreach (var responseType in context.ApiDescription.SupportedResponseTypes)
        {
            var responseKey = responseType.IsDefaultResponse ? "default" : responseType.StatusCode.ToString();
            var response = operation.Responses[responseKey];

            foreach (var contentType in response.Content.Keys)
            {
                if (responseType.ApiResponseFormats.All(x => x.MediaType != contentType))
                {
                    response.Content.Remove(contentType);
                }
            }
        }

        if (operation.Parameters == null)
            return;

        foreach (var parameter in operation.Parameters)
        {
            var description = apiDescription.ParameterDescriptions.First(p => p.Name == parameter.Name);
            parameter.Description ??= description.ModelMetadata?.Description;

            if (parameter.Schema.Default == null && description.DefaultValue != null)
            {
                parameter.Schema.Default = new Microsoft.OpenApi.Any.OpenApiString(description.DefaultValue.ToString());
            }

            parameter.Required |= description.IsRequired;
        }
    }
}

public class XmlRequestExampleFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Check if this is the XML transfer endpoint
        if (context.MethodInfo.Name == "ProcessXmlTransfer" &&
            context.MethodInfo.DeclaringType?.Name == "XmlTransfersController")
        {
            // Add XML example to request body
            if (operation.RequestBody?.Content?.ContainsKey("application/xml") == true)
            {
                var xmlContent = operation.RequestBody.Content["application/xml"];

                // Set the schema to string type
                xmlContent.Schema = new OpenApiSchema
                {
                    Type = "string",
                    Format = "xml",
                    Example = new Microsoft.OpenApi.Any.OpenApiString(@"<TransferRequest>
    <FromAccount>ACC123</FromAccount>
    <ToAccount>ACC456</ToAccount>
    <Amount>75000.00</Amount>
    <Currency>NGN</Currency>
    <Description>Test transfer</Description>
    <RequestId>test-123</RequestId>
</TransferRequest>")
                };

                // Add examples
                xmlContent.Examples = new Dictionary<string, OpenApiExample>
                {
                    ["example1"] = new OpenApiExample
                    {
                        Summary = "Basic Transfer Request",
                        Description = "A simple transfer request with required fields",
                        Value = new Microsoft.OpenApi.Any.OpenApiString(@"<TransferRequest>
    <FromAccount>ACC123</FromAccount>
                    <ToAccount>ACC456</ToAccount>
    <Amount>75000.00</Amount>
    <Currency>NGN</Currency>
    <Description>Test transfer</Description>
    <RequestId>test-123</RequestId>
</TransferRequest>")
                    },
                    ["example2"] = new OpenApiExample
                    {
                        Summary = "Transfer with Timestamp",
                        Description = "Transfer request including timestamp",
                        Value = new Microsoft.OpenApi.Any.OpenApiString(@"<TransferRequest>
    <FromAccount>ACC789</FromAccount>
    <ToAccount>ACC012</ToAccount>
    <Amount>100000.00</Amount>
    <Currency>NGN</Currency>
    <Description>Monthly salary transfer</Description>
    <RequestId>salary-2025-01</RequestId>
    <Timestamp>2025-07-08T22:00:00Z</Timestamp>
</TransferRequest>")
                    }
                };
            }
        }
    }
}

public class SwaggerDocumentFilter : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        // Remove unwanted schemas
        var schemasToRemove = swaggerDoc.Components.Schemas
            .Where(x => x.Key.Contains("Microsoft.") || x.Key.Contains("System."))
            .ToList();

        foreach (var schema in schemasToRemove)
        {
            swaggerDoc.Components.Schemas.Remove(schema.Key);
        }

        // Add custom examples
        AddCustomExamples(swaggerDoc);
    }

    private void AddCustomExamples(OpenApiDocument swaggerDoc)
    {
        // Add examples for common request/response models
        // This can be expanded based on your specific models
    }
}

/// <summary>
/// Operation filter to enhance text/plain endpoints for PGP operations
/// </summary>
public class PgpTextPlainFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Check if this is a text/plain endpoint
        if (operation.RequestBody?.Content?.ContainsKey("text/plain") == true)
        {
            var textPlainContent = operation.RequestBody.Content["text/plain"];

            // Set schema to string with multiline format
            textPlainContent.Schema = new OpenApiSchema
            {
                Type = "string",
                Format = "text",
                Description = "Enter your text here. For PGP endpoints, paste encrypted PGP messages or XML to encrypt.",
                Example = new Microsoft.OpenApi.Any.OpenApiString("Enter your text here...")
            };

            // Add specific examples based on endpoint
            if (context.MethodInfo.Name.Contains("Encrypt") ||
                context.MethodInfo.Name.Contains("encrypt"))
            {
                textPlainContent.Example = new Microsoft.OpenApi.Any.OpenApiString(@"<?xml version=""1.0"" encoding=""UTF-8""?>
<NESingleRequest>
    <SessionID>00050425071820450**********114</SessionID>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>");
            }
            else if (context.MethodInfo.Name.Contains("Decrypt") ||
                     context.MethodInfo.Name.Contains("decrypt") ||
                     context.MethodInfo.Name.Contains("NameEnquiry") ||
                     context.MethodInfo.Name.Contains("Transfer"))
            {
                textPlainContent.Example = new Microsoft.OpenApi.Any.OpenApiString(@"-----BEGIN PGP MESSAGE-----
Version: BouncyCastle.NET Cryptography (net6.0) v2.4.0+83ebf4a805

hIwDAAIBAw/6gj9XjUIByy1iBG5hbWVog+g4VGhpcyBpcyBhIHRlc3QgbWVzc2Fn
ZSBmb3Igc2lnbmluZy6IvwQAAQIAKQUCaIPoOCIcdHJhbnNmZXJzZXJ2aWNlQGJl
bGVtYWZpbnRlY2guY29tAAoJEAMP+oI/V41CPAQD/RIhPWvHfxNyy6apg7CrNn2Q
k1NL/KA2gXxpfhYjyddDjOkptkS7TUx8wL4u7YrMhTPczsAPBxe8s98CO5VzTLbu
DQW/9QG3aUusdlsXlzQnbzMkdJimyzlHByPGNK6yPrNCZXJxRlIdRKIJ2FZrCL4N
wDVveguvY0wwp8k+L4b1
=A+9R
-----END PGP MESSAGE-----");
            }
        }
    }
}

public class XmlTransferExampleFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Check if this is the XML transfer endpoint
        if (context.MethodInfo.Name == "Transfer" &&
            context.MethodInfo.DeclaringType?.Name == "TransfersController")
        {
            // Add XML example to request body
            if (operation.RequestBody?.Content?.ContainsKey("application/xml") == true)
            {
                var xmlContent = operation.RequestBody.Content["application/xml"];

                // Set proper XML example
                xmlContent.Example = new Microsoft.OpenApi.Any.OpenApiString(@"<?xml version=""1.0"" encoding=""UTF-8""?>
<FTSingleCreditRequest>
    <SessionID>SESSION*********</SessionID>
    <NameEnquiryRef>NE*********</NameEnquiryRef>
    <DestinationInstitutionCode>011</DestinationInstitutionCode>
    <ChannelCode>BELEMA_VA</ChannelCode>
    <BeneficiaryAccountName>JANE DOE SMITH</BeneficiaryAccountName>
    <BeneficiaryAccountNumber>**********</BeneficiaryAccountNumber>
    <BeneficiaryBankVerificationNumber>***********</BeneficiaryBankVerificationNumber>
    <BeneficiaryKYCLevel>3</BeneficiaryKYCLevel>
    <OriginatorAccountName>JOHN DOE SMITH</OriginatorAccountName>
    <OriginatorAccountNumber>**********</OriginatorAccountNumber>
    <OriginatorBankVerificationNumber>**********1</OriginatorBankVerificationNumber>
    <OriginatorKYCLevel>3</OriginatorKYCLevel>
    <TransactionLocation>LAGOS</TransactionLocation>
    <Narration>Payment for services</Narration>
    <PaymentReference>TRF-REF-*********</PaymentReference>
    <Amount>5000.00</Amount>
</FTSingleCreditRequest>");
            }
        }

        // Check if this is the XML name enquiry endpoint
        if (context.MethodInfo.Name == "NameEnquiry" &&
            context.MethodInfo.DeclaringType?.Name == "TransfersController")
        {
            // Add XML example to request body
            if (operation.RequestBody?.Content?.ContainsKey("application/xml") == true)
            {
                var xmlContent = operation.RequestBody.Content["application/xml"];

                // Set proper XML example
                xmlContent.Example = new Microsoft.OpenApi.Any.OpenApiString(@"<?xml version=""1.0"" encoding=""UTF-8""?>
<NESingleRequest>
    <SessionID>SESSION*********</SessionID>
    <DestinationInstitutionCode>011</DestinationInstitutionCode>
    <ChannelCode>BELEMA_VA</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>");
            }
        }
    }
}

public class XmlSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        // Handle XML DTOs to show proper XML structure in Swagger
        if (context.Type.Name == "TransferXmlRequest")
        {
            schema.Example = new Microsoft.OpenApi.Any.OpenApiString(@"<?xml version=""1.0"" encoding=""UTF-8""?>
<FTSingleCreditRequest>
    <SessionID>string</SessionID>
    <NameEnquiryRef>string</NameEnquiryRef>
    <DestinationInstitutionCode>string</DestinationInstitutionCode>
    <ChannelCode>string</ChannelCode>
    <BeneficiaryAccountName>string</BeneficiaryAccountName>
    <BeneficiaryAccountNumber>string</BeneficiaryAccountNumber>
    <BeneficiaryBankVerificationNumber>string</BeneficiaryBankVerificationNumber>
    <BeneficiaryKYCLevel>string</BeneficiaryKYCLevel>
    <OriginatorAccountName>string</OriginatorAccountName>
    <OriginatorAccountNumber>string</OriginatorAccountNumber>
    <OriginatorBankVerificationNumber>string</OriginatorBankVerificationNumber>
    <OriginatorKYCLevel>string</OriginatorKYCLevel>
    <TransactionLocation>string</TransactionLocation>
    <Narration>string</Narration>
    <PaymentReference>string</PaymentReference>
    <Amount>string</Amount>
</FTSingleCreditRequest>");
        }

        if (context.Type.Name == "NameEnquiryXmlRequest")
        {
            schema.Example = new Microsoft.OpenApi.Any.OpenApiString(@"<?xml version=""1.0"" encoding=""UTF-8""?>
<NESingleRequest>
    <SessionID>string</SessionID>
    <DestinationInstitutionCode>string</DestinationInstitutionCode>
    <ChannelCode>string</ChannelCode>
    <AccountNumber>string</AccountNumber>
</NESingleRequest>");
        }
    }
}
