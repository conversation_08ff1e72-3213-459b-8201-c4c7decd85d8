using AutoMapper.Configuration.Annotations;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using TransferService.Application.Features.EasyPay.Commands;
using TransferService.Application.Features.EasyPay.Queries;
using TransferService.Application.Features.Transfers.Models;

namespace TransferService.Controllers;

/// <summary>
/// EasyPay transfer operations controller for NIBSS EasyPay services
/// </summary>
//[ApiExplorerSettings(IgnoreApi = true)]
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class EasyPayController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<EasyPayController> _logger;

    /// <summary>
    /// EasyPay transfer operations controller for NIBSS EasyPay services
    /// </summary>
    public EasyPayController(IMediator mediator, ILogger<EasyPayController> logger)
    {
        _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Performs name enquiry to validate account details
    /// </summary>
    /// <param name="request">Name enquiry request containing account number and bank code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Account validation result with account name if successful</returns>
    /// <response code="200">Name enquiry completed successfully</response>
    /// <response code="400">Invalid request parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("name-enquiry")]
    [ProducesResponseType(typeof(EasyPayNameEnquiryResultDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<EasyPayNameEnquiryResultDto>> NameEnquiry(
        [FromQuery] EasyPayNameEnquiryDto request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Received name enquiry request for account {AccountNumber} at bank {BankCode}", 
            request.AccountNumber, request.BankCode);

        var command = new EasyPayNameEnquiryCommand
        {
            AccountNumber = request.AccountNumber,
            BankCode = request.BankCode
        };

        var result = await _mediator.Send(command, cancellationToken);

        if (result.IsSuccessful)
        {
            return Ok(result);
        }

        // Return 400 Bad Request for failed name enquiry
        return BadRequest(result);
    }

    /// <summary>
    /// Performs fund transfer operation
    /// </summary>
    /// <param name="request">Transfer request containing amount, account details, and narration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Transfer result with transaction details</returns>
    /// <response code="200">Transfer completed successfully</response>
    /// <response code="400">Invalid request parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("transfer")]
    [ProducesResponseType(typeof(EasyPayTransferResultDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<EasyPayTransferResultDto>> Transfer(
        [FromBody] EasyPayTransferDto request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Received transfer request for {Amount} to account {AccountNumber} at bank {BankCode}", 
            request.Amount, request.AccountNumber, request.BankCode);

        var command = new EasyPayTransferCommand
        {
            // Map from comprehensive payload first, then fallback to simple fields
            Amount = request.Amount,
            AccountNumber = !string.IsNullOrEmpty(request.BeneficiaryAccountNumber) ? request.BeneficiaryAccountNumber : request.AccountNumber,
            BankCode = !string.IsNullOrEmpty(request.DestinationInstitutionCode) ? request.DestinationInstitutionCode : request.BankCode,
            AccountName = !string.IsNullOrEmpty(request.BeneficiaryAccountName) ? request.BeneficiaryAccountName : request.AccountName,
            Narration = !string.IsNullOrEmpty(request.OriginatorNarration) ? request.OriginatorNarration :
                       !string.IsNullOrEmpty(request.BeneficiaryNarration) ? request.BeneficiaryNarration : request.Narration,
            OriginatorAccountName = request.OriginatorAccountName,
            OriginatorAccountNumber = request.OriginatorAccountNumber,
            OriginatorBankVerificationNumber = request.OriginatorBankVerificationNumber,
            OriginatorKycLevel = request.OriginatorKYCLevel,
            TransactionLocation = request.TransactionLocation,
            PaymentReference = request.PaymentReference
        };

        var result = await _mediator.Send(command, cancellationToken);

        if (result.IsSuccessful)
        {
            return Ok(result);
        }

        // Return 400 Bad Request for failed transfers (response code not "00")
        return BadRequest(result);
    }

    /// <summary>
    /// Queries wallet balance
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current wallet balance</returns>
    /// <response code="200">Balance query completed successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("wallet/balance")]
    [ProducesResponseType(typeof(EasyPayBalanceDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<EasyPayBalanceDto>> GetWalletBalance(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Received wallet balance query request");

        var query = new EasyPayBalanceQuery();
        var result = await _mediator.Send(query, cancellationToken);

        return Ok(result);
    }

    /// <summary>
    /// Credits wallet with specified amount
    /// </summary>
    /// <param name="request">Credit wallet request containing amount</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Credit operation result</returns>
    /// <response code="200">Wallet credit completed successfully</response>
    /// <response code="400">Invalid request parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("wallet/credit")]
    [ProducesResponseType(typeof(EasyPayCreditWalletResultDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<EasyPayCreditWalletResultDto>> CreditWallet(
        [FromBody] EasyPayCreditWalletDto request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Received wallet credit request for amount {Amount}", request.Amount);

        var command = new EasyPayCreditWalletCommand
        {
            Amount = request.Amount
        };

        var result = await _mediator.Send(command, cancellationToken);

        if (result.IsSuccessful)
        {
            return Ok(result);
        }

        // Return 400 Bad Request for failed wallet credit
        return BadRequest(result);
    }

    /// <summary>
    /// Performs Transaction Status Query (TSQ)
    /// </summary>
    /// <param name="request">TSQ request containing session ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Transaction status</returns>
    /// <response code="200">TSQ completed successfully</response>
    /// <response code="400">Invalid request parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("transaction-status")]
    [ProducesResponseType(typeof(EasyPayTSQResultDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<EasyPayTSQResultDto>> TransactionStatusQuery(
        [FromBody] EasyPayTSQDto request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Received TSQ request for session {SessionId}", request.SessionId);

        var query = new EasyPayTSQQuery
        {
            SessionId = request.SessionId
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccessful)
        {
            return Ok(result);
        }

        // Return 400 Bad Request for failed TSQ
        return BadRequest(result);
    }
}
