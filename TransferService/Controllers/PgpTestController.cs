using Microsoft.AspNetCore.Mvc;
using TransferService.Application.Interfaces;

namespace TransferService.Controllers;

/// <summary>
/// PGP Testing Controller - provides standalone encryption/decryption endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class PgpTestController(IPGPService pgpService, ILogger<PgpTestController> logger) : ControllerBase
{
    /// <summary>
    /// Test PGP encryption endpoint
    /// </summary>
    /// <param name="plainText">Text to encrypt</param>
    /// <returns>Encrypted text</returns>
    [HttpPost("encrypt")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> Encrypt()
    {
        try
        {
            using var reader = new StreamReader(Request.Body);
            var plainText = await reader.ReadToEndAsync();
            
            if (string.IsNullOrEmpty(plainText))
            {
                return BadRequest("Request body cannot be empty");
            }
            
            logger.LogInformation("Encrypting text of length: {Length}", plainText.Length);
            var encryptedText = pgpService.Encrypt(plainText);
            logger.LogInformation("Encryption successful. Encrypted length: {Length}", encryptedText.Length);
            
            return Ok(encryptedText);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Encryption failed");
            return BadRequest($"Encryption failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Test PGP decryption endpoint
    /// </summary>
    /// <param name="encryptedText">Encrypted text to decrypt</param>
    /// <returns>Decrypted text</returns>
    [HttpPost("decrypt")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> Decrypt()
    {
        try
        {
            using var reader = new StreamReader(Request.Body);
            var encryptedText = await reader.ReadToEndAsync();
            
            if (string.IsNullOrEmpty(encryptedText))
            {
                return BadRequest("Request body cannot be empty");
            }
            
            logger.LogInformation("Decrypting text of length: {Length}", encryptedText.Length);
            var decryptedText = pgpService.Decrypt(encryptedText);
            logger.LogInformation("Decryption successful. Decrypted length: {Length}", decryptedText.Length);
            
            return Ok(decryptedText);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Decryption failed");
            return BadRequest($"Decryption failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Test PGP signing endpoint
    /// </summary>
    /// <param name="data">Data to sign</param>
    /// <returns>Signature</returns>
    [HttpPost("sign")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> Sign()
    {
        try
        {
            using var reader = new StreamReader(Request.Body);
            var data = await reader.ReadToEndAsync();
            
            if (string.IsNullOrEmpty(data))
            {
                return BadRequest("Request body cannot be empty");
            }
            
            logger.LogInformation("Signing text of length: {Length}", data.Length);
            var signature = pgpService.Sign(data);
            logger.LogInformation("Signing successful. Signature length: {Length}", signature.Length);
            
            return Ok(signature);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Signing failed");
            return BadRequest($"Signing failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Test PGP signature verification endpoint
    /// </summary>
    /// <param name="request">Data and signature to verify</param>
    /// <returns>Verification result</returns>
    [HttpPost("verify")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public IActionResult Verify([FromBody] VerifySignatureRequest request)
    {
        try
        {
            if (request == null || string.IsNullOrEmpty(request.Data) || string.IsNullOrEmpty(request.Signature))
            {
                return BadRequest("Data and signature are required");
            }
            
            logger.LogInformation("Verifying signature for data of length: {Length}", request.Data.Length);
            var isValid = pgpService.VerifySignature(request.Data, request.Signature);
            logger.LogInformation("Signature verification result: {IsValid}", isValid);
            
            return Ok(new { IsValid = isValid });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Verification failed");
            return BadRequest($"Verification failed: {ex.Message}");
        }
    }
}


