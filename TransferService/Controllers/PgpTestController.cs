using Microsoft.AspNetCore.Mvc;
using TransferService.Application.Interfaces;

namespace TransferService.Controllers;

/// <summary>
/// PGP Testing Controller - provides standalone encryption/decryption endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class PgpTestController(IPGPService pgpService, ILogger<PgpTestController> logger) : ControllerBase
{
    /// <summary>
    /// PGP Encryption - Encrypt XML for use with name-enquiry and transfer endpoints
    /// </summary>
    /// <remarks>
    /// Use this endpoint to encrypt your XML before sending it to the name-enquiry or transfer endpoints.
    ///
    /// **Workflow:**
    /// 1. Paste your XML here to encrypt it
    /// 2. Copy the encrypted result
    /// 3. Use the encrypted result in `/api/BelemaEasyTransfer/name-enquiry` or `/api/BelemaEasyTransfer/transfer`
    ///
    /// **Example XML input:**
    /// ```xml
    /// &lt;?xml version="1.0" encoding="UTF-8"?&gt;
    /// &lt;NESingleRequest&gt;
    ///     &lt;SessionID&gt;000504250718204500123456789114&lt;/SessionID&gt;
    ///     &lt;DestinationInstitutionCode&gt;999998&lt;/DestinationInstitutionCode&gt;
    ///     &lt;ChannelCode&gt;1&lt;/ChannelCode&gt;
    ///     &lt;AccountNumber&gt;**********&lt;/AccountNumber&gt;
    /// &lt;/NESingleRequest&gt;
    /// ```
    /// </remarks>
    [HttpPost("encrypt")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> Encrypt()
    {
        try
        {
            using var reader = new StreamReader(Request.Body);
            var plainText = await reader.ReadToEndAsync();
            
            if (string.IsNullOrEmpty(plainText))
            {
                return BadRequest("Request body cannot be empty");
            }
            
            logger.LogInformation("Encrypting text of length: {Length}", plainText.Length);
            var encryptedText = pgpService.Encrypt(plainText);
            logger.LogInformation("Encryption successful. Encrypted length: {Length}", encryptedText.Length);
            
            return Ok(encryptedText);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Encryption failed");
            return BadRequest($"Encryption failed: {ex.Message}");
        }
    }

    /// <summary>
    /// PGP Decryption - Decrypt responses from name-enquiry and transfer endpoints
    /// </summary>
    /// <remarks>
    /// Use this endpoint to decrypt the encrypted responses you receive from the name-enquiry or transfer endpoints.
    ///
    /// **Workflow:**
    /// 1. Copy the encrypted response from `/api/BelemaEasyTransfer/name-enquiry` or `/api/BelemaEasyTransfer/transfer`
    /// 2. Paste it here to decrypt it
    /// 3. View the decrypted XML response
    ///
    /// **Example encrypted input:**
    /// ```
    /// -----BEGIN PGP MESSAGE-----
    /// Version: BouncyCastle.NET Cryptography (net6.0) v2.4.0+83ebf4a805
    ///
    /// hIwDAAIBAw/6gj9XjUIByy1iBG5hbWVog+g4VGhpcyBpcyBhIHRlc3QgbWVzc2Fn
    /// ZSBmb3Igc2lnbmluZy6IvwQAAQIAKQUCaIPoOCIcdHJhbnNmZXJzZXJ2aWNlQGJl
    /// bGVtYWZpbnRlY2guY29tAAoJEAMP+oI/V41CPAQD/RIhPWvHfxNyy6apg7CrNn2Q
    /// =A+9R
    /// -----END PGP MESSAGE-----
    /// ```
    /// </remarks>
    [HttpPost("decrypt")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> Decrypt()
    {
        try
        {
            using var reader = new StreamReader(Request.Body);
            var encryptedText = await reader.ReadToEndAsync();
            
            if (string.IsNullOrEmpty(encryptedText))
            {
                return BadRequest("Request body cannot be empty");
            }
            
            logger.LogInformation("Decrypting text of length: {Length}", encryptedText.Length);
            var decryptedText = pgpService.Decrypt(encryptedText);
            logger.LogInformation("Decryption successful. Decrypted length: {Length}", decryptedText.Length);
            
            return Ok(decryptedText);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Decryption failed");
            return BadRequest($"Decryption failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Test PGP signing endpoint
    /// </summary>
    /// <param name="data">Data to sign</param>
    /// <returns>Signature</returns>
    [HttpPost("sign")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> Sign()
    {
        try
        {
            using var reader = new StreamReader(Request.Body);
            var data = await reader.ReadToEndAsync();
            
            if (string.IsNullOrEmpty(data))
            {
                return BadRequest("Request body cannot be empty");
            }
            
            logger.LogInformation("Signing text of length: {Length}", data.Length);
            var signature = pgpService.Sign(data);
            logger.LogInformation("Signing successful. Signature length: {Length}", signature.Length);
            
            return Ok(signature);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Signing failed");
            return BadRequest($"Signing failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Test PGP signature verification endpoint
    /// </summary>
    /// <param name="request">Data and signature to verify</param>
    /// <returns>Verification result</returns>
    [HttpPost("verify")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public IActionResult Verify([FromBody] VerifySignatureRequest request)
    {
        try
        {
            if (request == null || string.IsNullOrEmpty(request.Data) || string.IsNullOrEmpty(request.Signature))
            {
                return BadRequest("Data and signature are required");
            }
            
            logger.LogInformation("Verifying signature for data of length: {Length}", request.Data.Length);
            var isValid = pgpService.VerifySignature(request.Data, request.Signature);
            logger.LogInformation("Signature verification result: {IsValid}", isValid);
            
            return Ok(new { IsValid = isValid });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Verification failed");
            return BadRequest($"Verification failed: {ex.Message}");
        }
    }
}


