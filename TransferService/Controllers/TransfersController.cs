using Microsoft.AspNetCore.Mvc;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;
using TransferService.Application.Common.Utilities;

namespace TransferService.Controllers;

/// <summary>
/// Central Payment Gateway Controller - handles XML requests and routes to appropriate payment providers
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/xml")]
public class BelemaEasyTransferController(
    IPaymentGatewayService paymentGateway,
    IXmlValidationService xmlValidationService,
    IXmlResponseFactory xmlResponseFactory,
    ILogger<BelemaEasyTransferController> logger) : ControllerBase
{
    /// <summary>
    /// Central Name Enquiry Gateway - accepts XML and routes to appropriate payment provider
    /// </summary>
    [HttpPost("name-enquiry")]
    [Consumes("application/xml")]
    [Produces("application/xml")]
    public async Task<IActionResult> NameEnquiry([FromBody] NameEnquiryXmlRequest xmlRequest, CancellationToken cancellationToken)
    {
        // Validate XML request
        var validationResult = await xmlValidationService.ValidateAsync(xmlRequest);
        if (!validationResult.IsValid)
        {
            logger.LogWarning("XML validation failed for name enquiry: {Errors}", string.Join(", ", validationResult.Errors));
            return BadRequest(xmlResponseFactory.CreateNameEnquiryValidationErrorResponse(xmlRequest));
        }

        try
        {
            // Process name enquiry
            var command = new PaymentNameEnquiryCommand
            {
                AccountNumber = xmlRequest.AccountNumber,
                BankCode = xmlRequest.DestinationInstitutionCode
            };

            var result = await paymentGateway.ProcessNameEnquiryAsync(command, cancellationToken);
            var response = xmlResponseFactory.CreateNameEnquirySuccessResponse(xmlRequest, result);

            return result.IsSuccessful ? Ok(response) : BadRequest(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing XML name enquiry");
            return BadRequest(xmlResponseFactory.CreateNameEnquiryErrorResponse(xmlRequest));
        }
    }

    /// <summary>
    /// Central Transfer Gateway - accepts XML and routes to appropriate payment provider via load balancing
    /// </summary>
    [HttpPost("transfer")]
    [Consumes("application/xml", "text/xml")]
    [Produces("application/xml")]
    public async Task<IActionResult> Transfer([FromBody] TransferXmlRequest xmlRequest, CancellationToken cancellationToken)
    {
        // Validate XML request
        var validationResult = await xmlValidationService.ValidateAsync(xmlRequest);
        if (!validationResult.IsValid)
        {
            logger.LogWarning("XML validation failed for transfer: {Errors}", string.Join(", ", validationResult.Errors));
            return BadRequest(xmlResponseFactory.CreateTransferValidationErrorResponse(xmlRequest));
        }

        try
        {
            logger.LogInformation("Processing transfer: SessionID={SessionID}, Amount={Amount}, Account={Account}",
                xmlRequest.SessionId, xmlRequest.Amount, xmlRequest.BeneficiaryAccountNumber);

            // Process transfer
            var command = new PaymentTransferDto
            {
                Amount = decimal.Parse(xmlRequest.Amount),
                AccountNumber = xmlRequest.BeneficiaryAccountNumber,
                BankCode = xmlRequest.DestinationInstitutionCode,
                AccountName = xmlRequest.BeneficiaryAccountName.UnescapeXml(),
                Narration = xmlRequest.Narration.UnescapeXml(),
                PreferredProvider = null,
                OriginatorAccountName = xmlRequest.OriginatorAccountName.UnescapeXml(),
                OriginatorAccountNumber = xmlRequest.OriginatorAccountNumber,
                OriginatorBankVerificationNumber = xmlRequest.OriginatorBankVerificationNumber,
                OriginatorKycLevel = xmlRequest.OriginatorKycLevel,
                TransactionLocation = xmlRequest.TransactionLocation,
                PaymentReference = xmlRequest.PaymentReference,
                NameEnquiryRef = xmlRequest.NameEnquiryRef.Trim(),
                SessionId = xmlRequest.SessionId?.Trim(),
                ChannelCode = xmlRequest.ChannelCode
            };

            var result = await paymentGateway.ProcessTransferAsync(command, cancellationToken);
            var response = xmlResponseFactory.CreateTransferSuccessResponse(xmlRequest, result);

            return result.IsSuccessful ? Ok(response) : BadRequest(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing XML transfer");
            return BadRequest(xmlResponseFactory.CreateTransferErrorResponse(xmlRequest));
        }
    }
}
