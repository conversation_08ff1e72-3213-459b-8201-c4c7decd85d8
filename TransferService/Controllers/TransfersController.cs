using Microsoft.AspNetCore.Mvc;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;
using TransferService.Application.Common.Utilities;

namespace TransferService.Controllers;

/// <summary>
/// Central Payment Gateway Controller - handles XML requests and routes to appropriate payment providers
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/xml")]
public class BelemaEasyTransferController(
    IPaymentGatewayService paymentGateway,
    IXmlValidationService xmlValidationService,
    IXmlResponseFactory xmlResponseFactory,
    IEncryptedXmlService encryptedXmlService,
    ILogger<BelemaEasyTransferController> logger) : ControllerBase
{
    /// <summary>
    /// Central Name Enquiry Gateway - accepts encrypted base64 XML and routes to appropriate payment provider
    /// </summary>
    [HttpPost("name-enquiry")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> NameEnquiry([FromBody] string encryptedXmlPayload, CancellationToken cancellationToken)
    {
        try
        {
            // Decrypt and deserialize the XML request
            var xmlRequest = await encryptedXmlService.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedXmlPayload);
            if (xmlRequest == null)
            {
                logger.LogWarning("Failed to decrypt and deserialize name enquiry request");
                return BadRequest("Invalid encrypted XML payload");
            }

            // Validate XML request
            var validationResult = await xmlValidationService.ValidateAsync(xmlRequest);
            if (!validationResult.IsValid)
            {
                logger.LogWarning("XML validation failed for name enquiry: {Errors}", string.Join(", ", validationResult.Errors));
                var errorResponse = xmlResponseFactory.CreateNameEnquiryValidationErrorResponse(xmlRequest);
                var encryptedErrorResponse = await encryptedXmlService.CreateEncryptedErrorResponseAsync(errorResponse);
                return BadRequest(encryptedErrorResponse);
            }

            // Process name enquiry
            var command = new PaymentNameEnquiryCommand
            {
                AccountNumber = xmlRequest.AccountNumber,
                BankCode = xmlRequest.DestinationInstitutionCode
            };

            var result = await paymentGateway.ProcessNameEnquiryAsync(command, cancellationToken);
            var response = xmlResponseFactory.CreateNameEnquirySuccessResponse(xmlRequest, result);

            // Serialize and encrypt the response
            var encryptedResponse = await encryptedXmlService.SerializeAndEncryptAsync(response);

            logger.LogInformation("Successfully processed name enquiry for account {AccountNumber}", xmlRequest.AccountNumber);
            return result.IsSuccessful ? Ok(encryptedResponse) : BadRequest(encryptedResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing encrypted name enquiry");
            return BadRequest("Name enquiry processing failed");
        }
    }

    /// <summary>
    /// Central Transfer Gateway - accepts encrypted base64 XML and routes to appropriate payment provider via load balancing
    /// </summary>
    [HttpPost("transfer")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> Transfer([FromBody] string encryptedXmlPayload, CancellationToken cancellationToken)
    {
        try
        {
            // Decrypt and deserialize the XML request
            var xmlRequest = await encryptedXmlService.DecryptAndDeserializeAsync<TransferXmlRequest>(encryptedXmlPayload);
            if (xmlRequest == null)
            {
                logger.LogWarning("Failed to decrypt and deserialize transfer request");
                return BadRequest("Invalid encrypted XML payload");
            }

            // Validate XML request
            var validationResult = await xmlValidationService.ValidateAsync(xmlRequest);
            if (!validationResult.IsValid)
            {
                logger.LogWarning("XML validation failed for transfer: {Errors}", string.Join(", ", validationResult.Errors));
                var errorResponse = xmlResponseFactory.CreateTransferValidationErrorResponse(xmlRequest);
                var encryptedErrorResponse = await encryptedXmlService.CreateEncryptedErrorResponseAsync(errorResponse);
                return BadRequest(encryptedErrorResponse);
            }

            logger.LogInformation("Processing transfer: SessionID={SessionID}, Amount={Amount}, Account={Account}",
                xmlRequest.SessionId, xmlRequest.Amount, xmlRequest.BeneficiaryAccountNumber);

            // Process transfer
            var command = new PaymentTransferDto
            {
                Amount = decimal.Parse(xmlRequest.Amount),
                AccountNumber = xmlRequest.BeneficiaryAccountNumber,
                BankCode = xmlRequest.DestinationInstitutionCode,
                AccountName = xmlRequest.BeneficiaryAccountName.UnescapeXml(),
                Narration = xmlRequest.Narration.UnescapeXml(),
                PreferredProvider = null,
                OriginatorAccountName = xmlRequest.OriginatorAccountName.UnescapeXml(),
                OriginatorAccountNumber = xmlRequest.OriginatorAccountNumber,
                OriginatorBankVerificationNumber = xmlRequest.OriginatorBankVerificationNumber,
                OriginatorKycLevel = xmlRequest.OriginatorKycLevel,
                TransactionLocation = xmlRequest.TransactionLocation,
                PaymentReference = xmlRequest.PaymentReference,
                NameEnquiryRef = xmlRequest.NameEnquiryRef.Trim(),
                SessionId = xmlRequest.SessionId?.Trim(),
                ChannelCode = xmlRequest.ChannelCode
            };

            var result = await paymentGateway.ProcessTransferAsync(command, cancellationToken);
            var response = xmlResponseFactory.CreateTransferSuccessResponse(xmlRequest, result);

            // Serialize and encrypt the response
            var encryptedResponse = await encryptedXmlService.SerializeAndEncryptAsync(response);

            logger.LogInformation("Successfully processed transfer for session {SessionID}", xmlRequest.SessionId);
            return result.IsSuccessful ? Ok(encryptedResponse) : BadRequest(encryptedResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing XML transfer");
            return BadRequest("Transfer processing failed");
        }
    }
}
