using Microsoft.AspNetCore.Mvc;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;
using TransferService.Application.Common.Utilities;
using System.Xml.Serialization;

namespace TransferService.Controllers;

/// <summary>
/// Central Payment Gateway Controller - handles XML requests and routes to appropriate payment providers
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/xml")]
public class BelemaEasyTransferController(
    IPaymentGatewayService paymentGateway,
    IXmlValidationService xmlValidationService,
    IXmlResponseFactory xmlResponseFactory,
    IRSAService rsaService,
    ILogger<BelemaEasyTransferController> logger) : ControllerBase
{
    /// <summary>
    /// Central Name Enquiry Gateway - accepts encrypted base64 XML and routes to appropriate payment provider
    /// </summary>
    [HttpPost("name-enquiry")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> NameEnquiry([FromBody] string encryptedXmlPayload, CancellationToken cancellationToken)
    {
        try
        {
            // Step 1: Decrypt the base64 encrypted XML payload
            logger.LogInformation("Decrypting XML payload for name enquiry");
            var decryptedXml = rsaService.Decrypt(encryptedXmlPayload);

            // Step 2: Deserialize XML to object
            var xmlRequest = DeserializeXml<NameEnquiryXmlRequest>(decryptedXml);
            if (xmlRequest == null)
            {
                logger.LogWarning("Failed to deserialize decrypted XML for name enquiry");
                return BadRequest("Invalid XML format");
            }

            // Step 3: Validate XML request
            var validationResult = await xmlValidationService.ValidateAsync(xmlRequest);
            if (!validationResult.IsValid)
            {
                logger.LogWarning("XML validation failed for name enquiry: {Errors}", string.Join(", ", validationResult.Errors));
                var errorResponse = xmlResponseFactory.CreateNameEnquiryValidationErrorResponse(xmlRequest);
                var errorXml = SerializeToXml(errorResponse);
                var encryptedErrorResponse = rsaService.Encrypt(errorXml);
                return BadRequest(encryptedErrorResponse);
            }

            // Step 4: Process name enquiry
            var command = new PaymentNameEnquiryCommand
            {
                AccountNumber = xmlRequest.AccountNumber,
                BankCode = xmlRequest.DestinationInstitutionCode
            };

            var result = await paymentGateway.ProcessNameEnquiryAsync(command, cancellationToken);
            var response = xmlResponseFactory.CreateNameEnquirySuccessResponse(xmlRequest, result);

            // Step 5: Serialize response to XML
            var responseXml = SerializeToXml(response);

            // Step 6: Encrypt the XML response
            var encryptedResponse = rsaService.Encrypt(responseXml);

            logger.LogInformation("Successfully processed and encrypted name enquiry response");
            return result.IsSuccessful ? Ok(encryptedResponse) : BadRequest(encryptedResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing XML name enquiry");
            return BadRequest(xmlResponseFactory.CreateNameEnquiryErrorResponse(xmlRequest));
        }
    }

    /// <summary>
    /// Central Transfer Gateway - accepts encrypted base64 XML and routes to appropriate payment provider via load balancing
    /// </summary>
    [HttpPost("transfer")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> Transfer([FromBody] string encryptedXmlPayload, CancellationToken cancellationToken)
    {
        try
        {
            // Step 1: Decrypt the base64 encrypted XML payload
            logger.LogInformation("Decrypting XML payload for transfer");
            var decryptedXml = rsaService.Decrypt(encryptedXmlPayload);

            // Step 2: Deserialize XML to object
            var xmlRequest = DeserializeXml<TransferXmlRequest>(decryptedXml);
            if (xmlRequest == null)
            {
                logger.LogWarning("Failed to deserialize decrypted XML for transfer");
                return BadRequest("Invalid XML format");
            }

            // Step 3: Validate XML request
            var validationResult = await xmlValidationService.ValidateAsync(xmlRequest);
            if (!validationResult.IsValid)
            {
                logger.LogWarning("XML validation failed for transfer: {Errors}", string.Join(", ", validationResult.Errors));
                var errorResponse = xmlResponseFactory.CreateTransferValidationErrorResponse(xmlRequest);
                var errorXml = SerializeToXml(errorResponse);
                var encryptedErrorResponse = rsaService.Encrypt(errorXml);
                return BadRequest(encryptedErrorResponse);
            }

            logger.LogInformation("Processing transfer: SessionID={SessionID}, Amount={Amount}, Account={Account}",
                xmlRequest.SessionId, xmlRequest.Amount, xmlRequest.BeneficiaryAccountNumber);

            // Step 4: Process transfer
            var command = new PaymentTransferDto
            {
                Amount = decimal.Parse(xmlRequest.Amount),
                AccountNumber = xmlRequest.BeneficiaryAccountNumber,
                BankCode = xmlRequest.DestinationInstitutionCode,
                AccountName = xmlRequest.BeneficiaryAccountName.UnescapeXml(),
                Narration = xmlRequest.Narration.UnescapeXml(),
                PreferredProvider = null,
                OriginatorAccountName = xmlRequest.OriginatorAccountName.UnescapeXml(),
                OriginatorAccountNumber = xmlRequest.OriginatorAccountNumber,
                OriginatorBankVerificationNumber = xmlRequest.OriginatorBankVerificationNumber,
                OriginatorKycLevel = xmlRequest.OriginatorKycLevel,
                TransactionLocation = xmlRequest.TransactionLocation,
                PaymentReference = xmlRequest.PaymentReference,
                NameEnquiryRef = xmlRequest.NameEnquiryRef.Trim(),
                SessionId = xmlRequest.SessionId?.Trim(),
                ChannelCode = xmlRequest.ChannelCode
            };

            var result = await paymentGateway.ProcessTransferAsync(command, cancellationToken);
            var response = xmlResponseFactory.CreateTransferSuccessResponse(xmlRequest, result);

            // Step 5: Serialize response to XML
            var responseXml = SerializeToXml(response);

            // Step 6: Encrypt the XML response
            var encryptedResponse = rsaService.Encrypt(responseXml);

            logger.LogInformation("Successfully processed and encrypted transfer response");
            return result.IsSuccessful ? Ok(encryptedResponse) : BadRequest(encryptedResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing XML transfer");
            return BadRequest(xmlResponseFactory.CreateTransferErrorResponse(xmlRequest));
        }
    }

    /// <summary>
    /// Helper method to deserialize XML string to object
    /// </summary>
    private static T? DeserializeXml<T>(string xml) where T : class
    {
        try
        {
            var serializer = new System.Xml.Serialization.XmlSerializer(typeof(T));
            using var reader = new StringReader(xml);
            return serializer.Deserialize(reader) as T;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Helper method to serialize object to XML string
    /// </summary>
    private static string SerializeToXml<T>(T obj) where T : class
    {
        try
        {
            var serializer = new System.Xml.Serialization.XmlSerializer(typeof(T));
            using var writer = new StringWriter();
            serializer.Serialize(writer, obj);
            return writer.ToString();
        }
        catch
        {
            return string.Empty;
        }
    }
}
