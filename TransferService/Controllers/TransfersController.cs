using Microsoft.AspNetCore.Mvc;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;
using TransferService.Application.Services.Interfaces;
using TransferService.Application.Common.Utilities;

namespace TransferService.Controllers;

/// <summary>
/// Central Payment Gateway Controller - handles XML requests and routes to appropriate payment providers
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/xml")]
public class BelemaEasyTransferController(
    IPaymentGatewayService paymentGateway,
    IXmlValidationService xmlValidationService,
    IXmlResponseFactory xmlResponseFactory,
    IEncryptedXmlService encryptedXmlService,
    ILogger<BelemaEasyTransferController> logger) : ControllerBase
{
    /// <summary>
    /// Central Name Enquiry Gateway - accepts PGP-encrypted XML and routes to appropriate payment provider
    /// </summary>
    /// <remarks>
    /// This endpoint accepts PGP-encrypted XML in the request body as plain text.
    ///
    /// **How to use:**
    /// 1. Create your XML request (see example below)
    /// 2. Encrypt it using the `/api/pgptest/encrypt` endpoint
    /// 3. Paste the encrypted result into this endpoint's request body
    /// 4. The response will also be PGP-encrypted
    /// 5. Decrypt the response using the `/api/pgptest/decrypt` endpoint
    ///
    /// **Example XML to encrypt:**
    /// ```xml
    /// &lt;?xml version="1.0" encoding="UTF-8"?&gt;
    /// &lt;NESingleRequest&gt;
    ///     &lt;SessionID&gt;000504250718204500123456789114&lt;/SessionID&gt;
    ///     &lt;DestinationInstitutionCode&gt;999998&lt;/DestinationInstitutionCode&gt;
    ///     &lt;ChannelCode&gt;1&lt;/ChannelCode&gt;
    ///     &lt;AccountNumber&gt;**********&lt;/AccountNumber&gt;
    /// &lt;/NESingleRequest&gt;
    /// ```
    ///
    /// **Example encrypted input:**
    /// ```
    /// -----BEGIN PGP MESSAGE-----
    /// Version: BouncyCastle.NET Cryptography (net6.0) v2.4.0+83ebf4a805
    ///
    /// hIwDAAIBAw/6gj9XjUIByy1iBG5hbWVog+g4VGhpcyBpcyBhIHRlc3QgbWVzc2Fn
    /// ZSBmb3Igc2lnbmluZy6IvwQAAQIAKQUCaIPoOCIcdHJhbnNmZXJzZXJ2aWNlQGJl
    /// bGVtYWZpbnRlY2guY29tAAoJEAMP+oI/V41CPAQD/RIhPWvHfxNyy6apg7CrNn2Q
    /// =A+9R
    /// -----END PGP MESSAGE-----
    /// ```
    /// </remarks>
    [HttpPost("name-enquiry")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> NameEnquiry(CancellationToken cancellationToken)
    {
        try
        {
            // Read the encrypted XML payload from request body
            using var reader = new StreamReader(Request.Body);
            var encryptedXmlPayload = await reader.ReadToEndAsync(cancellationToken);

            if (string.IsNullOrWhiteSpace(encryptedXmlPayload))
            {
                logger.LogWarning("Empty encrypted XML payload received");
                return BadRequest("Empty payload");
            }

            // Decrypt and deserialize the XML request
            var xmlRequest = await encryptedXmlService.DecryptAndDeserializeAsync<NameEnquiryXmlRequest>(encryptedXmlPayload);
            if (xmlRequest == null)
            {
                logger.LogWarning("Failed to decrypt and deserialize name enquiry request");
                return BadRequest("Invalid encrypted XML payload");
            }

            // Validate XML request
            var validationResult = await xmlValidationService.ValidateAsync(xmlRequest);
            if (!validationResult.IsValid)
            {
                logger.LogWarning("XML validation failed for name enquiry: {Errors}", string.Join(", ", validationResult.Errors));
                var errorResponse = xmlResponseFactory.CreateNameEnquiryValidationErrorResponse(xmlRequest);
                var encryptedErrorResponse = await encryptedXmlService.CreateEncryptedErrorResponseAsync(errorResponse);
                return BadRequest(encryptedErrorResponse);
            }

            // Process name enquiry
            var command = new PaymentNameEnquiryCommand
            {
                AccountNumber = xmlRequest.AccountNumber,
                BankCode = xmlRequest.DestinationInstitutionCode
            };

            var result = await paymentGateway.ProcessNameEnquiryAsync(command, cancellationToken);
            var response = xmlResponseFactory.CreateNameEnquirySuccessResponse(xmlRequest, result);

            // Serialize and encrypt the response
            var encryptedResponse = await encryptedXmlService.SerializeAndEncryptAsync(response);

            logger.LogInformation("Successfully processed name enquiry for account {AccountNumber}", xmlRequest.AccountNumber);
            return result.IsSuccessful ? Ok(encryptedResponse) : BadRequest(encryptedResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing encrypted name enquiry");
            return BadRequest("Name enquiry processing failed");
        }
    }

    /// <summary>
    /// Central Transfer Gateway - accepts PGP-encrypted XML and routes to appropriate payment provider via load balancing
    /// </summary>
    /// <remarks>
    /// This endpoint accepts PGP-encrypted XML in the request body as plain text.
    ///
    /// **How to use:**
    /// 1. Create your XML request (see example below)
    /// 2. Encrypt it using the `/api/pgptest/encrypt` endpoint
    /// 3. Paste the encrypted result into this endpoint's request body
    /// 4. The response will also be PGP-encrypted
    /// 5. Decrypt the response using the `/api/pgptest/decrypt` endpoint
    ///
    /// **Example XML to encrypt:**
    /// ```xml
    /// &lt;?xml version="1.0" encoding="UTF-8"?&gt;
    /// &lt;FTSingleCreditRequest&gt;
    ///     &lt;SessionID&gt;000504250718105353033496566072&lt;/SessionID&gt;
    ///     &lt;NameEnquiryRef&gt;000504250718105353033496566072&lt;/NameEnquiryRef&gt;
    ///     &lt;DestinationInstitutionCode&gt;999998&lt;/DestinationInstitutionCode&gt;
    ///     &lt;ChannelCode&gt;1&lt;/ChannelCode&gt;
    ///     &lt;BeneficiaryAccountName&gt;Ake Mobolaji Temabo&lt;/BeneficiaryAccountName&gt;
    ///     &lt;BeneficiaryAccountNumber&gt;**********&lt;/BeneficiaryAccountNumber&gt;
    ///     &lt;BeneficiaryBankVerificationNumber&gt;***********&lt;/BeneficiaryBankVerificationNumber&gt;
    ///     &lt;BeneficiaryKYCLevel&gt;1&lt;/BeneficiaryKYCLevel&gt;
    ///     &lt;OriginatorAccountName&gt;vee Test&lt;/OriginatorAccountName&gt;
    ///     &lt;OriginatorAccountNumber&gt;**********&lt;/OriginatorAccountNumber&gt;
    ///     &lt;OriginatorBankVerificationNumber&gt;***********&lt;/OriginatorBankVerificationNumber&gt;
    ///     &lt;OriginatorKYCLevel&gt;1&lt;/OriginatorKYCLevel&gt;
    ///     &lt;TransactionLocation&gt;1.38716,3.05117&lt;/TransactionLocation&gt;
    ///     &lt;Narration&gt;Payment from ********** to **********&lt;/Narration&gt;
    ///     &lt;PaymentReference&gt;NIPMINI/**********&lt;/PaymentReference&gt;
    ///     &lt;Amount&gt;100.00&lt;/Amount&gt;
    /// &lt;/FTSingleCreditRequest&gt;
    /// ```
    /// </remarks>
    [HttpPost("transfer")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public async Task<IActionResult> Transfer(CancellationToken cancellationToken)
    {
        try
        {
            // Read the encrypted XML payload from request body
            using var reader = new StreamReader(Request.Body);
            var encryptedXmlPayload = await reader.ReadToEndAsync(cancellationToken);

            if (string.IsNullOrWhiteSpace(encryptedXmlPayload))
            {
                logger.LogWarning("Empty encrypted XML payload received");
                return BadRequest("Empty payload");
            }

            // Decrypt and deserialize the XML request
            var xmlRequest = await encryptedXmlService.DecryptAndDeserializeAsync<TransferXmlRequest>(encryptedXmlPayload);
            if (xmlRequest == null)
            {
                logger.LogWarning("Failed to decrypt and deserialize transfer request");
                return BadRequest("Invalid encrypted XML payload");
            }

            // Validate XML request
            var validationResult = await xmlValidationService.ValidateAsync(xmlRequest);
            if (!validationResult.IsValid)
            {
                logger.LogWarning("XML validation failed for transfer: {Errors}", string.Join(", ", validationResult.Errors));
                var errorResponse = xmlResponseFactory.CreateTransferValidationErrorResponse(xmlRequest);
                var encryptedErrorResponse = await encryptedXmlService.CreateEncryptedErrorResponseAsync(errorResponse);
                return BadRequest(encryptedErrorResponse);
            }

            logger.LogInformation("Processing transfer: SessionID={SessionID}, Amount={Amount}, Account={Account}",
                xmlRequest.SessionId, xmlRequest.Amount, xmlRequest.BeneficiaryAccountNumber);

            // Process transfer
            var command = new PaymentTransferDto
            {
                Amount = decimal.Parse(xmlRequest.Amount),
                AccountNumber = xmlRequest.BeneficiaryAccountNumber,
                BankCode = xmlRequest.DestinationInstitutionCode,
                AccountName = xmlRequest.BeneficiaryAccountName.UnescapeXml(),
                Narration = xmlRequest.Narration.UnescapeXml(),
                PreferredProvider = null,
                OriginatorAccountName = xmlRequest.OriginatorAccountName.UnescapeXml(),
                OriginatorAccountNumber = xmlRequest.OriginatorAccountNumber,
                OriginatorBankVerificationNumber = xmlRequest.OriginatorBankVerificationNumber,
                OriginatorKycLevel = xmlRequest.OriginatorKycLevel,
                TransactionLocation = xmlRequest.TransactionLocation,
                PaymentReference = xmlRequest.PaymentReference,
                NameEnquiryRef = xmlRequest.NameEnquiryRef.Trim(),
                SessionId = xmlRequest.SessionId?.Trim(),
                ChannelCode = xmlRequest.ChannelCode
            };

            var result = await paymentGateway.ProcessTransferAsync(command, cancellationToken);
            var response = xmlResponseFactory.CreateTransferSuccessResponse(xmlRequest, result);

            // Serialize and encrypt the response
            var encryptedResponse = await encryptedXmlService.SerializeAndEncryptAsync(response);

            logger.LogInformation("Successfully processed transfer for session {SessionID}", xmlRequest.SessionId);
            return result.IsSuccessful ? Ok(encryptedResponse) : BadRequest(encryptedResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing XML transfer");
            return BadRequest("Transfer processing failed");
        }
    }
}
