// using AutoMapper.Configuration.Annotations;
// using MediatR;
// using Microsoft.AspNetCore.Mvc;
// using TransferService.Application.Features.UnifiedPayment.Command;
// using TransferService.Application.Features.UnifiedPayment.Models;
// using TransferService.Application.Interfaces;

// namespace TransferService.Controllers;

// /// <summary>
// /// EasyPay transfer operations controller for NIBSS EasyPay services
// /// </summary>
// [ApiExplorerSettings(IgnoreApi = true)]
// [ApiController]
// [Route("api/[controller]")]
// [Produces("application/json")]
// public class UnifiedPaymentController : ControllerBase
// {
//     private readonly IMediator _mediator;
//     private readonly IPaymentGatewayService _paymentGatewayService;
//     private readonly ILogger<UnifiedPaymentController> _logger;

//     /// <summary>
//     /// EasyPay transfer operations controller for NIBSS EasyPay services
//     /// </summary>
//     public UnifiedPaymentController(IMediator mediator, ILogger<UnifiedPaymentController> logger, IPaymentGatewayService paymentGatewayService)
//     {
//         _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
//         _logger = logger ?? throw new ArgumentNullException(nameof(logger));
//         _paymentGatewayService = paymentGatewayService;
//     }


//     /// <summary>
//     /// Central Transfer Gateway - accepts XML and routes to appropriate payment provider via load balancing
//     /// </summary>
//     [HttpPost("transfer")]
//     public async Task<IActionResult> Transfer([FromBody] PaymentTransferDto requestBody, CancellationToken cancellationToken)
//     {       
//         try
//         {
//             _logger.LogInformation("Processing transfer: TransactionID={TransactionId}, Amount={Amount}, Account={Account}",
//                 requestBody.TrxId, requestBody.Amount, requestBody.AccountNumber);

//             // Process transfer
//             requestBody.PreferredProvider = "uppayment";

//             var result = await _paymentGatewayService.ProcessTransferAsync(requestBody, cancellationToken);
//             return result.IsSuccessful ? Ok(result) : BadRequest(result);
//         }
//         catch (Exception ex)
//         {
//             _logger.LogError(ex, "Error processing transfer");
//             return BadRequest("Error processing transfer");
//         }
//     }

//     /// <summary>
//     /// Performs name enquiry to validate account details
//     /// </summary>
//     /// <param name="request">Name enquiry request containing account number and bank code</param>
//     /// <param name="cancellationToken">Cancellation token</param>
//     /// <returns>Account validation result with account name if successful</returns>
//     /// <response code="200">Name enquiry completed successfully</response>
//     /// <response code="400">Invalid request parameters</response>
//     /// <response code="500">Internal server error</response>
//     [HttpPost("name-enquiry")]
//     [ProducesResponseType(typeof(NameEnqDto), StatusCodes.Status200OK)]
//     [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
//     [ProducesResponseType(StatusCodes.Status500InternalServerError)]
//     public async Task<ActionResult<NameEnqDto>> NameEnquiry(
//         [FromBody] NameEnqDto request,
//         CancellationToken cancellationToken = default)
//     {

//         var result = await _paymentGatewayService.ProcessNameEnquiryAsync(new PaymentNameEnquiryCommand { AccountNumber = request.AccountNumber, BankCode = request.BankCode, PreferredProvider = "uppayment" }, cancellationToken);

//         if (result is not null)
//         {
//             return Ok(result);
//         }
//         return BadRequest(result);
//     }

//     /// <summary>
//     /// Performs tsq enquiry 
//     /// </summary>
//     /// <param name="request">Name enquiry request containing account number and bank code</param>
//     /// <param name="cancellationToken">Cancellation token</param>
//     /// <returns>Account validation result with account name if successful</returns>
//     /// <response code="200">Name enquiry completed successfully</response>
//     /// <response code="400">Invalid request parameters</response>
//     /// <response code="500">Internal server error</response>
//     [HttpPost("transfer/status")]
//     [ProducesResponseType(typeof(NameEnqDto), StatusCodes.Status200OK)]
//     [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
//     [ProducesResponseType(StatusCodes.Status500InternalServerError)]
//     public async Task<ActionResult<TsqDto>> TransferStatusEnquiry(
//         [FromBody] TransferStatusDto request,
//         CancellationToken cancellationToken = default)
//     {
//         //validation done in command

//         request.PreferredProvider = "uppayment"; //remove later
        
//         var result = await _paymentGatewayService.ProcessTSQEnquiryAsync(request, cancellationToken);

//         if (result is not null)
//         {
//             return Ok(result);
//         }
//         return BadRequest(result);
//     }

// }