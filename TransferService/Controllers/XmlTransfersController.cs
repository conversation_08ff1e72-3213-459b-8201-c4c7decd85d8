using Microsoft.AspNetCore.Mvc;
using MediatR;
using TransferService.Application.Features.Transfers.Commands;
using TransferService.Application.Features.Transfers.Models;
using TransferService.Application.Interfaces;
using TransferService.Infrastructure.ExternalServices;
using System.Net;

namespace TransferService.Controllers;

/// <summary>
/// XML Transfer operations controller for processing transfers via XML
/// </summary>
[ApiController]
[Route("api/xml/[controller]")]
[Produces("application/xml")]
public class XmlTransfersController(IMediator mediator, IXmlConverterService xmlConverter, IPGPService pgpService) : ControllerBase
{
    /// <summary>
    /// Process a transfer request via XML
    /// </summary>
    /// <param name="transferRequest">XML transfer request object</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>XML transfer response</returns>
    /// <response code="200">Transfer processed successfully</response>
    /// <response code="400">Invalid XML or validation errors</response>
    /// <response code="500">Internal server error</response>
    /// <remarks>
    /// Sample XML request:
    ///
    ///     &lt;TransferRequest&gt;
    ///         &lt;FromAccount&gt;ACC123&lt;/FromAccount&gt;
    ///         &lt;ToAccount&gt;ACC456&lt;/ToAccount&gt;
    ///         &lt;Amount&gt;75000.00&lt;/Amount&gt;
    ///         &lt;Currency&gt;NGN&lt;/Currency&gt;
    ///         &lt;Description&gt;Test transfer&lt;/Description&gt;
    ///         &lt;RequestId&gt;test-123&lt;/RequestId&gt;
    ///     &lt;/TransferRequest&gt;
    ///
    /// </remarks>
    [HttpPost]
    [Consumes("application/xml", "text/xml")]
    [ProducesResponseType(typeof(XmlTransferResponseDto), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(string), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(string), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> ProcessXmlTransfer([FromBody] XmlTransferRequestDto? transferRequest, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate XML input
            if (transferRequest == null)
            {
                return BadRequest("XML request body is required");
            }

            // Create command from XML request
            var command = new ProcessXmlTransferCommand
            {
                FromAccount = transferRequest.FromAccount,
                ToAccount = transferRequest.ToAccount,
                Amount = transferRequest.Amount,
                Currency = transferRequest.Currency,
                Description = transferRequest.Description,
                RequestId = transferRequest.RequestId,
                Timestamp = transferRequest.Timestamp
            };

            // Process the command
            var result = await mediator.Send(command, cancellationToken);
            // Convert response object back to XML
            var xmlResponse = await xmlConverter.SerializeToXmlAsync(result);

            // Return XML response
            return Content(xmlResponse, "application/xml");
        }
        catch (Exception ex)
        {
            // Create error response as XML
            var errorResponse = new XmlTransferResponseDto
            {
                TransactionId = Guid.NewGuid().ToString(),
                Status = "ERROR",
                Message = "Failed to process XML transfer request",
                ErrorCode = "XML_PROCESSING_ERROR",
                ErrorDetails = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };

            var errorXml = await xmlConverter.SerializeToXmlAsync(errorResponse);
            return BadRequest(errorXml);
        }
    }

    /// <summary>
    /// Get a sample XML request format
    /// </summary>
    /// <returns>Sample XML request</returns>
    [HttpGet("sample-request")]
    [ProducesResponseType(typeof(string), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSampleRequest()
    {
        var sampleRequest = new XmlTransferRequestDto
        {
            FromAccount = "ACC001",
            ToAccount = "ACC002",
            Amount = 50000.00m,
            Currency = "NGN",
            Description = "Sample transfer request - Nigerian Naira",
            RequestId = Guid.NewGuid().ToString(),
            Timestamp = DateTime.UtcNow
        };

        var xmlContent = await xmlConverter.SerializeToXmlAsync(sampleRequest);
        return Content(xmlContent, "application/xml");
    }

    /// <summary>
    /// Get a sample XML response format
    /// </summary>
    /// <returns>Sample XML response</returns>
    [HttpGet("sample-response")]
    [ProducesResponseType(typeof(string), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSampleResponse()
    {
        var sampleResponse = new XmlTransferResponseDto
        {
            TransactionId = Guid.NewGuid().ToString(),
            Status = "SUCCESS",
            Message = "Transfer processed successfully",
            FromAccount = "ACC001",
            ToAccount = "ACC002",
            Amount = 50000.00m,
            Currency = "NGN",
            RequestId = Guid.NewGuid().ToString(),
            ProcessedAt = DateTime.UtcNow
        };

        var xmlContent = await xmlConverter.SerializeToXmlAsync(sampleResponse);
        return Content(xmlContent, "application/xml");
    }

    /// <summary>
    /// Test PGP encryption endpoint
    /// </summary>
    /// <param name="plainText">Text to encrypt</param>
    /// <returns>Encrypted text</returns>
    [HttpPost("test-encrypt")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public IActionResult TestEncrypt([FromBody] string plainText)
    {
        try
        {
            var encryptedText = pgpService.Encrypt(plainText);
            return Ok(encryptedText);
        }
        catch (Exception ex)
        {
            return BadRequest($"Encryption failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Test PGP decryption endpoint
    /// </summary>
    /// <param name="encryptedText">Encrypted text to decrypt</param>
    /// <returns>Decrypted text</returns>
    [HttpPost("test-decrypt")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public IActionResult TestDecrypt([FromBody] string encryptedText)
    {
        try
        {
            var decryptedText = pgpService.Decrypt(encryptedText);
            return Ok(decryptedText);
        }
        catch (Exception ex)
        {
            return BadRequest($"Decryption failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Test PGP signing endpoint
    /// </summary>
    /// <param name="data">Data to sign</param>
    /// <returns>Signature</returns>
    [HttpPost("test-sign")]
    [Consumes("text/plain")]
    [Produces("text/plain")]
    public IActionResult TestSign([FromBody] string data)
    {
        try
        {
            var signature = pgpService.Sign(data);
            return Ok(signature);
        }
        catch (Exception ex)
        {
            return BadRequest($"Signing failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Test PGP signature verification endpoint
    /// </summary>
    /// <param name="request">Data and signature to verify</param>
    /// <returns>Verification result</returns>
    [HttpPost("test-verify")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public IActionResult TestVerify([FromBody] VerifySignatureRequest request)
    {
        try
        {
            var isValid = pgpService.VerifySignature(request.Data, request.Signature);
            return Ok(new { IsValid = isValid });
        }
        catch (Exception ex)
        {
            return BadRequest($"Verification failed: {ex.Message}");
        }
    }
}

/// <summary>
/// Request model for signature verification
/// </summary>
public class VerifySignatureRequest
{
    public string Data { get; set; } = string.Empty;
    public string Signature { get; set; } = string.Empty;
}
