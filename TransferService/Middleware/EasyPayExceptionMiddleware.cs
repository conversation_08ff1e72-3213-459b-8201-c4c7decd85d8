using System.Net;
using System.Text.Json;
using TransferService.Domain.Exceptions;

namespace TransferService.Middleware;

/// <summary>
/// Middleware for handling EasyPay-specific exceptions
/// </summary>
public class EasyPayExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<EasyPayExceptionMiddleware> _logger;

    public EasyPayExceptionMiddleware(RequestDelegate next, ILogger<EasyPayExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = context.Response;
        response.ContentType = "application/json";

        var errorResponse = new ErrorResponse();

        switch (exception)
        {
            case EasyPayAuthenticationException authEx:
                _logger.LogError(authEx, "EasyPay authentication error");
                response.StatusCode = (int)HttpStatusCode.Unauthorized;
                errorResponse = new ErrorResponse
                {
                    Message = "Authentication failed with EasyPay service",
                    Details = authEx.Message,
                    ErrorCode = "EASYPAY_AUTH_ERROR"
                };
                break;

            case EasyPayApiException apiEx:
                _logger.LogError(apiEx, "EasyPay API error: {ResponseCode} - {ResponseMessage}", 
                    apiEx.ResponseCode, apiEx.ResponseMessage);
                response.StatusCode = (int)HttpStatusCode.BadGateway;
                errorResponse = new ErrorResponse
                {
                    Message = "EasyPay service error",
                    Details = apiEx.ResponseMessage ?? apiEx.Message,
                    ErrorCode = apiEx.ResponseCode ?? "EASYPAY_API_ERROR"
                };
                break;

            case EasyPayNameEnquiryException nameEx:
                _logger.LogError(nameEx, "EasyPay name enquiry error for account {AccountNumber} at bank {BankCode}", 
                    nameEx.AccountNumber, nameEx.BankCode);
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                errorResponse = new ErrorResponse
                {
                    Message = "Name enquiry failed",
                    Details = nameEx.Message,
                    ErrorCode = "EASYPAY_NAME_ENQUIRY_ERROR"
                };
                break;

            case EasyPayTransferException transferEx:
                _logger.LogError(transferEx, "EasyPay transfer error for amount {Amount} to account {AccountNumber} at bank {BankCode}", 
                    transferEx.Amount, transferEx.AccountNumber, transferEx.BankCode);
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                errorResponse = new ErrorResponse
                {
                    Message = "Transfer failed",
                    Details = transferEx.Message,
                    ErrorCode = "EASYPAY_TRANSFER_ERROR"
                };
                break;

            case EasyPayConfigurationException configEx:
                _logger.LogError(configEx, "EasyPay configuration error");
                response.StatusCode = (int)HttpStatusCode.InternalServerError;
                errorResponse = new ErrorResponse
                {
                    Message = "Service configuration error",
                    Details = "EasyPay service is not properly configured",
                    ErrorCode = "EASYPAY_CONFIG_ERROR"
                };
                break;

            case EasyPayException easyPayEx:
                _logger.LogError(easyPayEx, "General EasyPay error");
                response.StatusCode = (int)HttpStatusCode.BadGateway;
                errorResponse = new ErrorResponse
                {
                    Message = "EasyPay service error",
                    Details = easyPayEx.Message,
                    ErrorCode = "EASYPAY_ERROR"
                };
                break;

            default:
                _logger.LogError(exception, "Unhandled exception occurred");
                response.StatusCode = (int)HttpStatusCode.InternalServerError;
                errorResponse = new ErrorResponse
                {
                    Message = "An internal server error occurred",
                    Details = "Please try again later",
                    ErrorCode = "INTERNAL_ERROR"
                };
                break;
        }

        var jsonResponse = JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await response.WriteAsync(jsonResponse);
    }
}

/// <summary>
/// Standard error response format
/// </summary>
public class ErrorResponse
{
    public string Message { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    public string ErrorCode { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
