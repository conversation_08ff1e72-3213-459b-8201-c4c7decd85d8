using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Text;
using System.Text.Json;
using TransferService.Application.Configuration;
using TransferService.Application.Common.Models;
using TransferService.Application.Interfaces;

namespace TransferService.Middleware;

/// <summary>
/// Middleware for automatic RSA encryption/decryption of HTTP requests and responses
/// </summary>
public class RSAEncryptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RSAEncryptionMiddleware> _logger;
    private readonly RSAOptions _rsaOptions;
    private readonly IRSACryptographyService _rsaService;

    public RSAEncryptionMiddleware(
        RequestDelegate next,
        ILogger<RSAEncryptionMiddleware> logger,
        IOptions<RSAOptions> rsaOptions,
        IRSACryptographyService rsaService)
    {
        _next = next;
        _logger = logger;
        _rsaOptions = rsaOptions.Value;
        _rsaService = rsaService;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (!_rsaOptions.Middleware.Enabled)
        {
            await _next(context);
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        var requestId = Guid.NewGuid().ToString();

        try
        {
            // Check if this endpoint should be processed
            var endpointConfig = GetEndpointConfiguration(context.Request.Path);
            if (endpointConfig == null || !endpointConfig.Enabled)
            {
                await _next(context);
                return;
            }

            // Check if request should be bypassed
            if (ShouldBypassEncryption(context, endpointConfig))
            {
                await _next(context);
                return;
            }

            // Process request decryption if enabled
            if (_rsaOptions.Middleware.EnableRequestDecryption && 
                ShouldDecryptRequest(context, endpointConfig))
            {
                await DecryptRequestAsync(context, endpointConfig, requestId);
            }

            // Capture response for encryption
            var originalResponseBodyStream = context.Response.Body;
            using var responseBodyStream = new MemoryStream();
            context.Response.Body = responseBodyStream;

            // Continue with the pipeline
            await _next(context);

            // Process response encryption if enabled
            if (_rsaOptions.Middleware.EnableResponseEncryption && 
                ShouldEncryptResponse(context, endpointConfig))
            {
                await EncryptResponseAsync(context, endpointConfig, requestId, responseBodyStream);
            }

            // Copy response back to original stream
            responseBodyStream.Seek(0, SeekOrigin.Begin);
            await responseBodyStream.CopyToAsync(originalResponseBodyStream);

            stopwatch.Stop();
            
            if (_rsaOptions.Monitoring.EnablePerformanceMonitoring)
            {
                _logger.LogInformation("RSA middleware processed request {RequestId} in {ElapsedMs}ms", 
                    requestId, stopwatch.ElapsedMilliseconds);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            await HandleEncryptionErrorAsync(context, ex, requestId);
        }
    }

    private RSAEndpointOptions? GetEndpointConfiguration(PathString path)
    {
        var pathString = path.Value?.ToLowerInvariant();
        
        // Find matching endpoint configuration
        foreach (var endpoint in _rsaOptions.Endpoints)
        {
            if (pathString?.StartsWith(endpoint.Key.ToLowerInvariant()) == true)
            {
                return endpoint.Value;
            }
        }

        return null;
    }

    private bool ShouldBypassEncryption(HttpContext context, RSAEndpointOptions endpointConfig)
    {
        var bypass = endpointConfig.Bypass;

        // Bypass health checks
        if (bypass.HealthChecks && IsHealthCheckRequest(context))
        {
            return true;
        }

        // Bypass specific user agents
        var userAgent = context.Request.Headers.UserAgent.ToString();
        if (bypass.BypassUserAgents.Any(ua => userAgent.Contains(ua, StringComparison.OrdinalIgnoreCase)))
        {
            return true;
        }

        // Bypass specific IP addresses
        var clientIp = GetClientIpAddress(context);
        if (bypass.BypassIpAddresses.Any(ip => clientIp == ip))
        {
            return true;
        }

        // Bypass based on headers
        foreach (var header in bypass.BypassHeaders)
        {
            if (context.Request.Headers.TryGetValue(header.Key, out var headerValue) &&
                headerValue.ToString() == header.Value)
            {
                return true;
            }
        }

        return false;
    }

    private bool IsHealthCheckRequest(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        return path?.Contains("health") == true || path?.Contains("ping") == true;
    }

    private string GetClientIpAddress(HttpContext context)
    {
        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }

    private bool ShouldDecryptRequest(HttpContext context, RSAEndpointOptions endpointConfig)
    {
        var method = context.Request.Method;
        var contentType = context.Request.ContentType;

        return endpointConfig.EncryptedMethods.Contains(method) &&
               endpointConfig.EncryptedContentTypes.Any(ct => 
                   contentType?.Contains(ct, StringComparison.OrdinalIgnoreCase) == true) &&
               context.Request.ContentLength > 0;
    }

    private bool ShouldEncryptResponse(HttpContext context, RSAEndpointOptions endpointConfig)
    {
        var contentType = context.Response.ContentType;
        return endpointConfig.EncryptedContentTypes.Any(ct => 
            contentType?.Contains(ct, StringComparison.OrdinalIgnoreCase) == true);
    }

    private async Task DecryptRequestAsync(HttpContext context, RSAEndpointOptions endpointConfig, string requestId)
    {
        try
        {
            context.Request.EnableBuffering();
            
            using var reader = new StreamReader(context.Request.Body, Encoding.UTF8, leaveOpen: true);
            var encryptedBody = await reader.ReadToEndAsync();
            
            if (string.IsNullOrEmpty(encryptedBody))
            {
                return;
            }

            // Decrypt the request body
            var decryptResult = await _rsaService.DecryptAsync(encryptedBody, endpointConfig.KeyIdentifier);
            
            if (!decryptResult.IsSuccess)
            {
                throw new InvalidOperationException($"Request decryption failed: {decryptResult.ErrorMessage}");
            }

            // Replace the request body with decrypted content
            var decryptedBytes = Encoding.UTF8.GetBytes(decryptResult.Data!);
            context.Request.Body = new MemoryStream(decryptedBytes);
            context.Request.ContentLength = decryptedBytes.Length;

            _logger.LogDebug("Request {RequestId} decrypted successfully", requestId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt request {RequestId}", requestId);
            throw;
        }
    }

    private async Task EncryptResponseAsync(HttpContext context, RSAEndpointOptions endpointConfig, string requestId, MemoryStream responseBodyStream)
    {
        try
        {
            responseBodyStream.Seek(0, SeekOrigin.Begin);
            using var reader = new StreamReader(responseBodyStream, Encoding.UTF8, leaveOpen: true);
            var responseBody = await reader.ReadToEndAsync();

            if (string.IsNullOrEmpty(responseBody))
            {
                return;
            }

            // Encrypt the response body
            var encryptResult = await _rsaService.EncryptAsync(responseBody, endpointConfig.KeyIdentifier);
            
            if (!encryptResult.IsSuccess)
            {
                throw new InvalidOperationException($"Response encryption failed: {encryptResult.ErrorMessage}");
            }

            // Replace the response body with encrypted content
            var encryptedBytes = Encoding.UTF8.GetBytes(encryptResult.Data!);
            responseBodyStream.SetLength(0);
            await responseBodyStream.WriteAsync(encryptedBytes);
            context.Response.ContentLength = encryptedBytes.Length;

            _logger.LogDebug("Response {RequestId} encrypted successfully", requestId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt response {RequestId}", requestId);
            throw;
        }
    }

    private async Task HandleEncryptionErrorAsync(HttpContext context, Exception ex, string requestId)
    {
        _logger.LogError(ex, "RSA middleware error for request {RequestId}", requestId);

        var errorAction = _rsaOptions.Middleware.ErrorHandling.OnError;
        
        switch (errorAction)
        {
            case RSAErrorAction.ReturnError:
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                context.Response.ContentType = "application/json";
                
                var errorResponse = new
                {
                    error = "Encryption/Decryption Error",
                    message = _rsaOptions.Middleware.ErrorHandling.IncludeDetailsInErrors ? ex.Message : "Request processing failed",
                    requestId = requestId
                };
                
                var errorJson = JsonSerializer.Serialize(errorResponse);
                await context.Response.WriteAsync(errorJson);
                break;

            case RSAErrorAction.Bypass:
                // Continue without encryption/decryption
                await _next(context);
                break;

            case RSAErrorAction.ThrowException:
                throw;

            default:
                throw;
        }
    }
} 