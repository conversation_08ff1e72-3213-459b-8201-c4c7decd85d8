using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text;

namespace TransferService.Middleware;

public class RequestResponseLoggingMiddleware(RequestDelegate next, ILogger<RequestResponseLoggingMiddleware> logger)
{
    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var requestId = Guid.NewGuid().ToString();

        // Log request
        await LogRequestAsync(context, requestId);

        // Capture response
        var originalResponseBodyStream = context.Response.Body;
        using var responseBodyStream = new MemoryStream();
        context.Response.Body = responseBodyStream;

        try
        {
            await next(context);
        }
        finally
        {
            stopwatch.Stop();

            // Log response
            await LogResponseAsync(context, requestId, stopwatch.ElapsedMilliseconds);

            // Copy response back to original stream
            responseBodyStream.Seek(0, SeekOrigin.Begin);
            await responseBodyStream.CopyToAsync(originalResponseBodyStream);
        }
    }

    private async Task LogRequestAsync(HttpContext context, string requestId)
    {
        try
        {
            var request = context.Request;
            
            // Read request body
            var requestBody = string.Empty;
            if (request.ContentLength > 0 && IsLoggableContentType(request.ContentType))
            {
                request.EnableBuffering();
                using var reader = new StreamReader(request.Body, Encoding.UTF8, leaveOpen: true);
                requestBody = await reader.ReadToEndAsync();
                request.Body.Position = 0;
            }

            logger.LogInformation("HTTP Request {RequestId}: {Method} {Path} {QueryString} | Content-Type: {ContentType} | Content-Length: {ContentLength} | Body: {RequestBody}",
                requestId,
                request.Method,
                request.Path,
                request.QueryString,
                request.ContentType ?? "N/A",
                request.ContentLength ?? 0,
                SanitizeRequestBody(requestBody));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error logging request {RequestId}", requestId);
        }
    }

    private async Task LogResponseAsync(HttpContext context, string requestId, long elapsedMilliseconds)
    {
        try
        {
            var response = context.Response;
            
            // Read response body
            string responseBody = string.Empty;
            if (response.Body.CanSeek && IsLoggableContentType(response.ContentType))
            {
                response.Body.Seek(0, SeekOrigin.Begin);
                using var reader = new StreamReader(response.Body, Encoding.UTF8, leaveOpen: true);
                responseBody = await reader.ReadToEndAsync();
                response.Body.Seek(0, SeekOrigin.Begin);
            }

            var logLevel = response.StatusCode >= 400 ? LogLevel.Warning : LogLevel.Information;

            logger.Log(logLevel, "HTTP Response {RequestId}: {StatusCode} | Content-Type: {ContentType} | Content-Length: {ContentLength} | Duration: {Duration}ms | Body: {ResponseBody}",
                requestId,
                response.StatusCode,
                response.ContentType ?? "N/A",
                response.ContentLength ?? 0,
                elapsedMilliseconds,
                SanitizeResponseBody(responseBody));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error logging response {RequestId}", requestId);
        }
    }

    private static bool IsLoggableContentType(string? contentType)
    {
        if (string.IsNullOrEmpty(contentType))
            return false;

        var loggableTypes = new[]
        {
            "application/json",
            "application/xml",
            "text/xml",
            "text/plain",
            "application/x-www-form-urlencoded"
        };

        return loggableTypes.Any(type => contentType.Contains(type, StringComparison.OrdinalIgnoreCase));
    }

    private static string SanitizeRequestBody(string body)
    {
        if (string.IsNullOrEmpty(body))
            return "N/A";

        // Remove sensitive information
        // This is a simple example - implement more sophisticated sanitization as needed
        var sensitiveFields = new[] { "password", "token", "secret", "key", "authorization" };

        body = sensitiveFields.Aggregate(body, (current, field) => System.Text.RegularExpressions.Regex.Replace(current, $@"""{field}"":\s*""[^""]*""", $@"""{field}"":""***""", System.Text.RegularExpressions.RegexOptions.IgnoreCase));

        return body.Length > 1000 ? body.Substring(0, 1000) + "..." : body;
    }

    private static string SanitizeResponseBody(string body)
    {
        if (string.IsNullOrEmpty(body))
            return "N/A";

        return body.Length > 1000 ? body[..1000] + "..." : body;
    }
}
