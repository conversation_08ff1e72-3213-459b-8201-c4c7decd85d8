using Serilog;
using TransferService.Application;
using TransferService.Infrastructure;
using TransferService.Configuration;
using TransferService.Middleware;
using TransferService.Application.Common.Formatters;

// Configure Serilog early
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .CreateLogger();

try
{
    var builder = WebApplication.CreateBuilder(args);

    // Add Serilog
    builder.Host.UseSerilog((context, configuration) =>
        configuration.ReadFrom.Configuration(context.Configuration));

    // Add services to the container
    builder.Services.AddControllers(options =>
    {
        // Remove default XML input formatter and add our custom safe one
        var xmlInputFormatter = options.InputFormatters.OfType<Microsoft.AspNetCore.Mvc.Formatters.XmlSerializerInputFormatter>().FirstOrDefault();
        if (xmlInputFormatter != null)
        {
            options.InputFormatters.Remove(xmlInputFormatter);
        }

        // Add our custom XML safe input and output formatters
        options.InputFormatters.Add(new XmlSafeInputFormatter());

        // Remove default XML output formatter and add our custom safe one
        var xmlOutputFormatter = options.OutputFormatters.OfType<Microsoft.AspNetCore.Mvc.Formatters.XmlSerializerOutputFormatter>().FirstOrDefault();
        if (xmlOutputFormatter != null)
        {
            options.OutputFormatters.Remove(xmlOutputFormatter);
        }
        options.OutputFormatters.Add(new XmlSafeOutputFormatter());

        // Support XML content types
        options.FormatterMappings.SetMediaTypeMappingForFormat("xml", "application/xml");
        
        // Add RSA action filter
        options.Filters.Add<RSAEncryptionActionFilter>();
    })
    .AddXmlSerializerFormatters(); // Add XML formatters
    builder.Services.AddEndpointsApiExplorer();

    // Add custom services
    builder.Services.AddApplication();
    builder.Services.AddInfrastructure(builder.Configuration, builder.Environment);
    builder.Services.AddSwaggerDocumentation();

    // Add CORS
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowAll", policy =>
        {
            policy.AllowAnyOrigin()
                  .AllowAnyMethod()
                  .AllowAnyHeader();
        });
    });

    var app = builder.Build();

    // Configure the HTTP request pipeline
    if (app.Environment.IsDevelopment())
    {
        app.UseSwaggerDocumentation(app.Environment);
    }

    // Add Serilog request logging
    app.UseSerilogRequestLogging();

    // Add global exception handling
    app.UseMiddleware<GlobalExceptionMiddleware>();

    // Add custom middleware
    app.UseMiddleware<RequestResponseLoggingMiddleware>();
    
    // Add RSA encryption middleware
    app.UseMiddleware<RSAEncryptionMiddleware>();

    app.UseHttpsRedirection();
    app.UseCors("AllowAll");
    app.UseStaticFiles();

    app.UseRouting();
    app.UseAuthorization();

    app.MapControllers();
    app.MapHealthChecks("/health");

    // Add a simple health check endpoint
    app.MapGet("/", () => new {
        Service = "Transfer Service",
        Version = "1.0.0",
        Status = "Running",
        Timestamp = DateTime.UtcNow
    });

    Log.Information("Starting Transfer Service...");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}