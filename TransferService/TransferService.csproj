<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\TransferService.Domain\TransferService.Domain.csproj" />
        <ProjectReference Include="..\TransferService.Application\TransferService.Application.csproj" />
        <ProjectReference Include="..\TransferService.Infrastructure\TransferService.Infrastructure.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
    </ItemGroup>

</Project>
