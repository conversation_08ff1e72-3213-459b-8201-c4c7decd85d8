{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/application-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 104857600, "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g", "restrictedToMinimumLevel": "Information"}}, {"Name": "File", "Args": {"path": "logs/debug-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "fileSizeLimitBytes": 209715200, "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g", "restrictedToMinimumLevel": "Debug"}}, {"Name": "File", "Args": {"path": "logs/errors-.log", "rollingInterval": "Day", "retainedFileCountLimit": 90, "fileSizeLimitBytes": 52428800, "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g", "restrictedToMinimumLevel": "Error"}}]}, "ExternalApis": {"EasyPay": {"BaseUrl": "https://apitest.nibss-plc.com.ng/nipservice/v1/nip", "TokenResetUrl": "https://apitest.nibss-plc.com.ng/reset", "ClientId": "a2539af3-34d1-4287-9d7f-4615299c2004", "ClientSecret": "****************************************", "ApiKey": "Tnwkf6YZ0HexSucWoIqKPJvtYd8tn8h0", "InstitutionCode": "000504", "SourceInstitutionCode": "999998", "BillerId": "ADC19BDC-7D3A-4C00-4F7B-08DA06684F59", "MandateRef": "MA-**********-2022315-53097", "OriginatorAccountName": "vee Test", "OriginatorAccountNumber": "**********", "TimeoutSeconds": 30, "CacheStrategy": "FusionCache", "RedisConnectionString": null}, "UnifiedPayment": {"BaseUrl": "http://************", "Username": "GENERICUSER", "Password": "JUSTAPASSWORD", "Key": "07GD1D31F0664561611A03CA6B2E8C8C", "IV": "800E03CA6B2E8C5C", "NameEnq": {"TimeoutSeconds": 60}, "TSQ": {"TimeoutSeconds": 60}}}, "PaymentGateway": {"LoadBalancingPolicy": "RoundR<PERSON>in", "HealthCheck": {"Enabled": true, "Interval": "00:00:15", "Timeout": "00:00:03", "FailureThreshold": 2}, "Destinations": {"easypay": {"Enabled": true, "Priority": 1, "Weight": 100, "HealthEndpoint": "/health", "Metadata": {"provider": "easypay", "maxAmount": "100000", "environment": "development"}}, "uppayment": {"Enabled": false, "Priority": 2, "Weight": 50, "HealthEndpoint": "/health", "Metadata": {"provider": "uppayment", "maxAmount": "50000", "environment": "development"}}}}, "RSA": {"YourPrivateKeyPath": "keys/your-private.key", "YourPublicKeyPath": "keys/your-public.key", "ThirdPartyPublicKeyPath": "keys/third-party-public.key"}}