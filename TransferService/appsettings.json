{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/application-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 104857600, "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g", "restrictedToMinimumLevel": "Information"}}, {"Name": "File", "Args": {"path": "logs/debug-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "fileSizeLimitBytes": 209715200, "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g", "restrictedToMinimumLevel": "Debug"}}, {"Name": "File", "Args": {"path": "logs/errors-.log", "rollingInterval": "Day", "retainedFileCountLimit": 90, "fileSizeLimitBytes": 52428800, "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g", "restrictedToMinimumLevel": "Error"}}]}, "ExternalApis": {"EasyPay": {"BaseUrl": "https://apitest.nibss-plc.com.ng/nipservice/v1/nip", "TokenResetUrl": "https://apitest.nibss-plc.com.ng/reset", "ClientId": "a2539af3-34d1-4287-9d7f-4615299c2004", "ClientSecret": "****************************************", "ApiKey": "Tnwkf6YZ0HexSucWoIqKPJvtYd8tn8h0", "InstitutionCode": "000504", "SourceInstitutionCode": "999998", "BillerId": "ADC19BDC-7D3A-4C00-4F7B-08DA06684F59", "MandateRef": "MA-**********-2022315-53097", "OriginatorAccountName": "vee Test", "OriginatorAccountNumber": "**********", "TimeoutSeconds": 30, "CacheStrategy": "FusionCache", "RedisConnectionString": null}, "UnifiedPayment": {"BaseUrl": "http://************", "Username": "GENERICUSER", "Password": "JUSTAPASSWORD", "Key": "07GD1D31F0664561611A03CA6B2E8C8C", "IV": "800E03CA6B2E8C5C", "NameEnq": {"TimeoutSeconds": 60}, "TSQ": {"TimeoutSeconds": 60}}}, "PaymentGateway": {"LoadBalancingPolicy": "RoundR<PERSON>in", "HealthCheck": {"Enabled": true, "Interval": "00:00:15", "Timeout": "00:00:03", "FailureThreshold": 2}, "Destinations": {"easypay": {"Enabled": true, "Priority": 1, "Weight": 100, "HealthEndpoint": "/health", "Metadata": {"provider": "easypay", "maxAmount": "100000", "environment": "development"}}, "uppayment": {"Enabled": false, "Priority": 2, "Weight": 50, "HealthEndpoint": "/health", "Metadata": {"provider": "uppayment", "maxAmount": "50000", "environment": "development"}}}}, "RSA": {"Encryption": {"DefaultKeyIdentifier": "transfer-service-default", "DefaultKeySize": 2048, "EnableKeyRotation": false, "KeyRotationInterval": "90.00:00:00", "EnablePerformanceLogging": true, "EnableDetailedErrorLogging": true, "MaxDataSizeBytes": 245, "EnableHealthChecks": true, "AutoLoadKeys": true}, "Keys": {"transfer-service-default": {"PublicKeyPath": "keys/other-party-public-key.pem", "PrivateKeyPath": "keys/your-private-key.pem", "KeyIdentifier": "transfer-service-default", "Description": "Default keys for transfer service"}, "transfers-endpoint": {"PublicKeyPath": "keys/other-party-public-key.pem", "PrivateKeyPath": "keys/your-private-key.pem", "KeyIdentifier": "transfers-endpoint", "Description": "Keys for transfers endpoint"}, "easypay-endpoint": {"PublicKeyPath": "keys/easypay-public-key.pem", "PrivateKeyPath": "keys/easypay-private-key.pem", "KeyIdentifier": "easypay-endpoint", "Description": "Keys for EasyPay endpoint"}, "unifiedpayment-endpoint": {"PublicKeyPath": "keys/unifiedpayment-public-key.pem", "PrivateKeyPath": "keys/unifiedpayment-private-key.pem", "KeyIdentifier": "unifiedpayment-endpoint", "Description": "Keys for UnifiedPayment endpoint"}}, "KeyManagement": {"EnableKeyRotation": false, "RotationInterval": "90.00:00:00", "MaxKeysInRotation": 3, "Storage": {"StorageType": "InMemory", "FilePath": null, "DatabaseConnectionString": null, "EncryptAtRest": true}, "Backup": {"Enabled": false, "BackupLocation": null, "BackupFrequency": "7.00:00:00", "RetentionCount": 10}}, "Endpoints": {"api/transfers": {"Enabled": true, "KeyIdentifier": "transfers-endpoint", "EncryptionMode": "RequestResponse", "SerializationFormat": "Json", "EncryptedContentTypes": ["application/json", "application/xml"], "EncryptedMethods": ["POST", "PUT", "PATCH"], "Bypass": {"HealthChecks": true, "BypassUserAgents": [], "BypassIpAddresses": [], "BypassHeaders": {}}}, "api/easypay": {"Enabled": true, "KeyIdentifier": "easypay-endpoint", "EncryptionMode": "RequestResponse", "SerializationFormat": "Json", "EncryptedContentTypes": ["application/json"], "EncryptedMethods": ["POST", "PUT"], "Bypass": {"HealthChecks": true, "BypassUserAgents": [], "BypassIpAddresses": [], "BypassHeaders": {}}}, "api/unifiedpayment": {"Enabled": true, "KeyIdentifier": "unifiedpayment-endpoint", "EncryptionMode": "RequestResponse", "SerializationFormat": "Xml", "EncryptedContentTypes": ["application/xml"], "EncryptedMethods": ["POST"], "Bypass": {"HealthChecks": true, "BypassUserAgents": [], "BypassIpAddresses": [], "BypassHeaders": {}}}}, "Middleware": {"Enabled": true, "Order": 100, "EnableRequestDecryption": true, "EnableResponseEncryption": true, "ErrorHandling": {"OnError": "ReturnError", "IncludeDetailsInErrors": false, "LogErrors": true, "EncryptionErrorMessage": "Request encryption failed", "DecryptionErrorMessage": "Request decryption failed"}}, "Monitoring": {"EnablePerformanceMonitoring": true, "EnableMetrics": true, "PerformanceWarningThreshold": 1000, "PerformanceErrorThreshold": 5000, "EnableHealthChecks": true, "HealthCheckInterval": "00:05:00"}}}