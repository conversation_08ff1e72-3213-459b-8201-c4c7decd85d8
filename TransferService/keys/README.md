# RSA Keys Directory

This directory contains RSA key files for the TransferService application.

## Key File Structure

The application expects the following key files to be present:

### Default Keys
- `other-party-public-key.pem` - The other party's public key (for encrypting responses)
- `your-private-key.pem` - Your private key (for decrypting requests)

### Endpoint-Specific Keys
- `easypay-public-key.pem` - EasyPay endpoint public key
- `easypay-private-key.pem` - EasyPay endpoint private key
- `unifiedpayment-public-key.pem` - UnifiedPayment endpoint public key
- `unifiedpayment-private-key.pem` - UnifiedPayment endpoint private key

## Key File Format

All keys should be in PEM format:

### Public Key Format
```
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----
```

### Private Key Format
```
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
-----<PERSON><PERSON> PRIVATE KEY-----
```

## Security Notes

1. **Never commit private keys to version control**
2. **Set appropriate file permissions** (600 for private keys, 644 for public keys)
3. **Use environment-specific key files** for different deployment environments
4. **Rotate keys regularly** according to your security policy

## Configuration

The key paths are configured in `appsettings.json` under the `RSA:Keys` section:

```json
{
  "RSA": {
    "Keys": {
      "transfer-service-default": {
        "PublicKeyPath": "keys/other-party-public-key.pem",
        "PrivateKeyPath": "keys/your-private-key.pem",
        "KeyIdentifier": "transfer-service-default"
      }
    }
  }
}
```

## Auto-Loading

The application will automatically load these keys on startup if `AutoLoadKeys` is enabled in the configuration. 