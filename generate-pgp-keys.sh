#!/bin/bash

# Generate PGP keys for TransferService
echo "🔐 Generating PGP keys for TransferService..."

# Create Keys directory if it doesn't exist
mkdir -p TransferService/Keys

# Generate PGP key pair
gpg --batch --generate-key <<EOF
Key-Type: RSA
Key-Length: 4096
Subkey-Type: RSA
Subkey-Length: 4096
Name-Real: TransferService
Name-Email: <EMAIL>
Expire-Date: 0
Passphrase: 
%commit
EOF

# Export public key
echo "📤 Exporting public key..."
gpg --armor --export <EMAIL> > TransferService/Keys/public.asc

# Export private key
echo "📤 Exporting private key..."
gpg --armor --export-secret-keys <EMAIL> > TransferService/Keys/private.asc

# Copy public key as third-party public key for testing
cp TransferService/Keys/public.asc TransferService/Keys/third-party-public.asc

echo "✅ PGP keys generated successfully!"
echo "📁 Keys saved to TransferService/Keys/"
echo "   - public.asc (Public key)"
echo "   - private.asc (Private key)"
echo "   - third-party-public.asc (Third-party public key for testing)"

# Show key info
echo ""
echo "🔍 Key information:"
gpg --list-keys <EMAIL>
