#!/bin/bash

echo "🔐 Generating proper PGP keys for TransferService..."

# Create Keys directory if it doesn't exist
mkdir -p TransferService/Keys

# Check if GPG is installed
if ! command -v gpg &> /dev/null; then
    echo "❌ GPG is not installed. Please install it first:"
    echo "   macOS: brew install gnupg"
    echo "   Ubuntu: sudo apt install gnupg"
    exit 1
fi

# Generate PGP key pair using batch mode
echo "📝 Creating GPG batch configuration..."
cat > gpg_batch_config <<EOF
%echo Generating PGP key pair for TransferService
Key-Type: RSA
Key-Length: 4096
Subkey-Type: RSA
Subkey-Length: 4096
Name-Real: TransferService
Name-Email: <EMAIL>
Expire-Date: 0
Passphrase:
%commit
%echo Done generating PGP key pair
EOF

echo "🔑 Generating PGP key pair..."
gpg --batch --generate-key gpg_batch_config

# Export public key in ASCII format
echo "📤 Exporting public key to TransferService/Keys/public.asc..."
gpg --armor --export <EMAIL> > TransferService/Keys/public.asc

# Export private key in ASCII format
echo "📤 Exporting private key to TransferService/Keys/private.asc..."
gpg --armor --export-secret-keys <EMAIL> > TransferService/Keys/private.asc

# Copy public key as third-party public key for testing
cp TransferService/Keys/public.asc TransferService/Keys/third-party-public.asc

# Clean up
rm gpg_batch_config

echo "✅ PGP keys generated successfully!"
echo "📁 Keys saved to TransferService/Keys/"
echo "   - public.asc (Public key)"
echo "   - private.asc (Private key)"
echo "   - third-party-public.asc (Third-party public key for testing)"

# Show key info
echo ""
echo "🔍 Key information:"
gpg --list-keys <EMAIL>

echo ""
echo "🎯 Next steps:"
echo "1. Update appsettings.json to use .asc files"
echo "2. Share public.asc with third parties"
echo "3. Import third party's public key as third-party-public.asc"
