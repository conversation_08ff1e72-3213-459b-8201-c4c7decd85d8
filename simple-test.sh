#!/bin/bash

# Simple test for Name Enquiry
echo "🧪 Testing Name Enquiry with 8192-bit RSA keys"

# Create the XML
XML='<?xml version="1.0" encoding="UTF-8"?>
<NESingleRequest>
    <SessionID>000504250718204500123456789114</SessionID>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>'

echo "📝 Original XML:"
echo "$XML"
echo ""

# Encrypt
echo "🔐 Encrypting..."
ENCRYPTED=$(echo "$XML" | openssl rsautl -encrypt -pubin -inkey TransferService/Keys/public.key | base64 -w 0)

if [ $? -eq 0 ]; then
    echo "✅ Encryption successful"
    echo "📦 Encrypted length: ${#ENCRYPTED} characters"
    echo ""
    
    # Test decryption
    echo "🔓 Testing decryption..."
    DECRYPTED=$(echo "$ENCRYPTED" | base64 -d | openssl rsautl -decrypt -inkey TransferService/Keys/private.key)
    
    if [ $? -eq 0 ]; then
        echo "✅ Decryption successful"
        echo "📄 Decrypted XML:"
        echo "$DECRYPTED"
        echo ""
        
        # Test API call
        echo "📡 Testing API call..."
        RESPONSE=$(curl -s -w "\n%{http_code}" -X POST \
            -H "Content-Type: text/plain" \
            -H "Accept: text/plain" \
            --data "$ENCRYPTED" \
            "http://localhost:5050/api/BelemaEasyTransfer/name-enquiry")
        
        RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)
        STATUS_CODE=$(echo "$RESPONSE" | tail -n 1)
        
        echo "📊 Status Code: $STATUS_CODE"
        echo "📄 Response: $RESPONSE_BODY"
        
        if [ "$STATUS_CODE" = "200" ]; then
            echo "✅ API call successful!"
            
            if [ ${#RESPONSE_BODY} -gt 50 ]; then
                echo "🔓 Attempting to decrypt response..."
                DECRYPTED_RESPONSE=$(echo "$RESPONSE_BODY" | base64 -d | openssl rsautl -decrypt -inkey TransferService/Keys/private.key 2>/dev/null)
                
                if [ $? -eq 0 ]; then
                    echo "✅ Response decrypted successfully:"
                    echo "$DECRYPTED_RESPONSE"
                else
                    echo "❌ Failed to decrypt response"
                fi
            fi
        else
            echo "❌ API call failed"
        fi
    else
        echo "❌ Decryption failed"
    fi
else
    echo "❌ Encryption failed"
fi
