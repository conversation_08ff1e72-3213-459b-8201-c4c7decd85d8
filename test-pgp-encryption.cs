using System;
using System.IO;
using System.Text;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;

class Program
{
    static void Main(string[] args)
    {
        try
        {
            Console.WriteLine("🔐 Testing PGP-style encryption...");
            
            // Load RSA keys
            var publicKey = LoadRsaPublicKey("TransferService/Keys/public.key");
            var privateKey = LoadRsaPrivateKey("TransferService/Keys/private.key");
            
            Console.WriteLine($"✅ Keys loaded successfully. Key size: {publicKey.Modulus.BitLength} bits");
            
            // Test data - Transfer XML
            var transferXml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<FTSingleCreditRequest>
    <SessionID>000504250718105353033496566072</SessionID>
    <NameEnquiryRef>000504250718105353033496566072</NameEnquiryRef>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <BeneficiaryAccountName>Ake Mobolaji Temabo</BeneficiaryAccountName>
    <BeneficiaryAccountNumber>**********</BeneficiaryAccountNumber>
    <BeneficiaryBankVerificationNumber>***********</BeneficiaryBankVerificationNumber>
    <BeneficiaryKYCLevel>1</BeneficiaryKYCLevel>
    <OriginatorAccountName>vee Test</OriginatorAccountName>
    <OriginatorAccountNumber>**********</OriginatorAccountNumber>
    <OriginatorBankVerificationNumber>***********</OriginatorBankVerificationNumber>
    <OriginatorKYCLevel>1</OriginatorKYCLevel>
    <TransactionLocation>1.38716,3.05117</TransactionLocation>
    <Narration>Payment from ********** to **********</Narration>
    <PaymentReference>NIPMINI/**********</PaymentReference>
    <Amount>100.00</Amount>
</FTSingleCreditRequest>";

            Console.WriteLine($"📄 Transfer XML length: {transferXml.Length} characters");
            
            // Encrypt with hybrid encryption
            var encrypted = EncryptWithHybrid(transferXml, publicKey);
            Console.WriteLine($"🔒 Encrypted length: {encrypted.Length} characters");
            Console.WriteLine($"🔒 Encrypted data: {encrypted.Substring(0, Math.Min(100, encrypted.Length))}...");
            
            // Decrypt
            var decrypted = DecryptWithHybrid(encrypted, privateKey);
            Console.WriteLine($"🔓 Decrypted length: {decrypted.Length} characters");
            
            // Verify
            if (transferXml == decrypted)
            {
                Console.WriteLine("✅ Encryption/Decryption successful!");
                
                // Save encrypted data for curl testing
                File.WriteAllText("encrypted-transfer.txt", encrypted);
                Console.WriteLine("💾 Encrypted data saved to encrypted-transfer.txt");
            }
            else
            {
                Console.WriteLine("❌ Encryption/Decryption failed - data mismatch!");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
    
    static string EncryptWithHybrid(string plainText, RsaKeyParameters publicKey)
    {
        var plainTextBytes = Encoding.UTF8.GetBytes(plainText);
        
        // Generate random AES key
        var aesKey = new byte[32]; // 256-bit key
        var random = new SecureRandom();
        random.NextBytes(aesKey);
        
        // Encrypt data with AES
        var aesEngine = CipherUtilities.GetCipher("AES/GCM/NoPadding");
        var aesKeyParam = new KeyParameter(aesKey);
        aesEngine.Init(true, aesKeyParam);
        var encryptedData = aesEngine.DoFinal(plainTextBytes);
        
        // Encrypt AES key with RSA
        var rsaCipher = CipherUtilities.GetCipher("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
        rsaCipher.Init(true, publicKey);
        var encryptedAesKey = rsaCipher.DoFinal(aesKey);
        
        // Combine encrypted AES key + encrypted data
        using var outputStream = new MemoryStream();
        using var writer = new BinaryWriter(outputStream);
        
        writer.Write(encryptedAesKey.Length);
        writer.Write(encryptedAesKey);
        writer.Write(encryptedData);
        
        // Prefix with "HYBRID:" to indicate encryption method
        return "HYBRID:" + Convert.ToBase64String(outputStream.ToArray());
    }
    
    static string DecryptWithHybrid(string encryptedData, RsaPrivateCrtKeyParameters privateKey)
    {
        // Remove prefix
        var base64Data = encryptedData.Substring(7);
        var encryptedBytes = Convert.FromBase64String(base64Data);
        
        using var inputStream = new MemoryStream(encryptedBytes);
        using var reader = new BinaryReader(inputStream);
        
        // Read encrypted AES key
        var aesKeyLength = reader.ReadInt32();
        var encryptedAesKey = reader.ReadBytes(aesKeyLength);
        
        // Read encrypted data
        var encryptedPayload = reader.ReadBytes((int)(inputStream.Length - inputStream.Position));
        
        // Decrypt AES key with RSA
        var rsaCipher = CipherUtilities.GetCipher("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
        rsaCipher.Init(false, privateKey);
        var aesKey = rsaCipher.DoFinal(encryptedAesKey);
        
        // Decrypt data with AES
        var aesEngine = CipherUtilities.GetCipher("AES/GCM/NoPadding");
        var aesKeyParam = new KeyParameter(aesKey);
        aesEngine.Init(false, aesKeyParam);
        var decryptedData = aesEngine.DoFinal(encryptedPayload);
        
        return Encoding.UTF8.GetString(decryptedData);
    }
    
    static RsaKeyParameters LoadRsaPublicKey(string publicKeyPath)
    {
        var keyContent = File.ReadAllText(publicKeyPath);
        using var reader = new StringReader(keyContent);
        var pemReader = new PemReader(reader);
        var keyObject = pemReader.ReadObject();
        
        if (keyObject is RsaKeyParameters rsaKey)
        {
            return rsaKey;
        }
        else if (keyObject is AsymmetricCipherKeyPair keyPair)
        {
            return (RsaKeyParameters)keyPair.Public;
        }
        
        throw new InvalidOperationException("Invalid RSA public key format");
    }

    static RsaPrivateCrtKeyParameters LoadRsaPrivateKey(string privateKeyPath)
    {
        var keyContent = File.ReadAllText(privateKeyPath);
        using var reader = new StringReader(keyContent);
        var pemReader = new PemReader(reader);
        var keyObject = pemReader.ReadObject();
        
        if (keyObject is RsaPrivateCrtKeyParameters rsaPrivateKey)
        {
            return rsaPrivateKey;
        }
        else if (keyObject is AsymmetricCipherKeyPair keyPair)
        {
            return (RsaPrivateCrtKeyParameters)keyPair.Private;
        }
        
        throw new InvalidOperationException("Invalid RSA private key format");
    }
}
