#!/bin/bash

echo "🔐 Testing PGP Encryption/Decryption Endpoints"
echo "=============================================="

BASE_URL="http://localhost:5050"

# Test 1: Encryption Endpoint
echo ""
echo "🔒 Test 1: Encryption Endpoint"
echo "------------------------------"
TEST_TEXT="Hello, PGP World! This is a test message for encryption."
echo "Input: $TEST_TEXT"

echo "Calling: POST $BASE_URL/api/pgptest/encrypt"
ENCRYPTED_RESULT=$(curl -s -X POST -H "Content-Type: text/plain" -d "$TEST_TEXT" "$BASE_URL/api/pgptest/encrypt")

if [[ $? -eq 0 && ! -z "$ENCRYPTED_RESULT" ]]; then
    echo "✅ Encryption successful!"
    echo "Encrypted length: ${#ENCRYPTED_RESULT} characters"
    echo "Encrypted data (first 100 chars): ${ENCRYPTED_RESULT:0:100}..."
    
    # Test 2: Decryption Endpoint
    echo ""
    echo "🔓 Test 2: Decryption Endpoint"
    echo "------------------------------"
    echo "Calling: POST $BASE_URL/api/pgptest/decrypt"
    DECRYPTED_RESULT=$(curl -s -X POST -H "Content-Type: text/plain" -d "$ENCRYPTED_RESULT" "$BASE_URL/api/pgptest/decrypt")
    
    if [[ $? -eq 0 && ! -z "$DECRYPTED_RESULT" ]]; then
        echo "✅ Decryption successful!"
        echo "Decrypted: $DECRYPTED_RESULT"
        
        # Verify round-trip
        if [[ "$TEST_TEXT" == "$DECRYPTED_RESULT" ]]; then
            echo "✅ Round-trip verification successful!"
            echo "🎉 PGP Encryption/Decryption endpoints working perfectly!"
        else
            echo "❌ Round-trip verification failed!"
            echo "Expected: $TEST_TEXT"
            echo "Got: $DECRYPTED_RESULT"
        fi
    else
        echo "❌ Decryption failed!"
        echo "Response: $DECRYPTED_RESULT"
    fi
else
    echo "❌ Encryption failed!"
    echo "Response: $ENCRYPTED_RESULT"
fi

# Test 3: Signing Endpoint
echo ""
echo "✍️ Test 3: Signing Endpoint"
echo "---------------------------"
SIGN_TEXT="This is a test message for signing."
echo "Input: $SIGN_TEXT"

echo "Calling: POST $BASE_URL/api/pgptest/sign"
SIGNATURE_RESULT=$(curl -s -X POST -H "Content-Type: text/plain" -d "$SIGN_TEXT" "$BASE_URL/api/pgptest/sign")

if [[ $? -eq 0 && ! -z "$SIGNATURE_RESULT" ]]; then
    echo "✅ Signing successful!"
    echo "Signature length: ${#SIGNATURE_RESULT} characters"
    echo "Signature (first 100 chars): ${SIGNATURE_RESULT:0:100}..."
    
    # Test 4: Verification Endpoint
    echo ""
    echo "✅ Test 4: Signature Verification Endpoint"
    echo "------------------------------------------"
    # Create a temporary JSON file with proper escaping using Python
    python3 -c "
import json
data = {
    'data': '''$SIGN_TEXT''',
    'signature': '''$SIGNATURE_RESULT'''
}
with open('verify_request.json', 'w') as f:
    json.dump(data, f)
"
    echo "Calling: POST $BASE_URL/api/pgptest/verify"
    VERIFY_RESULT=$(curl -s -X POST -H "Content-Type: application/json" -d @verify_request.json "$BASE_URL/api/pgptest/verify")
    rm -f verify_request.json
    
    if [[ $? -eq 0 && ! -z "$VERIFY_RESULT" ]]; then
        echo "✅ Verification response received!"
        echo "Result: $VERIFY_RESULT"
        
        if [[ "$VERIFY_RESULT" == *"true"* ]]; then
            echo "✅ Signature verification successful!"
            echo "🎉 PGP Signing/Verification endpoints working perfectly!"
        else
            echo "❌ Signature verification failed!"
        fi
    else
        echo "❌ Verification failed!"
        echo "Response: $VERIFY_RESULT"
    fi
else
    echo "❌ Signing failed!"
    echo "Response: $SIGNATURE_RESULT"
fi

echo ""
echo "🏁 PGP Endpoints Testing Complete!"
