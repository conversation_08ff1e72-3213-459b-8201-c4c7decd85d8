#!/bin/bash

# RSA Encryption Test Script using curl
# Tests the RSA encryption with real XML data

set -e

echo "🔐 RSA Encryption Test for TransferService"
echo "=========================================="

# Test XML data
NAME_ENQUIRY_XML='<?xml version="1.0" encoding="UTF-8"?>
<NESingleRequest>
    <SessionID>000504250718204500123456789114</SessionID>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>'

TRANSFER_XML='<?xml version="1.0" encoding="UTF-8"?>
<FTSingleCreditRequest>
    <SessionID>000504250718105353033496566072</SessionID>
    <NameEnquiryRef>000504250718105353033496566072</NameEnquiryRef>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <BeneficiaryAccountName>Ake Mobolaji Temabo</BeneficiaryAccountName>
    <BeneficiaryAccountNumber>**********</BeneficiaryAccountNumber>
    <BeneficiaryBankVerificationNumber>***********</BeneficiaryBankVerificationNumber>
    <BeneficiaryKYCLevel>1</BeneficiaryKYCLevel>
    <OriginatorAccountName>vee Test</OriginatorAccountName>
    <OriginatorAccountNumber>**********</OriginatorAccountNumber>
    <OriginatorBankVerificationNumber>***********</OriginatorBankVerificationNumber>
    <OriginatorKYCLevel>1</OriginatorKYCLevel>
    <TransactionLocation>1.38716,3.05117</TransactionLocation>
    <Narration>Payment from ********** to **********</Narration>
    <PaymentReference>NIPMINI/**********</PaymentReference>
    <Amount>100.00</Amount>
</FTSingleCreditRequest>'

PUBLIC_KEY_PATH="TransferService/Keys/public.key"
PRIVATE_KEY_PATH="TransferService/Keys/private.key"
BASE_URL="http://localhost:5050/api/BelemaEasyTransfer"

# Function to encrypt data with RSA public key
encrypt_data() {
    local data="$1"
    local temp_file=$(mktemp)
    local encrypted_file=$(mktemp)
    
    echo "$data" > "$temp_file"
    
    if openssl rsautl -encrypt -pubin -inkey "$PUBLIC_KEY_PATH" -in "$temp_file" -out "$encrypted_file" 2>/dev/null; then
        base64 -i "$encrypted_file"
        rm -f "$temp_file" "$encrypted_file"
        return 0
    else
        echo "❌ Encryption failed" >&2
        rm -f "$temp_file" "$encrypted_file"
        return 1
    fi
}

# Function to decrypt data with RSA private key
decrypt_data() {
    local encrypted_base64="$1"
    local temp_encrypted=$(mktemp)
    local temp_decrypted=$(mktemp)
    
    echo "$encrypted_base64" | base64 -d > "$temp_encrypted"
    
    if openssl rsautl -decrypt -inkey "$PRIVATE_KEY_PATH" -in "$temp_encrypted" -out "$temp_decrypted" 2>/dev/null; then
        cat "$temp_decrypted"
        rm -f "$temp_encrypted" "$temp_decrypted"
        return 0
    else
        echo "❌ Decryption failed" >&2
        rm -f "$temp_encrypted" "$temp_decrypted"
        return 1
    fi
}

# Function to test API endpoint
test_endpoint() {
    local encrypted_payload="$1"
    local endpoint="$2"
    local description="$3"
    
    echo ""
    echo "🧪 Testing $description"
    echo "================================"
    
    local url="$BASE_URL/$endpoint"
    echo "📡 Sending request to: $url"
    echo "📦 Payload length: ${#encrypted_payload} characters"
    
    # Make the API call
    local response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: text/plain" \
        -H "Accept: text/plain" \
        --data "$encrypted_payload" \
        "$url")
    
    # Extract response body and status code
    local response_body=$(echo "$response" | head -n -1)
    local status_code=$(echo "$response" | tail -n 1)
    
    echo "📊 Response Status: $status_code"
    
    if [ "$status_code" = "200" ]; then
        echo "✅ Request successful!"
        echo "📄 Response length: ${#response_body} characters"
        
        if [ ${#response_body} -gt 50 ]; then
            echo "📄 Response preview: ${response_body:0:100}..."
            echo ""
            echo "🔓 Attempting to decrypt response..."
            
            if decrypted_response=$(decrypt_data "$response_body"); then
                echo "✅ Decrypted response:"
                echo "$decrypted_response"
            else
                echo "❌ Failed to decrypt response"
                echo "Raw response: $response_body"
            fi
        else
            echo "📄 Response: $response_body"
        fi
    else
        echo "❌ Request failed!"
        echo "📄 Error response: $response_body"
    fi
}

# Check if keys exist
if [ ! -f "$PUBLIC_KEY_PATH" ]; then
    echo "❌ Public key not found at: $PUBLIC_KEY_PATH"
    exit 1
fi

if [ ! -f "$PRIVATE_KEY_PATH" ]; then
    echo "❌ Private key not found at: $PRIVATE_KEY_PATH"
    exit 1
fi

echo "✅ RSA keys found"

# Check if application is running
if ! curl -s "$BASE_URL/../health" > /dev/null 2>&1; then
    echo "⚠️  Application may not be running on http://localhost:5050"
    echo "   Make sure to start the application first with: dotnet run --project TransferService"
fi

# Test 1: Name Enquiry
echo ""
echo "🔍 Encrypting Name Enquiry XML..."
if encrypted_name_enquiry=$(encrypt_data "$NAME_ENQUIRY_XML"); then
    echo "✅ Name Enquiry XML encrypted successfully"
    test_endpoint "$encrypted_name_enquiry" "name-enquiry" "Name Enquiry API"
else
    echo "❌ Failed to encrypt Name Enquiry XML"
fi

# Test 2: Transfer
echo ""
echo "💸 Encrypting Transfer XML..."
if encrypted_transfer=$(encrypt_data "$TRANSFER_XML"); then
    echo "✅ Transfer XML encrypted successfully"
    test_endpoint "$encrypted_transfer" "transfer" "Transfer API"
else
    echo "❌ Failed to encrypt Transfer XML"
fi

echo ""
echo "=========================================="
echo "🎯 Test Summary:"
echo "• Make sure TransferService is running on http://localhost:5050"
echo "• Check application logs for detailed processing information"
echo "• Encrypted payloads are sent as text/plain content"
echo "• Responses should be encrypted and returned as text/plain"
echo "=========================================="
