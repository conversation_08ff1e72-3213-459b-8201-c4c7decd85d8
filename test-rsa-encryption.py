#!/usr/bin/env python3
"""
RSA Encryption Test Script for TransferService
Tests the RSA encryption with real XML data
"""

import base64
import subprocess
import tempfile
import os
import json
import requests

# Test XML data
NAME_ENQUIRY_XML = '''<?xml version="1.0" encoding="UTF-8"?>
<NESingleRequest>
    <SessionID>000504250718204500123456789114</SessionID>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>'''

TRANSFER_XML = '''<?xml version="1.0" encoding="UTF-8"?>
<FTSingleCreditRequest>
    <SessionID>000504250718105353033496566072</SessionID>
    <NameEnquiryRef>000504250718105353033496566072</NameEnquiryRef>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <BeneficiaryAccountName>Ake Mo<PERSON>aji Te<PERSON>bo</BeneficiaryAccountName>
    <BeneficiaryAccountNumber>**********</BeneficiaryAccountNumber>
    <BeneficiaryBankVerificationNumber>***********</BeneficiaryBankVerificationNumber>
    <BeneficiaryKYCLevel>1</BeneficiaryKYCLevel>
    <OriginatorAccountName>vee Test</OriginatorAccountName>
    <OriginatorAccountNumber>**********</OriginatorAccountNumber>
    <OriginatorBankVerificationNumber>***********</OriginatorBankVerificationNumber>
    <OriginatorKYCLevel>1</OriginatorKYCLevel>
    <TransactionLocation>1.38716,3.05117</TransactionLocation>
    <Narration>Payment from ********** to **********</Narration>
    <PaymentReference>NIPMINI/**********</PaymentReference>
    <Amount>100.00</Amount>
</FTSingleCreditRequest>'''

def encrypt_with_openssl(data, public_key_path):
    """Encrypt data using OpenSSL with the public key"""
    try:
        # Create temporary file for input data
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_input:
            temp_input.write(data)
            temp_input_path = temp_input.name
        
        # Create temporary file for encrypted output
        with tempfile.NamedTemporaryFile(delete=False, suffix='.enc') as temp_output:
            temp_output_path = temp_output.name
        
        # Encrypt using OpenSSL
        cmd = [
            'openssl', 'rsautl', '-encrypt', 
            '-pubin', '-inkey', public_key_path,
            '-in', temp_input_path,
            '-out', temp_output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ OpenSSL encryption failed: {result.stderr}")
            return None
        
        # Read encrypted data and encode as base64
        with open(temp_output_path, 'rb') as f:
            encrypted_data = f.read()
        
        # Clean up temporary files
        os.unlink(temp_input_path)
        os.unlink(temp_output_path)
        
        # Return base64 encoded encrypted data
        return base64.b64encode(encrypted_data).decode('utf-8')
        
    except Exception as e:
        print(f"❌ Encryption error: {e}")
        return None

def test_api_endpoint(encrypted_payload, endpoint, description):
    """Test the API endpoint with encrypted payload"""
    print(f"\n🧪 Testing {description}")
    print("=" * 50)
    
    try:
        url = f"http://localhost:5050/api/BelemaEasyTransfer/{endpoint}"
        headers = {
            'Content-Type': 'text/plain',
            'Accept': 'text/plain'
        }
        
        print(f"📡 Sending request to: {url}")
        print(f"📦 Payload length: {len(encrypted_payload)} characters")
        print(f"📦 Payload preview: {encrypted_payload[:100]}...")
        
        response = requests.post(url, data=encrypted_payload, headers=headers, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            print(f"📄 Response length: {len(response.text)} characters")
            print(f"📄 Response preview: {response.text[:200]}...")
            
            # Try to decode if it looks like base64
            try:
                if len(response.text) > 50:  # Likely encrypted response
                    print("🔓 Attempting to decrypt response...")
                    decrypted = decrypt_with_openssl(response.text, "TransferService/Keys/private.key")
                    if decrypted:
                        print(f"✅ Decrypted response:\n{decrypted}")
                    else:
                        print("❌ Failed to decrypt response")
            except Exception as e:
                print(f"⚠️  Could not decrypt response: {e}")
                
        else:
            print(f"❌ Request failed!")
            print(f"📄 Error response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed! Make sure the application is running on http://localhost:5050")
    except requests.exceptions.Timeout:
        print("❌ Request timed out!")
    except Exception as e:
        print(f"❌ Request error: {e}")

def decrypt_with_openssl(encrypted_base64, private_key_path):
    """Decrypt base64 encoded data using OpenSSL with private key"""
    try:
        # Decode base64
        encrypted_data = base64.b64decode(encrypted_base64)
        
        # Create temporary file for encrypted input
        with tempfile.NamedTemporaryFile(delete=False, suffix='.enc') as temp_input:
            temp_input.write(encrypted_data)
            temp_input_path = temp_input.name
        
        # Create temporary file for decrypted output
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_output:
            temp_output_path = temp_output.name
        
        # Decrypt using OpenSSL
        cmd = [
            'openssl', 'rsautl', '-decrypt',
            '-inkey', private_key_path,
            '-in', temp_input_path,
            '-out', temp_output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ OpenSSL decryption failed: {result.stderr}")
            return None
        
        # Read decrypted data
        with open(temp_output_path, 'r') as f:
            decrypted_data = f.read()
        
        # Clean up temporary files
        os.unlink(temp_input_path)
        os.unlink(temp_output_path)
        
        return decrypted_data
        
    except Exception as e:
        print(f"❌ Decryption error: {e}")
        return None

def main():
    """Main test function"""
    print("🔐 RSA Encryption Test for TransferService")
    print("=" * 60)
    
    public_key_path = "TransferService/Keys/public.key"
    
    # Check if keys exist
    if not os.path.exists(public_key_path):
        print(f"❌ Public key not found at: {public_key_path}")
        return 1
    
    print("✅ Public key found")
    
    # Test 1: Name Enquiry
    print("\n🔍 Encrypting Name Enquiry XML...")
    encrypted_name_enquiry = encrypt_with_openssl(NAME_ENQUIRY_XML, public_key_path)
    
    if encrypted_name_enquiry:
        print("✅ Name Enquiry XML encrypted successfully")
        test_api_endpoint(encrypted_name_enquiry, "name-enquiry", "Name Enquiry API")
    else:
        print("❌ Failed to encrypt Name Enquiry XML")
    
    # Test 2: Transfer
    print("\n💸 Encrypting Transfer XML...")
    encrypted_transfer = encrypt_with_openssl(TRANSFER_XML, public_key_path)
    
    if encrypted_transfer:
        print("✅ Transfer XML encrypted successfully")
        test_api_endpoint(encrypted_transfer, "transfer", "Transfer API")
    else:
        print("❌ Failed to encrypt Transfer XML")
    
    print("\n" + "=" * 60)
    print("🎯 Test Summary:")
    print("• Make sure TransferService is running on http://localhost:5050")
    print("• Check application logs for detailed processing information")
    print("• Encrypted payloads are sent as text/plain content")
    print("• Responses should be encrypted and returned as text/plain")
    
    return 0

if __name__ == "__main__":
    exit(main())
