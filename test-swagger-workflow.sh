#!/bin/bash

echo "🔐 Testing Swagger PGP Workflow"
echo "==============================="

BASE_URL="http://localhost:5050"

# Step 1: Create sample XML for name enquiry
NAME_ENQUIRY_XML='<?xml version="1.0" encoding="UTF-8"?>
<NESingleRequest>
    <SessionID>000504250718204500123456789114</SessionID>
    <DestinationInstitutionCode>999998</DestinationInstitutionCode>
    <ChannelCode>1</ChannelCode>
    <AccountNumber>**********</AccountNumber>
</NESingleRequest>'

echo ""
echo "📄 Step 1: Sample XML for Name Enquiry"
echo "--------------------------------------"
echo "$NAME_ENQUIRY_XML"

# Step 2: Encrypt the XML
echo ""
echo "🔒 Step 2: Encrypting XML using /api/pgptest/encrypt"
echo "---------------------------------------------------"
ENCRYPTED_XML=$(curl -s -X POST -H "Content-Type: text/plain" -d "$NAME_ENQUIRY_XML" "$BASE_URL/api/pgptest/encrypt")

if [[ $? -eq 0 && ! -z "$ENCRYPTED_XML" ]]; then
    echo "✅ Encryption successful!"
    echo "Encrypted length: ${#ENCRYPTED_XML} characters"
    echo "Encrypted data (first 100 chars): ${ENCRYPTED_XML:0:100}..."
    
    # Step 3: Use encrypted XML in name enquiry endpoint
    echo ""
    echo "🔍 Step 3: Calling /api/BelemaEasyTransfer/name-enquiry with encrypted XML"
    echo "------------------------------------------------------------------------"
    ENCRYPTED_RESPONSE=$(curl -s -X POST -H "Content-Type: text/plain" -d "$ENCRYPTED_XML" "$BASE_URL/api/BelemaEasyTransfer/name-enquiry")
    
    if [[ $? -eq 0 && ! -z "$ENCRYPTED_RESPONSE" ]]; then
        echo "✅ Name enquiry successful!"
        echo "Encrypted response length: ${#ENCRYPTED_RESPONSE} characters"
        echo "Encrypted response (first 100 chars): ${ENCRYPTED_RESPONSE:0:100}..."
        
        # Step 4: Decrypt the response
        echo ""
        echo "🔓 Step 4: Decrypting response using /api/pgptest/decrypt"
        echo "--------------------------------------------------------"
        DECRYPTED_RESPONSE=$(curl -s -X POST -H "Content-Type: text/plain" -d "$ENCRYPTED_RESPONSE" "$BASE_URL/api/pgptest/decrypt")
        
        if [[ $? -eq 0 && ! -z "$DECRYPTED_RESPONSE" ]]; then
            echo "✅ Decryption successful!"
            echo "Decrypted response:"
            echo "$DECRYPTED_RESPONSE"
            echo ""
            echo "🎉 Complete Swagger PGP workflow successful!"
        else
            echo "❌ Decryption failed!"
        fi
    else
        echo "❌ Name enquiry failed!"
        echo "Response: $ENCRYPTED_RESPONSE"
    fi
else
    echo "❌ Encryption failed!"
    echo "Response: $ENCRYPTED_XML"
fi

echo ""
echo "📋 Swagger Workflow Summary:"
echo "============================"
echo "1. ✅ Use /api/pgptest/encrypt to encrypt your XML"
echo "2. ✅ Copy encrypted result to /api/BelemaEasyTransfer/name-enquiry or /api/BelemaEasyTransfer/transfer"
echo "3. ✅ Copy encrypted response to /api/pgptest/decrypt to view the result"
echo ""
echo "🌐 Open Swagger UI: http://localhost:5050/api-docs"
echo "📖 All endpoints now have detailed documentation and examples!"
